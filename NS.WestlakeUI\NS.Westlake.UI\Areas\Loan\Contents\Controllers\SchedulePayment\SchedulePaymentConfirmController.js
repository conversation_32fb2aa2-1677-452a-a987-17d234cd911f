angular
    .module('loanManagement.schedulePaymentConfirm', [])
    //Add by NH-4124 start
    .service('UriSvc', function () {
        this.convertDataURIToBinary = function (dataURI) {
            var BASE64_MARKER = ';base64,',
                base64Index = dataURI.indexOf(BASE64_MARKER) + BASE64_MARKER.length,
                base64 = dataURI.substring(base64Index),
                raw = window.atob(base64),
                rawLength = raw.length,
                array = new Uint8Array(new ArrayBuffer(rawLength));

            for (var i = 0; i < rawLength; i++) {
                array[i] = raw.charCodeAt(i);
            }
            return array;
        }
    })
    //Add by NH-4124 end

    .controller('SchedulePaymentConfirmController', ['$scope', '$http', 'LoanHttpConfig', 'TitleMessage', 'TitleHttpConfig', 'UriSvc', 'FormAlert', 'DTConfig', '$state', 'RequestHandler', '$filter', 'ngDialog', 'NgTablePara', 'LoadingMask', 'Notify', 'LoanSvc', 'NgTableParams', '$stateParams', 'SweetAlert', 'SchedulePaymentSvc', 'LoanCAMMessage',
        function ($scope, $http, LoanHttpConfig, TitleMessage, TitleHttpConfig, UriSvc, FormAlert, DTConfig, $state, RequestHandler, $filter, ngDialog, NgTablePara, LoadingMask, Notify, LoanSvc, NgTableParams, $stateParams, SweetAlert, SchedulePaymentSvc, LoanCAMMessage) {
        SchedulePaymentSvc.tempNumber = 0;

        $scope.getOpt = function (ScheduleDate, ScheduleDateEnd) {
            var opt = {
                icons: {
                    next: 'glyphicon glyphicon-arrow-right',
                    previous: 'glyphicon glyphicon-arrow-left',
                    up: 'glyphicon glyphicon-arrow-up',
                    down: 'glyphicon glyphicon-arrow-down'
                },
                viewMode: 'days',
                format: 'MM/DD/YYYY',
                ignoreReadonly: true,
                defaultDate: $filter('date')(ScheduleDate, 'MM/dd/yyyy'),
                minDate: $filter('date')(ScheduleDate, 'MM/dd/yyyy'),
                maxDate: $filter('date')(ScheduleDateEnd, 'MM/dd/yyyy')
            };
            return opt;
        }

        $scope.getOptReleaseDate = function (ScheduleDate, ScheduleDateEnd) {
            var optRelease = {
                icons: {
                    next: 'glyphicon glyphicon-arrow-right',
                    previous: 'glyphicon glyphicon-arrow-left',
                    up: 'glyphicon glyphicon-arrow-up',
                    down: 'glyphicon glyphicon-arrow-down'
                },
                viewMode: 'days',
                format: 'MM/DD/YYYY',
                ignoreReadonly: true,
                defaultDate: $filter('date')(ScheduleDate, 'MM/dd/yyyy'),
                minDate: $filter('date')(ScheduleDate, 'MM/dd/yyyy'),
                maxDate: $filter('date')(ScheduleDateEnd, 'MM/dd/yyyy')
            };
            return optRelease;
        }
        if (SchedulePaymentSvc.allPaymentList == null) {
            BackToPage();
            return;
        }
        var selectData = SchedulePaymentSvc.allPaymentList.selectData;
        var selectDealerFee = SchedulePaymentSvc.allPaymentList.selectDealerFee;

        //var selectData = angular.fromJson($stateParams.data).selectData;
        //var selectDealerFee = angular.fromJson($stateParams.data).selectDealerFee;
        $scope.NonePostageFee = false;
        $scope.opt = {
            icons: {
                next: 'glyphicon glyphicon-arrow-right',
                previous: 'glyphicon glyphicon-arrow-left',
                up: 'glyphicon glyphicon-arrow-up',
                down: 'glyphicon glyphicon-arrow-down'
            },
            viewMode: 'days',
            format: 'MM/DD/YYYY',
            showClear: true,
            useCurrent: false,
            useStrict: true
        };

        var data = { selectData: selectData, selectDealerFee: selectDealerFee };
        LoadingMask.show();
        $scope.formdata = new Object();
        $http(angular.extend({ data: data }, LoanHttpConfig.SchedulePaymentConfirm.getpayinfo))
              .success(function (d) {
                  LoadingMask.hide();
                  if (d.status != undefined && d.status.indexOf("danger") != -1) {
                      SweetAlert.swal({
                          title: '',
                          text: d.message,
                          type: 'error',
                          showCancelButton: false,
                          confirmButtonColor: '#DD6B55',
                          confirmButtonText: 'OK',
                          closeOnConfirm: true
                      }, function () {
                          BackToPage();
                      });
                  }
                  $scope.formdata = d.results;
                  $scope.postageFee = d.postageFee;
                  $scope.stateList = d.stateList;
                  //add by NH-3938 start
                  $scope.holdTypeList = d.releaseHoldTypeList;
                  $scope.uccProviderList = d.uccProviderList;
                  $scope.holdSwitch = d.releaseHoldSwitch;
                  //add by NH-3938 end

                  //select default post address
                  for (var i = 0; i < $scope.formdata.length; i++) {
                      var DtoList = $scope.formdata[i].DtoList;
                      if (DtoList != null && DtoList != undefined) {
                          for (var j = 0; j < DtoList.length; j++) {
                              //NH-1947
                              if (DtoList[j].IsPayOff == true && !DtoList[j].IsPartialPayment) {
                                  // NH-2428
                                  // ----------------- Edit Start -----------------
                                  if (DtoList[j].ContactSwitch) {
                                      $scope.formdata[i].DtoList[j].NewLocationInfo = DtoList[j].NewLocationDto;
                                      $scope.formdata[i].DtoList[j].NewContactInfo = DtoList[j].NewContactDto;
                                  } else {
                                      $scope.formdata[i].DtoList[j].ContactInfo = DtoList[j].ContactDtoList[0];
                                  }
                                  // ----------------- Edit  End  -----------------
                                  $scope.formdata[i].DtoList[j].MailFeeInfo = $scope.postageFee[0];
                              }

                              var releaseDate = moment($scope.formdata[i].DtoList[j].ScheduleDate).add($scope.formdata[i].DtoList[j].DelayDays, 'days').format("MM/DD/YYYY");
                              $scope.formdata[i].DtoList[j].TitleReleaseDate = releaseDate;

                              //add by NH-3938 start
                              $scope.formdata[i].DtoList[j].uccProviderList = $scope.uccProviderList; 
                              if ($scope.holdSwitch && $scope.formdata[i].DtoList[j].IsHold) {
                                  const holdTypes = $scope.holdTypeList.filter(c => c.Value == "H");
                                  if (holdTypes.length > 0) {
                                      $scope.formdata[i].DtoList[j].HoldType = holdTypes[0];
                                  } 
                              }
                              //add by NH-3938 end
                          }
                      }
                  }

                  for (var i = 0; i < $scope.formdata.length; i++) {
                      if ($scope.formdata[i].paymentSource != null && $scope.formdata[i].paymentSource != undefined
                          && $scope.formdata[i].bankAccountList != null && $scope.formdata[i].bankAccountList != undefined) {
                          for (var j = 0; j < $scope.formdata[i].bankAccountList.length; j++) {
                              if ($scope.formdata[i].paymentSource.BankAccountId == $scope.formdata[i].bankAccountList[j].BankAccountDtoId) {
                                  $scope.formdata[i].bankAccount = $scope.formdata[i].bankAccountList[j];
                                  break;
                              }
                          }
                          //if($scope.formdata[i].paymentSource.BankAccountId == )BankAccountDtoId

                      }
                  }
                  LoadingMask.hide();
              });

            //Add by NH-4124 start
            $scope.ViewTitle = function (title) {
                LoadingMask.show();

                $http(angular.extend({ data: { path: title.FileManagementUrl } }, TitleHttpConfig.Title.EditPdfFile)).success(function (resp) {
                    if (resp.status == 'success') {

                        if (navigator.userAgent.indexOf("Trident") > -1) {
                            // download file if using IE
                            var titlefilename = 'Title' + title.AccountNumber + '.pdf';
                            saveAs(new Blob([UriSvc.convertDataURIToBinary(resp.weburl)], { type: 'application/pdf' }), titlefilename);
                        } else {
                            // show PDF if not using IE
                            $scope.pdf = {
                                src: resp.weburl
                            }
                            ngDialog.open({
                                template: 'pdfTemplate',
                                appendClassName: 'WST_Dialog pdf_Dialog',
                                showClose: false,
                                width: '100%',
                                height: '100%',
                                scope: $scope
                            });
                        }

                    } else {
                        SweetAlert.swal(
                            {
                                title: 'Alert',
                                text: TitleMessage.Title.NoTitleFile,
                                type: 'warning',
                                showCancelButton: false,
                                confirmButtonColor: '#DD6B55',
                                confirmButtonText: 'OK',
                                closeOnConfirm: true
                            },
                            function (confirm) {
                            }
                        );
                    }
                })
                    .finally(function () {
                        LoadingMask.hide();
                    })
            };
            //Add by NH-4124 end

        $scope.pay = function () {
            var isContinue = true;
            var isOnlyPrincipalSave = true;
            angular.forEach($scope.formdata, function (data, index) {
                if (data.bankAccount == null || data.bankAccount == undefined) {
                    SweetAlert.swal({
                        title: '',
                        text: data.DBA + "'s Bank Account is required, please edit",
                        type: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#DD6B55',
                        confirmButtonText: 'OK',
                        closeOnConfirm: true
                    }, function () {
                    });
                    isContinue = false;
                }


                
                //2018-10-17 sunhongyang NH-1504
                var isSave = true;
                var isSaveL = true;
                if (data.DtoList != null && data.DtoList != undefined) {
                    for (var i = 0; i < data.DtoList.length; i++) {
                        // NH-2428
                        // ----------------- Edit Start -----------------
                        if (data.DtoList[i].ContactSwitch) {
                            if (data.DtoList[i].IsPayOff && !data.DtoList[i].IsPartialPayment && !(data.DtoList[i].DisplayMail && data.DtoList[i].IsTrusted)) {

                                //Mod by NH-3938 start
                                if ($scope.holdSwitch) {

                                    //Special Title Handling check on
                                    if (data.DtoList[i].IsHold) {
                                        if (data.DtoList[i].HoldType == undefined || data.DtoList[i].HoldType == null || data.DtoList[i].HoldType.length == 0) {
                                            SweetAlert.swal({
                                                title: '',
                                                text: LoanCAMMessage.LoanManage.TitleHoldType,
                                                type: 'error',
                                                showCancelButton: false,
                                                confirmButtonColor: '#DD6B55',
                                                confirmButtonText: 'OK',
                                                closeOnConfirm: true
                                            }, function () {
                                            });
                                            isContinue = false;
                                        }
                                        else if (data.DtoList[i].HoldType.Value == "T" && (data.DtoList[i].HoldContactInfo == undefined || data.DtoList[i].HoldContactInfo == null || data.DtoList[i].HoldContactInfo.length == 0)) {
                                            SweetAlert.swal({
                                                title: '',
                                                text: LoanCAMMessage.LoanManage.ShippingMessage,
                                                type: 'error',
                                                showCancelButton: false,
                                                confirmButtonColor: '#DD6B55',
                                                confirmButtonText: 'OK',
                                                closeOnConfirm: true
                                            }, function () {
                                            });
                                            isContinue = false;
                                        }
                                        else if (data.DtoList[i].HoldType.Value == "D" || data.DtoList[i].HoldType.Value == "H") {
                                            if (data.DtoList[i].NewContactInfo == undefined || data.DtoList[i].NewContactInfo == null || data.DtoList[i].NewContactInfo.length == 0) {
                                                isSave = false;
                                            } else if (data.DtoList[i].NewLocationInfo == undefined || data.DtoList[i].NewLocationInfo == null || data.DtoList[i].NewLocationInfo.length == 0) {
                                                isSaveL = false;
                                            }
                                        }
                                    }
                                    else {
                                        //Special Title Handling check off
                                        if (data.DtoList[i].NewContactInfo == undefined || data.DtoList[i].NewContactInfo == null || data.DtoList[i].NewContactInfo.length == 0) {
                                            isSave = false;
                                        } else if (data.DtoList[i].NewLocationInfo == undefined || data.DtoList[i].NewLocationInfo == null || data.DtoList[i].NewLocationInfo.length == 0) {
                                            isSaveL = false;
                                        }
                                    }
                                }
                                //switch off
                                else {
                                    if (data.DtoList[i].NewContactInfo == undefined || data.DtoList[i].NewContactInfo == null || data.DtoList[i].NewContactInfo.length == 0) {
                                        isSave = false;
                                    } else if (data.DtoList[i].NewLocationInfo == undefined || data.DtoList[i].NewLocationInfo == null || data.DtoList[i].NewLocationInfo.length == 0) {
                                        isSaveL = false;
                                    }
                                }
                               //Mod by NH-3938 end
                            }

                        } else {
                            //NH-1947
                            if (data.DtoList[i].IsPayOff && !data.DtoList[i].IsPartialPayment && (data.DtoList[i].ContactInfo == undefined || data.DtoList[i].ContactInfo == null || data.DtoList[i].ContactInfo.length == 0)) {
                                isSave = false;
                            }
                        }
                        // ----------------- Edit  End  -----------------
                       
                        //if (data.DtoList[i].OnlyPrincialAmount == 0 && data.DtoList[i].IsOnlyPrincial == true)
                        //{
                        //    SweetAlert.swal({
                        //        title: 'Alert',
                        //        text: 'Only Princial amount should be greater than 0 !',
                        //        type: 'warning',
                        //        showCancelButton: false,
                        //        confirmButtonColor: '#DD6B55',
                        //        confirmButtonText: 'OK',
                        //        closeOnConfirm: true
                        //    })
                        //    isOnlyPrincipalSave = false;
                            
                        //}


                       
                         if (data.DtoList[i].OnlyPrincialAmount == 0 && data.DtoList[i].IsOnlyPrincial == true) {
                            SweetAlert.swal({
                                title: 'Alert',
                                text: 'Only Princial amount should be greater than 0 !',
                                type: 'warning',
                                showCancelButton: false,
                                confirmButtonColor: '#DD6B55',
                                confirmButtonText: 'OK',
                                closeOnConfirm: true
                            })
                            isOnlyPrincipalSave = false;

                        }
                           
                    }
                }

              

                if (!isSave) {
                    isContinue = false;
                    SweetAlert.swal({
                        title: '',
                        text: LoanCAMMessage.LoanManage.ShippingMessage,
                        type: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#DD6B55',
                        confirmButtonText: 'OK',
                        closeOnConfirm: true
                    }, function () {
                    });
                } else if (!isSaveL) {
                    isContinue = false;
                    SweetAlert.swal({
                        title: '',
                        text: LoanCAMMessage.LoanManage.ShippingMessageL,
                        type: 'error',
                        showCancelButton: false,
                        confirmButtonColor: '#DD6B55',
                        confirmButtonText: 'OK',
                        closeOnConfirm: true
                    }, function () {
                    });

                }
            });


            if (!isOnlyPrincipalSave) {
                return false;
            }

            if (!isContinue)
                return false;

            LoadingMask.show();
            $http(angular.extend({ data: $scope.formdata }, LoanHttpConfig.SchedulePaymentConfirm.editmakepayment))
              .success(function (d) {
                  LoadingMask.hide();
                  if (d.status.indexOf("success") != -1) {
                      SweetAlert.swal({
                          title: '',
                          text: d.results,
                          type: 'success',
                          showCancelButton: false,
                          confirmButtonColor: '#DD6B55',
                          confirmButtonText: 'OK',
                          closeOnConfirm: true
                      }, function (confirm) {
                          $scope.FormOverview.$setPristine();
                          BackToPage();
                      });
                  }
                      //NH-1428
                  else if (d.status.indexOf("warning") != -1) {
                      SweetAlert.swal({
                          title: 'Alert',
                          text: d.results,
                          type: 'warning',
                          showCancelButton: false,
                          confirmButtonColor: '#DD6B55',
                          confirmButtonText: 'OK',
                          closeOnConfirm: true
                      })
                  }
                  else {
                      SweetAlert.swal({
                          title: '',
                          text: d.results,
                          type: 'error',
                          showCancelButton: false,
                          confirmButtonColor: '#DD6B55',
                          confirmButtonText: 'OK',
                          closeOnConfirm: true
                      })
                  }
              });
        };

        $scope.Cancel = function () {
            $scope.FormOverview.$dirty = true;
            BackToPage();

        };
        $scope.inputOtherAmount = function (item) {
            if (item.IsPayOff == true)
                return false;
            $scope.scopeitem = item;
            $scope.scopeitem.TempOtherAmount = $scope.scopeitem.OtherAmount;

            var dialog = ngDialog.open({
                template: 'OtherAmountDialog',
                appendClassName: 'WST_Dialog',
                closeByEscape: false,
                closeByDocument: false,
                showClose: false,
                width: '30%',
                scope: $scope
            });
        }
        $scope.confirmOtherAmount = function () {
            $scope.scopeitem.OtherAmount = $("#OtherAmount").val();

            if ($scope.scopeitem.OtherAmount != 0)
                $scope.scopeitem.ButtonName = "OtherAmount";
            else
                $scope.scopeitem.ButtonName = "Curtailment";
            ngDialog.close();
        }

        $scope.getTotal = function () {
            var total = 0.0;
            if ($scope.formdata != null) {
                for (var i = 0; i < $scope.formdata.length; i++) {
                    if ($scope.formdata[i].DealerLevelFeeList != null) {
                        for (var j = 0; j < $scope.formdata[i].DealerLevelFeeList.length; j++) {
                            if (isNaN($scope.formdata[i].DealerLevelFeeList[j].RemainingAmount) == false) {
                                total += $scope.formdata[i].DealerLevelFeeList[j].RemainingAmount;
                            }
                        }
                    }
                    if ($scope.formdata[i].DtoList != null) {
                        for (var j = 0; j < $scope.formdata[i].DtoList.length; j++) {
                            if (isNaN($scope.formdata[i].DtoList[j].TotalMoney) == false) {
                                total += $scope.formdata[i].DtoList[j].TotalMoney;
                            }
                            if (isNaN($scope.formdata[i].DtoList[j].OtherAmount) == false) {
                                total += parseFloat($scope.formdata[i].DtoList[j].OtherAmount);
                            }
                        }
                    }

                }
            }
            return total;
        }

        $scope.getSubTotal = function (dealerInfo) {
            var total = 0;

            if (dealerInfo != null) {
                if (dealerInfo.DealerLevelFeeList != null) {
                    for (var j = 0; j < dealerInfo.DealerLevelFeeList.length; j++) {
                        total += dealerInfo.DealerLevelFeeList[j].RemainingAmount;
                    }
                }
                if (dealerInfo.DtoList != null)
                    for (var j = 0; j < dealerInfo.DtoList.length; j++) {
                        total += dealerInfo.DtoList[j].TotalMoney;
                        total += parseFloat(dealerInfo.DtoList[j].OtherAmount);
                    }
            }

            return total;
        }

        $scope.isShowEstimation = false;

        $scope.scheduleDateChange = function (item) {
            var releaseDate = moment(item.ScheduleDate).add(item.DelayDays, 'days').format("MM/DD/YYYY");
            item.TitleReleaseDate = releaseDate;
            var now = $filter('date')(item.CurrentDate, 'MM/dd/yyyy');
            var diffDays = DateDiff(item.ScheduleDate, now);
            if (diffDays > 0)
                $scope.isShowEstimation = true;
            else
                $scope.isShowEstimation = false;

            var diffInterest = diffDays * item.InterestDaily;
            var diffInsurance = diffDays * item.InsuranceDaily;
            item.InterestPrice = item.InterestPriceTemp + diffInterest;
            item.InsurancePrice = item.InsurancePriceTemp + diffInsurance;

            item.TotalMoney = item.TempTotalMoney + diffInterest + diffInsurance;
        }

        $scope.onlyPrincialAmountChange = function (item) {

            //item.TotalMoney = item.PayOffPrincipal;
            if ((parseFloat(item.TotalMoney) > parseFloat(item.PayOffPrincipal))) {
                if ((parseFloat(item.OnlyPrincialAmount) > parseFloat(item.PayOffPrincipal)) || item.OnlyPrincialAmount <=0) {
                    SweetAlert.swal({
                        title: 'Alert',
                        text: 'Princial amount should not be greater than payoff princial amount and greater than 0!',
                        type: 'warning',
                        showCancelButton: false,
                        confirmButtonColor: '#DD6B55',
                        confirmButtonText: 'OK',
                        closeOnConfirm: true
                    })

                    item.OnlyPrincialAmount = 0;
                }
            }
            else
            {
                if ((parseFloat(item.OnlyPrincialAmount) >= parseFloat(item.PayOffPrincipal)) || item.OnlyPrincialAmount <=0) {
                    SweetAlert.swal({
                        title: 'Alert',
                        text: 'Princial amount should be less than payoff princial amount and greater than 0!',
                        type: 'warning',
                        showCancelButton: false,
                        confirmButtonColor: '#DD6B55',
                        confirmButtonText: 'OK',
                        closeOnConfirm: true
                    })

                    item.OnlyPrincialAmount = 0;
                }
            }
          

        }
        function DateDiff(sDate1, sDate2) {    //sDate1 sDate2 2006-12-18 
            var aDate, oDate1, oDate2, iDays
            aDate = sDate1.split("/")
            //oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])    // 12-18-2006  
            oDate1 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2])    //12-18-2006 
            aDate = sDate2.split("/")
            //oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])
            oDate2 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2])
            var ss = Math.abs(oDate1 - oDate2);
            iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24)    //Converts the number of milliseconds to days  
            return iDays
        }

        //If ScheduleDate changes, ReleaseDate also changes
        $scope.releaseDate = function (item) {
            var releaseDate = moment(item.ScheduleDate).add(item.DelayDays, 'days').format("MM/DD/YYYY");
            item.TitleReleaseDate = releaseDate;
            return releaseDate;
        }

        //Set the other Amount input format and enter the maximum amount
        $scope.getInputMaskOpt = function (loan) {
            var option = {
                "autoUnmask": true,
                "rightAlign": false,
                "allowPlus": false,
                "allowMinus": false,
                "jitMasking": true,
                "max": loan.OtherAmountLimit
            }
            return option;
        }

        $scope.getInputMaskOptForPrincipal = function () {
            var option = {
                "autoUnmask": true,
                "rightAlign": false,
                "allowPlus": false,
                "allowMinus": false,
                "jitMasking": true
            }
            return option;
        }

        $scope.removeFee = function (parentIndex, index) {
            $scope.formdata[parentIndex].DealerLevelFeeList.splice(index, 1);
        }

        $scope.removePayoff = function (parentIndex, index) {
            $scope.formdata[parentIndex].DtoList.splice(index, 1);
        }

        //If all the Payment is deleted on the Check page, return 0. The Confirm button is disabled at the foreground
        $scope.getPaymentLength = function () {
            var totalLength = 0;
            for (var i = 0; i < $scope.formdata.length; i++) {
                if ($scope.formdata[i].DealerLevelFeeList != undefined)
                    totalLength += $scope.formdata[i].DealerLevelFeeList.length;

                if ($scope.formdata[i].DtoList != undefined)
                    totalLength += $scope.formdata[i].DtoList.length;
            }

            return totalLength;
        }
        $scope.AddContactDialog = function (item, DtoList) {
            $scope.flag = "Add";
            var dialog = ngDialog.open({
                template: 'AddContactDialog',
                appendClassName: 'WST_Dialog',
                closeByEscape: false,
                closeByDocument: false,
                showClose: false,
                width: '45%',
                controller: 'EditDialogContactController',
                scope: $scope

            });
            dialog.closePromise.then(function (data) {
                if (SchedulePaymentSvc.tempContact == null || SchedulePaymentSvc.tempContact == undefined) {

                    --SchedulePaymentSvc.tempNumber;
                    return;
                }
                var tempContect = new Object();
                angular.copy(item.ContactDtoList[0], tempContect);

                var newTempContect = $scope.setTempContactInfo(tempContect, SchedulePaymentSvc.tempContact);

                item.ContactInfo = newTempContect;
                for (var i = 0; i < $scope.formdata.length; i++) {
                    if ($scope.formdata[i].DealerId == item.DealerId) {
                        if ($scope.formdata[i].DtoList != undefined) {
                            for (var j = 0; j < $scope.formdata[i].DtoList.length; j++) {
                                if ($scope.formdata[i].DtoList[j].ContactDtoList != null && $scope.formdata[i].DtoList[j].ContactDtoList != undefined) {
                                    $scope.formdata[i].DtoList[j].ContactDtoList.push(newTempContect);
                                    //$scope.formdata[i].DtoList[j].ContactInfo = newTempContect;
                                    if ($scope.formdata[i].DtoList[j].ContactInfo.ContactReference.indexOf('Temporary_Contact_') != -1)
                                        $scope.formdata[i].DtoList[j].isDisabledEdit = true;
                                }
                            }
                        }
                    }
                }
                SchedulePaymentSvc.tempContact = null;
            });
        }

        $scope.EditContactDialog = function (ContactInfo, item) {
            $scope.flag = "Edit";
            $scope.TempEditContact = ContactInfo;
            var dialog = ngDialog.open({
                template: 'AddContactDialog',
                appendClassName: 'WST_Dialog',
                closeByEscape: false,
                closeByDocument: false,
                showClose: false,
                width: '45%',
                controller: 'EditDialogContactController',
                scope: $scope

            });
            dialog.closePromise.then(function (data) {
                if (SchedulePaymentSvc.tempContact == null || SchedulePaymentSvc.tempContact == undefined)
                    return;

                //When you modify TempContact, write to ContactList and modify ContactInfo
                for (var i = 0; i < $scope.formdata.length; i++) {
                    if ($scope.formdata[i].DealerId == item.DealerId) {
                        if ($scope.formdata[i].DtoList != undefined) {
                            for (var j = 0; j < $scope.formdata[i].DtoList.length; j++) {
                                if ($scope.formdata[i].DtoList[j].ContactDtoList != null && $scope.formdata[i].DtoList[j].ContactDtoList != undefined) {
                                    //If the Dealer modified TempContact is also selected by other Loans, then the other Lo's TempContact information also changes
                                    if ($scope.formdata[i].DtoList[j].ContactInfo.ContactReference == SchedulePaymentSvc.tempContact.ContactReference)
                                        $scope.formdata[i].DtoList[j].ContactInfo = SchedulePaymentSvc.tempContact;
                                    //Gets the Index of ContactInfo in ContactList
                                    var index = $scope.getDrpoDownIndex($scope.formdata[i].DtoList[j].ContactDtoList, ContactInfo);
                                    delete $scope.formdata[i].DtoList[j].ContactDtoList[index];
                                    $scope.formdata[i].DtoList[j].ContactDtoList.push(SchedulePaymentSvc.tempContact);
                                }
                            }
                        }
                    }
                }
                //item.ContactInfo = SchedulePaymentSvc.tempContact;
                SchedulePaymentSvc.tempContact = null;
            });
        }
        $scope.setTempContactInfo = function (tempContect, addTempContect) {
            tempContect.ContactReference = addTempContect.ContactReference;
            tempContect.FirstName = addTempContect.FirstName;
            tempContect.LastName = addTempContect.LastName;
            tempContect.City = addTempContect.City;
            tempContect.ZipCode = addTempContect.ZipCode;
            tempContect.AddressLine1 = addTempContect.AddressLine1;
            tempContect.State = addTempContect.StateSelect.Text;
            tempContect.Phone = addTempContect.Phone;
            tempContect.StateSelect = addTempContect.StateSelect;
            if (addTempContect.Email == undefined)
                tempContect.Email = "";
            else
                tempContect.Email = addTempContect.Email;

            if (addTempContect.AddressLine2 == undefined)
                tempContect.AddressLine2 = "";
            else
                tempContect.AddressLine2 = addTempContect.AddressLine2;

            return tempContect;
        }

        $scope.shippingContactChange = function (item) {
            if (item.ContactInfo.ContactReference.indexOf('Temporary_Contact_') != -1)
                item.isDisabledEdit = true;
            else
                item.isDisabledEdit = false;

        }

        $scope.getDrpoDownIndex = function (ContactList, ContactInfo) {
            for (var i = 0; i < ContactList.length; i++) {
                if (ContactList[i] == undefined || ContactList[i] == null)
                    continue;
                if (ContactList[i].ContactReference == ContactInfo.ContactReference)
                    return i;
            }
        }

        function BackToPage() {
            var data = angular.fromJson($stateParams.data);

            if (data.FromUrl == 'LoanSchedulePayment') {
                $state.go('app.schedulePayment');
            }
            else if (data.FromUrl == 'Dealer') {
                //delete by NH-3025 start
                //$http.post(LoanHttpConfig.SchedulePaymentConfirm.DeleteLoanManagementSession.url)
                //      .success(function (data, status) {

                //      });
                //delete by NH-3025 end
                $state.go('app.create-dealer', { data: data.DealerJson });
            }
            else {
                $state.go('app.schedulePayment');
            }
        }

    }])


.controller('EditDialogContactController', ['$scope', '$http', '$filter', 'RequestHandler', 'ngDialog', 'LoadingMask', 'SaveBtnValidator', 'SchedulePaymentSvc',
         function ($scope, $http, $filter, RequestHandler, ngDialog, LoadingMask, SaveBtnValidator, SchedulePaymentSvc) {
             //init
             $scope.formValidate;
             $scope.submitted = false;
             $scope.checkZipCodeResult = true;
             $scope.checkExists = true;
             $scope.editContact = new Object();

             if ($scope.flag == "Add") {
                 $scope.editContact.ContactReference = "Temporary_Contact_" + ++SchedulePaymentSvc.tempNumber;
             }
             else {
                 angular.copy($scope.TempEditContact, $scope.editContact);
                 //$scope.editContact.StateSelect = $scope.editContact.StateSelect;
             }

             $scope.CheckZipCode = function () {
                 $scope.CheckZipCodeMessage = '';
                 $scope.checkZipCodeResult = true;
             }


             //submit
             $scope.submitForm = function () {
                 $scope.submitted = true;


                 if (!($scope.checkZipCodeResult && $scope.checkExists)) {
                     $scope.formValidate.$invalid = true;
                 }

                 SaveBtnValidator.validate($scope.formValidate, function () {
                     $scope.TempEditContact = new Object();
                     angular.copy($scope.editContact, $scope.TempEditContact);
                     SchedulePaymentSvc.tempContact = $scope.TempEditContact;
                     //$scope.saveContact();
                     ngDialog.close();
                 }, null);

             };

         }])