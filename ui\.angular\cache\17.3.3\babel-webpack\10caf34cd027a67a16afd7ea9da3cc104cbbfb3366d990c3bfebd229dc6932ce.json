{"ast": null, "code": "import moment from 'moment';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/checkbox\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtextarea\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/inputnumber\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"primeng/inputgroup\";\nimport * as i16 from \"primeng/inputgroupaddon\";\nimport * as i17 from \"primeng/ripple\";\nconst _c0 = () => ({\n  width: \"30vw\"\n});\nconst _c1 = () => ({\n  width: \"45vw\"\n});\nfunction LoanProceedComponent_div_10_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵtextInterpolate2(\" \", item_r3.bankAccountRanking, \".\", item_r3.reference, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bankAccountRanking, \".\", item_r4.reference, \"\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"i\", 50);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_1_Template_i_click_2_listener() {\n      const feeItem_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const dealer_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.removeFee(dealer_r2, feeItem_r7));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"div\", 52)(5, \"div\", 37);\n    i0.ɵɵtext(6, \"Fee Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"div\", 37);\n    i0.ɵɵtext(11, \"Pay Amount:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 55);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const feeItem_r7 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(feeItem_r7.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 2, feeItem_r7.remainingAmount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_i_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 74);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_i_23_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.showPayoff());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_i_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 75);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_i_24_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.showPayoff());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fee_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(fee_r12.feeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, fee_r12.amount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_29_i_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 81);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_29_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 81);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"div\", 10)(3, \"div\");\n    i0.ɵɵtext(4, \"Principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, LoanProceedComponent_div_10_div_26_div_2_div_29_div_8_Template, 6, 4, \"div\", 78);\n    i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\");\n    i0.ɵɵtext(11, \"Interest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 79);\n    i0.ɵɵtemplate(13, LoanProceedComponent_div_10_div_26_div_2_div_29_i_13_Template, 1, 0, \"i\", 80);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 10)(17, \"div\");\n    i0.ɵɵtext(18, \"WIP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 79);\n    i0.ɵɵtemplate(20, LoanProceedComponent_div_10_div_26_div_2_div_29_i_20_Template, 1, 0, \"i\", 80);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 6, item_r9.principal));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r9.extraAmountList);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isShowEstimation);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 8, item_r9.interestPrice), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isShowEstimation);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 10, item_r9.insurancePrice), \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 63);\n    i0.ɵɵtext(2, \"Additional Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 82);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_div_30_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.inputOtherAmount(item_r9));\n    });\n    i0.ɵɵtext(8, \"Change\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 1, item_r9.otherAmount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 83)(2, \"div\", 69);\n    i0.ɵɵtext(3, \"Principal Only\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-checkbox\", 84);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_36_Template_p_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.isOnlyPrincial, $event) || (item_r9.isOnlyPrincial = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.isOnlyPrincial);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 10)(2, \"div\", 69);\n    i0.ɵɵtext(3, \"Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 69)(5, \"p-inputGroup\", 86)(6, \"p-inputGroupAddon\");\n    i0.ɵɵtext(7, \"$\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-inputNumber\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_37_Template_p_inputNumber_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.onlyPrincialAmount, $event) || (item_r9.onlyPrincialAmount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_37_Template_p_inputNumber_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onlyPrincipalAmountChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\", 69);\n    i0.ɵɵtext(11, \"Schedule Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 69)(13, \"p-calendar\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_37_Template_p_calendar_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.scheduleDate, $event) || (item_r9.scheduleDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"minFractionDigits\", 2)(\"maxFractionDigits\", 2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.onlyPrincialAmount);\n    i0.ɵɵproperty(\"maxlength\", 14);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.scheduleDate);\n    i0.ɵɵproperty(\"disabled\", true)(\"selectOtherMonths\", true)(\"showButtonBar\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"dateFormat\", \"mm/dd/yy\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"p-checkbox\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_6_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.isHold, $event) || (item_r9.isHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.isHold);\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", item_r9.isOnlyPrincial);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p-calendar\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_4_Template_p_calendar_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const item_r9 = i0.ɵɵnextContext(4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.titleReleaseDate, $event) || (item_r9.titleReleaseDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.titleReleaseDate);\n    i0.ɵɵproperty(\"showButtonBar\", false)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"readonlyInput\", true)(\"dateFormat\", \"mm/dd/yy\")(\"minDate\", ctx_r4.getReleaseDateMinDate(item_r9))(\"maxDate\", ctx_r4.getReleaseDateMaxDate(item_r9))(\"disabled\", item_r9.isOnlyPrincial);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, item_r9.titleReleaseHoldDate, \"MM/dd/yyyy\"), \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96);\n    i0.ɵɵtext(2, \"Release Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 97);\n    i0.ɵɵtemplate(4, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_4_Template, 2, 9, \"div\", 45)(5, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_5_Template, 3, 4, \"div\", 100);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdType.value === \"T\" || item_r9.holdType.value === \"H\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdType.value === \"D\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96);\n    i0.ɵɵtext(2, \"Shipping Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 97)(4, \"p-dropdown\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_7_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.mailFeeInfo, $event) || (item_r9.mailFeeInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.mailFeeInfo);\n    i0.ɵɵproperty(\"options\", ctx_r4.postageFee)(\"disabled\", item_r9.isOnlyPrincial);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const selected_r22 = ctx.$implicit;\n    i0.ɵɵtextInterpolate2(\" \", selected_r22.uccProviderName, \" - \", selected_r22.address, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const provider_r23 = ctx.$implicit;\n    i0.ɵɵtextInterpolate2(\" \", provider_r23.uccProviderName, \" - \", provider_r23.address, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r9.holdContactInfo.city, \", \", item_r9.holdContactInfo.state, \", \", item_r9.holdContactInfo.zipCode, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 96);\n    i0.ɵɵtext(2, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 97)(4, \"p\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 106);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_p_8_Template, 2, 3, \"p\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", item_r9.holdContactInfo.uccProviderName, \" Title Dept\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.holdContactInfo.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdContactInfo);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵtext(3, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"p-dropdown\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_Template_p_dropdown_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.holdContactInfo, $event) || (item_r9.holdContactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(6, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_6_Template, 1, 2, \"ng-template\", 42)(7, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_7_Template, 1, 2, \"ng-template\", 43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_Template, 9, 3, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.holdContactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.uccProviderList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdContactInfo == null ? null : item_r9.holdContactInfo.uccProviderId);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_p_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r9.newLocationInfo.city, \", \", item_r9.newLocationInfo.state, \", \", item_r9.newLocationInfo.zipCode, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵtext(3, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"p-dropdown\", 108);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newContactInfo, $event) || (item_r9.newContactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 95)(7, \"div\", 96);\n    i0.ɵɵtext(8, \"Shipping Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 97)(10, \"p-dropdown\", 109);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newLocationInfo, $event) || (item_r9.newLocationInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_onChange_10_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 105)(12, \"div\", 96);\n    i0.ɵɵtext(13, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 97)(15, \"p\", 106);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 106);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_p_19_Template, 2, 3, \"p\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newContactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newContactDtoList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newLocationInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newLocationDtoList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.newContactInfo == null ? null : item_r9.newContactInfo.firstName, \" \", item_r9.newContactInfo == null ? null : item_r9.newContactInfo.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.newLocationInfo == null ? null : item_r9.newLocationInfo.address1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.newLocationInfo);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 96);\n    i0.ɵɵtext(2, \"Note\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 97)(4, \"textarea\", 111);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_10_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.note, $event) || (item_r9.note = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(5, \"              \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.note);\n    i0.ɵɵproperty(\"rows\", 3)(\"maxlength\", 256);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_Template, 6, 2, \"div\", 94);\n    i0.ɵɵelementStart(2, \"div\", 95)(3, \"div\", 96);\n    i0.ɵɵtext(4, \"Special Title Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 97)(6, \"p-dropdown\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_Template_p_dropdown_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.holdType, $event) || (item_r9.holdType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_7_Template, 5, 3, \"div\", 94)(8, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_Template, 9, 4, \"div\", 72)(9, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template, 20, 10, \"div\", 72)(10, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_10_Template, 6, 3, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdType);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.holdType);\n    i0.ɵɵproperty(\"options\", ctx_r4.holdTypeList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (item_r9.holdType == null ? null : item_r9.holdType.value) === \"T\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (item_r9.holdType == null ? null : item_r9.holdType.value) === \"D\" || (item_r9.holdType == null ? null : item_r9.holdType.value) === \"H\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.holdType);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵtext(1, \"Hold\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"p-checkbox\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_7_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.isHold, $event) || (item_r9.isHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.isHold);\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", item_r9.isOnlyPrincial);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵtext(3, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 117)(5, \"p-dropdown\", 118);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_p_dropdown_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.contactInfo, $event) || (item_r9.contactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_p_dropdown_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.addContactDialog(item_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.editContactDialog(item_r9.contactInfo, item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 105)(9, \"div\", 96);\n    i0.ɵɵtext(10, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 97)(12, \"p\", 106);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 106);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 106);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.contactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.contactDtoList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !item_r9.isDisabledEdit || item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.contactInfo == null ? null : item_r9.contactInfo.firstName, \" \", item_r9.contactInfo == null ? null : item_r9.contactInfo.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.contactInfo == null ? null : item_r9.contactInfo.addressLine1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r9.contactInfo == null ? null : item_r9.contactInfo.city, \", \", item_r9.contactInfo == null ? null : item_r9.contactInfo.state, \", \", item_r9.contactInfo == null ? null : item_r9.contactInfo.zipCode, \"\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_p_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r9.newLocationInfo.city, \", \", item_r9.newLocationInfo.state, \", \", item_r9.newLocationInfo.zipCode, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵtext(3, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"p-dropdown\", 108);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newContactInfo, $event) || (item_r9.newContactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 95)(7, \"div\", 96);\n    i0.ɵɵtext(8, \"Shipping Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 97)(10, \"p-dropdown\", 109);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newLocationInfo, $event) || (item_r9.newLocationInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_onChange_10_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const item_r9 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 105)(12, \"div\", 96);\n    i0.ɵɵtext(13, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 97)(15, \"p\", 106);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 106);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_p_19_Template, 2, 3, \"p\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newContactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newContactDtoList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newLocationInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newLocationDtoList)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.newContactInfo == null ? null : item_r9.newContactInfo.firstName, \" \", item_r9.newContactInfo == null ? null : item_r9.newContactInfo.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.newLocationInfo == null ? null : item_r9.newLocationInfo.address1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.newLocationInfo);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵtext(3, \"Release Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"p-calendar\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template_p_calendar_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.titleReleaseDate, $event) || (item_r9.titleReleaseDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_6_Template, 2, 0, \"div\", 112)(7, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_7_Template, 2, 3, \"div\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 95)(9, \"div\", 96);\n    i0.ɵɵtext(10, \"Shipping Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 97)(12, \"p-dropdown\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template_p_dropdown_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.mailFeeInfo, $event) || (item_r9.mailFeeInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template, 18, 11, \"div\", 72)(14, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template, 20, 10, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.titleReleaseDate);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"readonlyInput\", true)(\"dateFormat\", \"mm/dd/yy\")(\"minDate\", ctx_r4.getReleaseDateMinDate(item_r9))(\"maxDate\", ctx_r4.getReleaseDateMaxDate(item_r9))(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.holdSwitch);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.holdSwitch);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.mailFeeInfo);\n    i0.ɵɵproperty(\"options\", ctx_r4.postageFee)(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.contactSwitch);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.contactSwitch);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 90);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Title Shipping Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.viewTitle(item_r9));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, LoanProceedComponent_div_10_div_26_div_2_div_38_div_6_Template, 2, 3, \"div\", 92)(7, LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_Template, 11, 8, \"div\", 72)(8, LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template, 15, 16, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !item_r9.isHasTitleFile);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.holdSwitch);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.holdSwitch && item_r9.isHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.isHold || !ctx_r4.holdSwitch);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\");\n    i0.ɵɵtext(2, \"Title Released\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"i\", 50);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_div_2_Template_i_click_2_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const dealer_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.removePayoff(dealer_r2, item_r9));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"div\", 57);\n    i0.ɵɵelement(5, \"i\", 58);\n    i0.ɵɵtext(6, \" Vehicle Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 2);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 2);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 4)(12, \"span\", 59);\n    i0.ɵɵtext(13, \"Due Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 59);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 60)(18, \"div\", 57);\n    i0.ɵɵelement(19, \"i\", 61);\n    i0.ɵɵtext(20, \" Payment Detail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 62)(22, \"div\", 63);\n    i0.ɵɵtemplate(23, LoanProceedComponent_div_10_div_26_div_2_i_23_Template, 1, 0, \"i\", 64)(24, LoanProceedComponent_div_10_div_26_div_2_i_24_Template, 1, 0, \"i\", 65);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, LoanProceedComponent_div_10_div_26_div_2_div_29_Template, 23, 12, \"div\", 66)(30, LoanProceedComponent_div_10_div_26_div_2_div_30_Template, 9, 3, \"div\", 67);\n    i0.ɵɵelementStart(31, \"div\", 10)(32, \"div\", 68);\n    i0.ɵɵtext(33, \"Schedule Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 69)(35, \"p-calendar\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_26_div_2_Template_p_calendar_ngModelChange_35_listener($event) {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.scheduleDate, $event) || (item_r9.scheduleDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onSelect\", function LoanProceedComponent_div_10_div_26_div_2_Template_p_calendar_onSelect_35_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.scheduleDateChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(36, LoanProceedComponent_div_10_div_26_div_2_div_36_Template, 5, 2, \"div\", 71)(37, LoanProceedComponent_div_10_div_26_div_2_div_37_Template, 14, 11, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, LoanProceedComponent_div_10_div_26_div_2_div_38_Template, 9, 4, \"div\", 73)(39, LoanProceedComponent_div_10_div_26_div_2_div_39_Template, 3, 0, \"div\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(item_r9.vin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r9.year, \" \", item_r9.make, \" \", item_r9.model, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 27, item_r9.nextDueDate, \"MM/dd/yy\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r9.buttonName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 30, item_r9.totalMoney));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.isPayOff && !item_r9.isPartialPayment);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.scheduleDate);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"showButtonBar\", false)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"dateFormat\", \"mm/dd/yy\")(\"showTime\", false)(\"showIcon\", false)(\"readonlyInput\", true)(\"minDate\", ctx_r4.getMinDate(item_r9))(\"maxDate\", ctx_r4.getMaxDate(item_r9))(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.buttonName === \"PayOff\" && item_r9.isShowPrincipal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.displayMail && !item_r9.isTrusted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.displayMail && item_r9.isTrusted);\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LoanProceedComponent_div_10_div_26_div_1_Template, 15, 4, \"div\", 47)(2, LoanProceedComponent_div_10_div_26_div_2_Template, 40, 32, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dealer_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", dealer_r2.dealerLevelFeeList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", dealer_r2.dtoList);\n  }\n}\nfunction LoanProceedComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 10)(2, \"div\", 35)(3, \"div\", 36)(4, \"div\", 37);\n    i0.ɵɵtext(5, \"Dealer Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"div\", 37);\n    i0.ɵɵtext(10, \"Dealer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"div\", 37);\n    i0.ɵɵtext(15, \"Subtotal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 39);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 40)(20, \"div\", 37);\n    i0.ɵɵtext(21, \"Bank Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p-dropdown\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_Template_p_dropdown_ngModelChange_22_listener($event) {\n      const dealer_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(dealer_r2.bankAccount, $event) || (dealer_r2.bankAccount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(23, LoanProceedComponent_div_10_ng_template_23_Template, 1, 2, \"ng-template\", 42)(24, LoanProceedComponent_div_10_ng_template_24_Template, 2, 2, \"ng-template\", 43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"img\", 44);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_Template_img_click_25_listener() {\n      const dealer_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleDealer(dealer_r2));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(26, LoanProceedComponent_div_10_div_26_Template, 3, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dealer_r2 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(dealer_r2.dealerCode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dealer_r2.dba);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 10, ctx_r4.getSubTotal(dealer_r2)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", dealer_r2.bankAccount);\n    i0.ɵɵproperty(\"options\", dealer_r2.bankAccountList)(\"optionLabel\", \"reference\")(\"optionValue\", \"bankAccountDtoId\")(\"filter\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", !dealer_r2.isExpanded ? \"./assets/img/upicon.png\" : \"./assets/img/downicon.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dealer_r2.isExpanded);\n  }\n}\nfunction LoanProceedComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelOtherAmount());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_17_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.confirmOtherAmount());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoanProceedComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_57_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelContactDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_57_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.saveContact());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let LoanProceedComponent = /*#__PURE__*/(() => {\n  class LoanProceedComponent {\n    // Toggle dealer panel\n    toggleDealer(dealer) {\n      dealer.isExpanded = !dealer.isExpanded;\n    }\n    constructor(router, route, loanSchedulePaymentService, messageService) {\n      this.router = router;\n      this.route = route;\n      this.loanSchedulePaymentService = loanSchedulePaymentService;\n      this.messageService = messageService;\n      this.payoff = false;\n      this.formdata = [];\n      this.postageFee = [];\n      this.stateList = [];\n      this.holdTypeList = [];\n      this.uccProviderList = [];\n      this.holdSwitch = false;\n      this.isShowEstimation = false;\n      // Additional Payment dialog variables\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n      // Calendar configuration\n      this.calendarConfig = {\n        showButtonBar: true,\n        monthNavigator: true,\n        yearNavigator: true,\n        dateFormat: 'mm/dd/yy',\n        showTime: false,\n        showIcon: false,\n        readonlyInput: true,\n        appendTo: 'body'\n      };\n      // Contact Dialog\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.contactDialogMode = 'add';\n      this.editingContact = null;\n      this.tempContactNumber = 0;\n      // Contact Form\n      this.contactForm = {\n        contactReference: '',\n        firstName: '',\n        lastName: '',\n        addressLine1: '',\n        addressLine2: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        phone: '',\n        email: ''\n      };\n    }\n    ngOnInit() {\n      // 临时使用mock数据替代路由参数\n      const mockData = {\n        selectData: [{\n          \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 351764,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": null,\n          \"soldDisp\": \"\",\n          \"financeTag\": null,\n          \"assetId\": null,\n          \"titleStatus\": \"RE\",\n          \"titleStatusDisplay\": \"Trusted Title Received\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2022,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287165\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 90,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 90,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 0,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 0,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 214,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287165-BMW 320Li 2022\",\n          \"feeAmountNoReserve\": 90,\n          \"reserveFee\": 0,\n          \"extraAmountList\": [{\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPayoff\": [{\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPartial\": [{\n            \"active\": true,\n            \"fixAmount\": 50,\n            \"tempAmount\": 50,\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 0,\n            \"tempAmount\": 0,\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 15,\n            \"tempAmount\": 15,\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        }, {\n          \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 352478,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": \"\",\n          \"soldDisp\": \"\",\n          \"financeTag\": \"\",\n          \"assetId\": null,\n          \"titleStatus\": \"PT\",\n          \"titleStatusDisplay\": \"Pending Release Trusted\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2023,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287243\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 2313,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 2313,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 2000,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 2000,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 312,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287243-BMW 320Li 2023\",\n          \"feeAmountNoReserve\": 65,\n          \"reserveFee\": 248,\n          \"extraAmountList\": [{\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPayoff\": [{\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"RESERVE\",\n            \"feeName\": \"Reserve\",\n            \"createDate\": null,\n            \"amount\": 248,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPartial\": [{\n            \"active\": true,\n            \"fixAmount\": 2000,\n            \"tempAmount\": 2000,\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"active\": false,\n            \"fixAmount\": 0,\n            \"tempAmount\": 0,\n            \"feeId\": \"RESERVE\",\n            \"feeName\": \"Reserve\",\n            \"createDate\": null,\n            \"amount\": 248,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 15,\n            \"tempAmount\": 15,\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        }],\n        \"selectDealerFee\": [{\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\n          \"feeName\": \"Audit Fee\",\n          \"description\": \"Audit Fee\",\n          \"remainingAmount\": 100,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2023-06-20T09:29:05\",\n          \"postpaymentDealerFeeAmount\": 100,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        }, {\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\n          \"feeName\": \"OC Defense Fee_CO\",\n          \"description\": \"OC Defense Fee_CO\",\n          \"remainingAmount\": 123,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2025-05-29T02:44:35\",\n          \"postpaymentDealerFeeAmount\": 123,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        }]\n      };\n      const data = JSON.stringify(mockData);\n      if (!data) {\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'No payment data found'\n        });\n        this.router.navigate(['/loan/schedule-payment']);\n        return;\n      }\n      const paymentData = {\n        selectData: JSON.parse(data).selectData,\n        selectDealerFee: JSON.parse(data).selectDealerFee\n      };\n      this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\n        next: response => {\n          if (response.code === 200) {\n            this.formdata = response.data.results || [];\n            this.postageFee = response.data.postageFee || [];\n            this.stateList = response.data.stateList || [];\n            // add by NH-3938 start\n            this.holdTypeList = response.data.releaseHoldTypeList || [];\n            this.uccProviderList = response.data.uccProviderList || [];\n            this.holdSwitch = response.data.releaseHoldSwitch || false;\n            // add by NH-3938 end\n            // select default post address - First loop (lines 113-145 in AngularJS)\n            for (let i = 0; i < this.formdata.length; i++) {\n              const dtoList = this.formdata[i].dtoList;\n              if (dtoList != null && dtoList != undefined) {\n                for (let j = 0; j < dtoList.length; j++) {\n                  // NH-1947\n                  if (dtoList[j].isPayOff == true && !dtoList[j].isPartialPayment) {\n                    // NH-2428\n                    // ----------------- Edit Start -----------------\n                    if (dtoList[j].contactSwitch) {\n                      this.formdata[i].dtoList[j].newLocationInfo = dtoList[j].newLocationDto;\n                      this.formdata[i].dtoList[j].newContactInfo = dtoList[j].newContactDto;\n                    } else {\n                      this.formdata[i].dtoList[j].contactInfo = dtoList[j].contactDtoList[0];\n                    }\n                    // ----------------- Edit End -----------------\n                    this.formdata[i].dtoList[j].mailFeeInfo = this.postageFee[0];\n                  }\n                  const releaseDate = moment(this.formdata[i].dtoList[j].scheduleDate).add(this.formdata[i].dtoList[j].delayDays, 'days').format(\"MM/DD/YYYY\");\n                  this.formdata[i].dtoList[j].titleReleaseDate = releaseDate;\n                  // add by NH-3938 start\n                  this.formdata[i].dtoList[j].uccProviderList = this.uccProviderList;\n                  if (this.holdSwitch && this.formdata[i].dtoList[j].isHold) {\n                    const holdTypes = this.holdTypeList.filter(c => c.value == \"H\");\n                    if (holdTypes.length > 0) {\n                      this.formdata[i].dtoList[j].holdType = holdTypes[0];\n                    }\n                  }\n                  // add by NH-3938 end\n                }\n              }\n            }\n            // Second loop for bank account matching (lines 147-159 in AngularJS)\n            for (let i = 0; i < this.formdata.length; i++) {\n              if (this.formdata[i].paymentSource != null && this.formdata[i].paymentSource != undefined && this.formdata[i].bankAccountList != null && this.formdata[i].bankAccountList != undefined) {\n                for (let j = 0; j < this.formdata[i].bankAccountList.length; j++) {\n                  if (this.formdata[i].paymentSource.bankAccountId == this.formdata[i].bankAccountList[j].bankAccountDtoId) {\n                    this.formdata[i].bankAccount = this.formdata[i].bankAccountList[j];\n                    break;\n                  }\n                }\n              }\n            }\n            // Convert date fields and set expanded state after processing\n            this.formdata.forEach(dealer => {\n              dealer.isExpanded = true;\n              if (dealer.dtoList) {\n                dealer.dtoList.forEach(dto => {\n                  if (dto.scheduleDate) {\n                    dto.scheduleDate = moment(dto.scheduleDate).toDate();\n                  }\n                  if (dto.scheduleDateEnd) {\n                    dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\n                  }\n                  if (dto.currentDate) {\n                    dto.currentDate = moment(dto.currentDate).toDate();\n                  }\n                });\n              }\n            });\n            // 初始化数据\n            this.formdata.forEach(dealer => {\n              if (dealer.dtoList) {\n                dealer.dtoList.forEach(item => {\n                  this.scheduleDateChange(item);\n                });\n              }\n            });\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.message\n            });\n            this.router.navigate(['/loan/schedule-payment']);\n          }\n        },\n        error: error => {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to get payment info'\n          });\n          console.error('Error getting payment info:', error);\n        }\n      });\n    }\n    showPayoff() {\n      this.payoff = !this.payoff;\n    }\n    getTotal() {\n      let total = 0.0;\n      if (this.formdata != null) {\n        for (let i = 0; i < this.formdata.length; i++) {\n          if (this.formdata[i].dealerLevelFeeList != null) {\n            for (let j = 0; j < this.formdata[i].dealerLevelFeeList.length; j++) {\n              if (isNaN(this.formdata[i].dealerLevelFeeList[j].remainingAmount) == false) {\n                total += this.formdata[i].dealerLevelFeeList[j].remainingAmount;\n              }\n            }\n          }\n          if (this.formdata[i].dtoList != null) {\n            for (let j = 0; j < this.formdata[i].dtoList.length; j++) {\n              if (isNaN(this.formdata[i].dtoList[j].totalMoney) == false) {\n                total += this.formdata[i].dtoList[j].totalMoney;\n              }\n              if (isNaN(this.formdata[i].dtoList[j].otherAmount) == false) {\n                total += parseFloat(this.formdata[i].dtoList[j].otherAmount);\n              }\n            }\n          }\n        }\n      }\n      return total;\n    }\n    getSubTotal(dealerInfo) {\n      let total = 0;\n      if (dealerInfo != null) {\n        if (dealerInfo.dealerLevelFeeList != null) {\n          for (let j = 0; j < dealerInfo.dealerLevelFeeList.length; j++) {\n            total += dealerInfo.dealerLevelFeeList[j].remainingAmount;\n          }\n        }\n        if (dealerInfo.dtoList != null) {\n          for (let j = 0; j < dealerInfo.dtoList.length; j++) {\n            total += dealerInfo.dtoList[j].totalMoney;\n            total += parseFloat(dealerInfo.dtoList[j].otherAmount);\n          }\n        }\n      }\n      return total;\n    }\n    getDatePickerConfig(scheduleDate, scheduleDateEnd) {\n      const config = {\n        ...this.calendarConfig\n      };\n      if (scheduleDate) {\n        config.minDate = new Date(scheduleDate);\n        config.defaultDate = new Date(scheduleDate);\n      }\n      if (scheduleDateEnd) {\n        config.maxDate = new Date(scheduleDateEnd);\n      }\n      return config;\n    }\n    scheduleDateChange(item) {\n      const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');\n      item.titleReleaseDate = releaseDate;\n      const now = moment(item.currentDate).format('MM/dd/yyyy');\n      const diffDays = this.dateDiff(moment(item.scheduleDate).format('MM/DD/YYYY'), now);\n      if (diffDays > 0) {\n        this.isShowEstimation = true;\n      } else {\n        this.isShowEstimation = false;\n      }\n      const diffInterest = diffDays * item.interestDaily;\n      const diffInsurance = diffDays * item.insuranceDaily;\n      item.interestPrice = item.interestPriceTemp + diffInterest;\n      item.insurancePrice = item.insurancePriceTemp + diffInsurance;\n      item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\n    }\n    // DateDiff function matching AngularJS version exactly\n    dateDiff(sDate1, sDate2) {\n      let aDate, oDate1, oDate2, iDays;\n      aDate = sDate1.split(\"/\");\n      // oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])    // 12-18-2006\n      oDate1 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]); // 12-18-2006\n      aDate = sDate2.split(\"/\");\n      // oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])\n      oDate2 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]);\n      iDays = parseInt((Math.abs(oDate1.getTime() - oDate2.getTime()) / 1000 / 60 / 60 / 24).toString()); // Converts the number of milliseconds to days\n      return iDays;\n    }\n    pay() {\n      let isContinue = true;\n      let isOnlyPrincipalSave = true;\n      // Use forEach to match AngularJS logic exactly\n      this.formdata.forEach(data => {\n        if (data.bankAccount == null || data.bankAccount == undefined) {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: `${data.dba}'s Bank Account is required, please edit`\n          });\n          isContinue = false;\n        }\n        // 2018-10-17 sunhongyang NH-1504\n        let isSave = true;\n        let isSaveL = true;\n        if (data.dtoList != null && data.dtoList != undefined) {\n          for (let i = 0; i < data.dtoList.length; i++) {\n            // NH-2428\n            // ----------------- Edit Start -----------------\n            if (data.dtoList[i].contactSwitch) {\n              if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && !(data.dtoList[i].displayMail && data.dtoList[i].isTrusted)) {\n                // Mod by NH-3938 start\n                if (this.holdSwitch) {\n                  // Special Title Handling check on\n                  if (data.dtoList[i].isHold) {\n                    if (data.dtoList[i].holdType == undefined || data.dtoList[i].holdType == null || data.dtoList[i].holdType.length == 0) {\n                      this.messageService.add({\n                        severity: 'error',\n                        summary: 'Error',\n                        detail: 'Special Title Type is required'\n                      });\n                      isContinue = false;\n                    } else if (data.dtoList[i].holdType.value == \"T\" && (data.dtoList[i].holdContactInfo == undefined || data.dtoList[i].holdContactInfo == null || data.dtoList[i].holdContactInfo.length == 0)) {\n                      this.messageService.add({\n                        severity: 'error',\n                        summary: 'Error',\n                        detail: 'Shipping contact is required'\n                      });\n                      isContinue = false;\n                    } else if (data.dtoList[i].holdType.value == \"D\" || data.dtoList[i].holdType.value == \"H\") {\n                      if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                        isSave = false;\n                      } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                        isSaveL = false;\n                      }\n                    }\n                  } else {\n                    // Special Title Handling check off\n                    if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                      isSave = false;\n                    } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                      isSaveL = false;\n                    }\n                  }\n                }\n                // switch off\n                else {\n                  if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                    isSave = false;\n                  } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                    isSaveL = false;\n                  }\n                }\n                // Mod by NH-3938 end\n              }\n            } else {\n              // NH-1947\n              if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && (data.dtoList[i].contactInfo == undefined || data.dtoList[i].contactInfo == null || data.dtoList[i].contactInfo.length == 0)) {\n                isSave = false;\n              }\n            }\n            // ----------------- Edit End -----------------\n            if (data.dtoList[i].onlyPrincialAmount == 0 && data.dtoList[i].isOnlyPrincial == true) {\n              this.messageService.add({\n                severity: 'warning',\n                summary: 'Warning',\n                detail: 'Only Principal amount should be greater than 0!'\n              });\n              isOnlyPrincipalSave = false;\n            }\n          }\n        }\n        if (!isSave) {\n          isContinue = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Shipping contact is required'\n          });\n        } else if (!isSaveL) {\n          isContinue = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Shipping location is required'\n          });\n        }\n      });\n      if (!isOnlyPrincipalSave) {\n        return;\n      }\n      if (!isContinue) {\n        return;\n      }\n      this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\n        next: response => {\n          if (response.status === 'success') {\n            this.messageService.add({\n              severity: 'success',\n              summary: 'Success',\n              detail: response.results\n            });\n            this.cancel();\n          } else if (response.status === 'warning') {\n            this.messageService.add({\n              severity: 'warn',\n              summary: 'Warning',\n              detail: response.results\n            });\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.results\n            });\n          }\n        },\n        error: error => {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to process payment'\n          });\n        }\n      });\n    }\n    cancel() {\n      this.router.navigate(['/loan/schedule-payment']);\n    }\n    getPaymentLength() {\n      let totalLength = 0;\n      for (let i = 0; i < this.formdata.length; i++) {\n        if (this.formdata[i].dealerLevelFeeList != undefined) {\n          totalLength += this.formdata[i].dealerLevelFeeList.length;\n        }\n        if (this.formdata[i].dtoList != undefined) {\n          totalLength += this.formdata[i].dtoList.length;\n        }\n      }\n      return totalLength;\n    }\n    removeFee(dealer, feeItem) {\n      if (dealer && dealer.dealerLevelFeeList) {\n        const index = dealer.dealerLevelFeeList.findIndex(item => item.description === feeItem.description && item.remainingAmount === feeItem.remainingAmount);\n        if (index > -1) {\n          dealer.dealerLevelFeeList.splice(index, 1);\n          this.recalculateTotal(dealer);\n        }\n      }\n    }\n    recalculateTotal(dealer) {\n      const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total, fee) => {\n        return total + (fee.remainingAmount || 0);\n      }, 0);\n      dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\n    }\n    // 处理仅本金支付的金额变更\n    onlyPrincipalAmountChange(item) {\n      if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\n        if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n          this.messageService.add({\n            severity: 'warning',\n            summary: 'Warning',\n            detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\n          });\n          item.onlyPrincialAmount = 0;\n        }\n      } else {\n        if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n          this.messageService.add({\n            severity: 'warning',\n            summary: 'Warning',\n            detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\n          });\n          item.onlyPrincialAmount = 0;\n        }\n      }\n    }\n    // 打开Other Amount输入对话框\n    inputOtherAmount(item) {\n      this.selectedItem = item;\n      this.tempOtherAmount = item.otherAmount || 0;\n      this.showOtherAmountDialog = true;\n    }\n    // 取消Other Amount输入\n    cancelOtherAmount() {\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n    }\n    // 确认Other Amount输入\n    confirmOtherAmount() {\n      if (this.selectedItem) {\n        this.selectedItem.otherAmount = this.tempOtherAmount;\n        if (this.tempOtherAmount !== 0) {\n          this.selectedItem.buttonName = 'OtherAmount';\n        } else {\n          this.selectedItem.buttonName = 'Curtailment';\n        }\n      }\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n    }\n    // 获取最小日期\n    getMinDate(item) {\n      return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\n    }\n    // 获取最大日期\n    getMaxDate(item) {\n      return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\n    }\n    // Get minimum date for release date picker (based on title release date)\n    getReleaseDateMinDate(item) {\n      return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;\n    }\n    // Get maximum date for release date picker (based on title release date)\n    getReleaseDateMaxDate(item) {\n      return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;\n    }\n    // Add Contact Dialog\n    addContactDialog(item) {\n      this.selectedContactItem = item;\n      this.contactDialogMode = 'add';\n      this.contactForm = {\n        contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\n        firstName: '',\n        lastName: '',\n        addressLine1: '',\n        addressLine2: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        phone: '',\n        email: ''\n      };\n      this.showContactDialog = true;\n    }\n    // Edit Contact Dialog\n    editContactDialog(contactInfo, item) {\n      this.selectedContactItem = item;\n      this.contactDialogMode = 'edit';\n      this.editingContact = contactInfo;\n      this.contactForm = {\n        contactReference: contactInfo.contactReference,\n        firstName: contactInfo.firstName,\n        lastName: contactInfo.lastName,\n        addressLine1: contactInfo.addressLine1,\n        addressLine2: contactInfo.addressLine2 || '',\n        city: contactInfo.city,\n        state: contactInfo.state,\n        zipCode: contactInfo.zipCode,\n        phone: contactInfo.phone,\n        email: contactInfo.email || ''\n      };\n      this.showContactDialog = true;\n    }\n    // Save Contact\n    saveContact() {\n      if (this.contactDialogMode === 'add') {\n        const newContact = {\n          ...this.contactForm\n        };\n        // Add to contact list\n        if (this.selectedContactItem.contactDtoList) {\n          this.selectedContactItem.contactDtoList.push(newContact);\n        } else {\n          this.selectedContactItem.contactDtoList = [newContact];\n        }\n        // Set as current contact\n        this.selectedContactItem.contactInfo = newContact;\n        // Update all loans for same dealer\n        this.formdata.forEach(dealer => {\n          if (dealer.dealerId === this.selectedContactItem.dealerId) {\n            dealer.dtoList?.forEach(loan => {\n              if (loan.contactDtoList) {\n                loan.contactDtoList.push({\n                  ...newContact\n                });\n                if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n                  loan.isDisabledEdit = true;\n                }\n              }\n            });\n          }\n        });\n      } else {\n        // Edit mode\n        const updatedContact = {\n          ...this.contactForm\n        };\n        // Update in all places\n        this.formdata.forEach(dealer => {\n          if (dealer.dealerId === this.selectedContactItem.dealerId) {\n            dealer.dtoList?.forEach(loan => {\n              if (loan.contactDtoList) {\n                // Update in contact list\n                const index = loan.contactDtoList.findIndex(c => c.contactReference === this.editingContact.contactReference);\n                if (index > -1) {\n                  loan.contactDtoList[index] = updatedContact;\n                }\n                // Update current selection if matches\n                if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\n                  loan.contactInfo = updatedContact;\n                }\n              }\n            });\n          }\n        });\n      }\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.editingContact = null;\n    }\n    // Cancel Contact Dialog\n    cancelContactDialog() {\n      if (this.contactDialogMode === 'add') {\n        this.tempContactNumber--;\n      }\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.editingContact = null;\n    }\n    // Handle shipping contact change\n    shippingContactChange(item) {\n      if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n        item.isDisabledEdit = true;\n      } else {\n        item.isDisabledEdit = false;\n      }\n    }\n    // Remove payoff item\n    removePayoff(dealer, item) {\n      if (dealer.dtoList) {\n        const index = dealer.dtoList.findIndex(dto => dto === item);\n        if (index > -1) {\n          dealer.dtoList.splice(index, 1);\n        }\n      }\n    }\n    // View Title\n    viewTitle(item) {\n      if (item.fileManagementUrl) {\n        window.open(item.fileManagementUrl, '_blank');\n      }\n    }\n    static #_ = this.ɵfac = function LoanProceedComponent_Factory(t) {\n      return new (t || LoanProceedComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.LoanSchedulePaymentService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoanProceedComponent,\n      selectors: [[\"app-loan-proceed\"]],\n      features: [i0.ɵɵProvidersFeature([MessageService])],\n      decls: 58,\n      vars: 39,\n      consts: [[1, \"flex\", \"flex-column\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"flex\"], [1, \"colorce3434\", \"font-bold\", \"pl-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"greyButton\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Submit\", 1, \"greenButton\", 3, \"click\", \"disabled\"], [\"class\", \"panel border-round p-3 text-sm flex flex-column\", 4, \"ngFor\", \"ngForOf\"], [\"header\", \"Other Amount\", 3, \"visibleChange\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"closeOnEscape\", \"closable\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-3\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [\"for\", \"otherAmount\"], [\"id\", \"otherAmount\", \"mode\", \"currency\", \"currency\", \"USD\", 3, \"ngModelChange\", \"ngModel\", \"minFractionDigits\", \"maxFractionDigits\", \"maxlength\", \"max\"], [\"pTemplate\", \"footer\"], [3, \"visibleChange\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"header\", \"closeOnEscape\", \"closable\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"for\", \"firstName\"], [\"pInputText\", \"\", \"id\", \"firstName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"lastName\"], [\"pInputText\", \"\", \"id\", \"lastName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"addressLine1\"], [\"pInputText\", \"\", \"id\", \"addressLine1\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"addressLine2\"], [\"pInputText\", \"\", \"id\", \"addressLine2\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"city\"], [\"pInputText\", \"\", \"id\", \"city\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"state\"], [\"id\", \"state\", \"optionLabel\", \"text\", \"optionValue\", \"value\", \"placeholder\", \"Select\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"options\"], [\"for\", \"zipCode\"], [\"pInputText\", \"\", \"id\", \"zipCode\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"phone\"], [\"pInputText\", \"\", \"id\", \"phone\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"email\"], [\"pInputText\", \"\", \"id\", \"email\", 3, \"ngModelChange\", \"ngModel\"], [1, \"panel\", \"border-round\", \"p-3\", \"text-sm\", \"flex\", \"flex-column\"], [1, \"flex\", \"align-items-center\", \"flex-1\"], [1, \"flex\", \"w-3\"], [1, \"text-right\", \"color2B2E3A\", \"pr-2\"], [1, \"pl-2\", \"color2B2E3A\", \"font-bold\"], [1, \"pl-2\", \"font-bold\", \"colorce3434\"], [1, \"flex\", \"w-3\", \"align-items-center\"], [\"placeholder\", \"Select\", \"filterBy\", \"reference\", 1, \"dropdownStyle\", \"pl-2\", \"w-15rem\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"optionLabel\", \"optionValue\", \"filter\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [1, \"cursor-pointer\", 3, \"click\", \"src\"], [4, \"ngIf\"], [1, \"text-ellipsis\"], [\"class\", \"panel border-round p-3 flex mt-3 color2B2E3A relative\", 4, \"ngFor\", \"ngForOf\"], [1, \"panel\", \"border-round\", \"p-3\", \"flex\", \"mt-3\", \"color2B2E3A\", \"relative\"], [1, \"absolute\", \"closeIcon\", \"z-1\"], [1, \"pi\", \"pi-times-circle\", \"text-xl\", \"colorce3434\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"w-full\"], [1, \"flex\", \"w-4\"], [1, \"pl-2\"], [1, \"flex\", \"w-8\"], [1, \"pl-2\", \"colorce3434\"], [1, \"w-3\", \"flex\", \"flex-column\", \"gap-3\", \"p-3\", \"border-right-1\", \"border-color-AEB9CC\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"font-bold\"], [1, \"pi\", \"pi-car\"], [1, \"color2B2E3A\"], [1, \"w-4\", \"flex\", \"flex-column\", \"gap-2\", \"py-3\", \"px-5\", \"border-right-1\", \"border-color-AEB9CC\"], [1, \"pi\", \"pi-book\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"pl-3\"], [\"class\", \"pi pi-angle-down cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"pi pi-angle-up cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"flex flex-column trbg border-round-sm color82808F\", 4, \"ngIf\"], [\"class\", \"flex justify-content-between align-items-center\", 4, \"ngIf\"], [1, \"w-6\", \"pl-3\"], [1, \"w-6\"], [1, \"calendarStyle\", \"w-full\", 3, \"ngModelChange\", \"onSelect\", \"ngModel\", \"selectOtherMonths\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"dateFormat\", \"showTime\", \"showIcon\", \"readonlyInput\", \"minDate\", \"maxDate\", \"disabled\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [\"class\", \"flex flex-column gap-2\", 4, \"ngIf\"], [\"class\", \"w-5 flex flex-column gap-2 py-3 px-5\", 4, \"ngIf\"], [1, \"pi\", \"pi-angle-down\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-angle-up\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"flex-column\", \"trbg\", \"border-round-sm\", \"color82808F\"], [1, \"flex\", \"flex-column\", \"w-12\", \"p-3\", \"gap-2\"], [\"class\", \"flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"class\", \"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\", \"pTooltip\", \"Estimated Amount\", \"tooltipPosition\", \"top\", 4, \"ngIf\"], [\"pTooltip\", \"Estimated Amount\", \"tooltipPosition\", \"top\", 1, \"pi\", \"pi-question\", \"text-xs\", \"color4B78E8FF\", \"border-1\", \"border-round-3xl\", \"questionIcon\"], [1, \"text-primary\", \"cursor-pointer\", 3, \"click\"], [1, \"w-12\", \"flex\", \"gap-2\", \"justify-content-between\", \"align-items-center\"], [\"inputId\", \"principalOnly\", 1, \"checkbox\", \"w-6\", 3, \"ngModelChange\", \"ngModel\", \"binary\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [1, \"w-full\"], [\"inputId\", \"onlyPrincialAmount\", \"mode\", \"decimal\", 1, \"inputNumberRadius\", \"w-full\", 3, \"ngModelChange\", \"minFractionDigits\", \"maxFractionDigits\", \"ngModel\", \"maxlength\"], [1, \"calendarStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\", \"selectOtherMonths\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"dateFormat\"], [1, \"w-5\", \"flex\", \"flex-column\", \"gap-2\", \"py-3\", \"px-5\"], [1, \"pi\", \"pi-envelope\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-file\", 1, \"p-button-rounded\", \"p-button-text\", \"ml-2\", 3, \"click\", \"disabled\"], [\"class\", \"w-full\", 4, \"ngIf\"], [\"label\", \"Special Title Handling\", 1, \"checkbox\", 3, \"ngModelChange\", \"ngModel\", \"binary\", \"disabled\"], [\"class\", \"flex align-items-center\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\"], [1, \"w-4\"], [1, \"w-8\"], [\"optionLabel\", \"text\", \"placeholder\", \"Select\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"disabled\"], [\"class\", \"flex align-items-start\", 4, \"ngIf\"], [\"class\", \"p-2\", 4, \"ngIf\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"readonlyInput\", \"dateFormat\", \"minDate\", \"maxDate\", \"disabled\"], [1, \"p-2\"], [\"optionLabel\", \"uccProviderName\", \"placeholder\", \"Select\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"disabled\"], [\"class\", \"flex align-items-start my-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-start\", \"my-2\"], [1, \"m-0\"], [\"class\", \"m-0\", 4, \"ngIf\"], [\"optionLabel\", \"contactName\", \"placeholder\", \"Select\", 1, \"w-full\", 3, \"ngModelChange\", \"onChange\", \"ngModel\", \"options\", \"disabled\"], [\"optionLabel\", \"address1\", \"placeholder\", \"Select\", 1, \"w-full\", 3, \"ngModelChange\", \"onChange\", \"ngModel\", \"options\", \"disabled\"], [1, \"flex\", \"align-items-start\"], [\"pInputTextarea\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\", \"maxlength\"], [\"class\", \"w-2 text-right\", 4, \"ngIf\"], [\"class\", \"w-2\", 4, \"ngIf\"], [1, \"w-2\", \"text-right\"], [1, \"w-2\"], [1, \"checkbox\", 3, \"ngModelChange\", \"ngModel\", \"binary\", \"disabled\"], [1, \"w-8\", \"flex\", \"gap-2\"], [\"optionLabel\", \"contactReference\", \"placeholder\", \"Select\", 1, \"w-full\", 3, \"ngModelChange\", \"onChange\", \"ngModel\", \"options\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\", \"disabled\"], [1, \"flex\", \"justify-content-end\", \"gap-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Confirm\", 1, \"greenButton\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Save\", 1, \"greenButton\", 3, \"click\"]],\n      template: function LoanProceedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"Total: \");\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"currency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function LoanProceedComponent_Template_button_click_8_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoanProceedComponent_Template_button_click_9_listener() {\n            return ctx.pay();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, LoanProceedComponent_div_10_Template, 27, 12, \"div\", 7);\n          i0.ɵɵelementStart(11, \"p-dialog\", 8);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoanProceedComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.showOtherAmountDialog, $event) || (ctx.showOtherAmountDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p-inputNumber\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_p_inputNumber_ngModelChange_16_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.tempOtherAmount, $event) || (ctx.tempOtherAmount = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, LoanProceedComponent_ng_template_17_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p-dialog\", 14);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoanProceedComponent_Template_p_dialog_visibleChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.showContactDialog, $event) || (ctx.showContactDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 15)(21, \"div\", 10)(22, \"label\", 16);\n          i0.ɵɵtext(23, \"First Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.firstName, $event) || (ctx.contactForm.firstName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 10)(26, \"label\", 18);\n          i0.ɵɵtext(27, \"Last Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.lastName, $event) || (ctx.contactForm.lastName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 10)(30, \"label\", 20);\n          i0.ɵɵtext(31, \"Address Line 1 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"input\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_32_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.addressLine1, $event) || (ctx.contactForm.addressLine1 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 10)(34, \"label\", 22);\n          i0.ɵɵtext(35, \"Address Line 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"input\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_36_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.addressLine2, $event) || (ctx.contactForm.addressLine2 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 10)(38, \"label\", 24);\n          i0.ɵɵtext(39, \"City *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_40_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.city, $event) || (ctx.contactForm.city = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 10)(42, \"label\", 26);\n          i0.ɵɵtext(43, \"State *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p-dropdown\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_p_dropdown_ngModelChange_44_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.state, $event) || (ctx.contactForm.state = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 10)(46, \"label\", 28);\n          i0.ɵɵtext(47, \"Zip Code *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"input\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_48_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.zipCode, $event) || (ctx.contactForm.zipCode = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 10)(50, \"label\", 30);\n          i0.ɵɵtext(51, \"Phone *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"input\", 31);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_52_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.phone, $event) || (ctx.contactForm.phone = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 10)(54, \"label\", 32);\n          i0.ɵɵtext(55, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"input\", 33);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_56_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.email, $event) || (ctx.contactForm.email = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(57, LoanProceedComponent_ng_template_57_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 35, ctx.getTotal()));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.getPaymentLength() === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.formdata);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(37, _c0));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showOtherAmountDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"closeOnEscape\", false)(\"closable\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tempOtherAmount);\n          i0.ɵɵproperty(\"minFractionDigits\", 2)(\"maxFractionDigits\", 2)(\"maxlength\", 14)(\"max\", ctx.selectedItem == null ? null : ctx.selectedItem.otherAmountLimit);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(38, _c1));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showContactDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"header\", ctx.contactDialogMode === \"add\" ? \"Add Contact\" : \"Edit Contact\")(\"closeOnEscape\", false)(\"closable\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.firstName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.addressLine1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.addressLine2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.city);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.state);\n          i0.ɵɵproperty(\"options\", ctx.stateList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.zipCode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.phone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.email);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ButtonDirective, i3.PrimeTemplate, i6.Checkbox, i7.InputText, i8.Tooltip, i9.Dialog, i10.Calendar, i11.InputTextarea, i12.Dropdown, i13.InputNumber, i14.DefaultValueAccessor, i14.NgControlStatus, i14.RequiredValidator, i14.MaxLengthValidator, i14.NgModel, i15.InputGroup, i16.InputGroupAddon, i17.Ripple, i4.CurrencyPipe, i4.DatePipe],\n      styles: [\".questionIcon[_ngcontent-%COMP%] {\\n  padding: 2px;\\n}\\n\\n.closeIcon[_ngcontent-%COMP%] {\\n  right: 20px;\\n}\\n\\n  .p-calendar .p-datepicker {\\n  width: 360px !important;\\n}\\n\\n[_nghost-%COMP%]     .inputNumberRadius .p-inputtext {\\n  border-radius: 0 !important;\\n  font-size: 14px;\\n}\\n\\n[_nghost-%COMP%]     .inputNumberBorder .p-inputtext {\\n  border-right: 0;\\n}\\n\\n  .p-dropdown,   .p-calendar,   .p-inputtext,   .p-inputtextarea,   .p-inputnumber {\\n  width: 100% !important;\\n}\\n  .p-dropdown input,   .p-calendar input,   .p-inputtext input,   .p-inputtextarea input,   .p-inputnumber input {\\n  width: 100% !important;\\n}\\n  .p-dropdown .p-dropdown-label,   .p-dropdown .p-dropdown-item {\\n  font-size: 14px !important;\\n}\\n  .p-calendar .p-inputtext {\\n  font-size: 14px !important;\\n}\\n  .p-datepicker table td > span,   .p-datepicker table th > span {\\n  font-size: 14px !important;\\n}\\n  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month,   .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year {\\n  font-size: 14px !important;\\n}\\n  .p-inputtext,   .p-inputtextarea {\\n  font-size: 14px !important;\\n}\\n  .p-checkbox-label {\\n  font-size: 14px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LoanProceedComponent;\n})();", "map": {"version": 3, "names": ["moment", "MessageService", "i0", "ɵɵtext", "ɵɵtextInterpolate2", "item_r3", "bankAccountRanking", "reference", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "item_r4", "ɵɵlistener", "LoanProceedComponent_div_10_div_26_div_1_Template_i_click_2_listener", "feeItem_r7", "ɵɵrestoreView", "_r6", "$implicit", "dealer_r2", "ɵɵnextContext", "ctx_r4", "ɵɵresetView", "removeFee", "ɵɵtextInterpolate", "description", "ɵɵpipeBind1", "remainingAmount", "LoanProceedComponent_div_10_div_26_div_2_i_23_Template_i_click_0_listener", "_r10", "show<PERSON><PERSON>off", "LoanProceedComponent_div_10_div_26_div_2_i_24_Template_i_click_0_listener", "_r11", "fee_r12", "feeName", "amount", "ɵɵelement", "ɵɵtemplate", "LoanProceedComponent_div_10_div_26_div_2_div_29_div_8_Template", "LoanProceedComponent_div_10_div_26_div_2_div_29_i_13_Template", "LoanProceedComponent_div_10_div_26_div_2_div_29_i_20_Template", "item_r9", "principal", "ɵɵproperty", "extraAmountList", "isShowEstimation", "ɵɵtextInterpolate1", "interestPrice", "insurancePrice", "LoanProceedComponent_div_10_div_26_div_2_div_30_Template_a_click_7_listener", "_r13", "inputOtherAmount", "otherAmount", "ɵɵtwoWayListener", "LoanProceedComponent_div_10_div_26_div_2_div_36_Template_p_checkbox_ngModelChange_4_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "isOnlyPrincial", "ɵɵtwoWayProperty", "LoanProceedComponent_div_10_div_26_div_2_div_37_Template_p_inputNumber_ngModelChange_8_listener", "_r15", "onlyPrincialAmount", "onlyPrincipalAmountChange", "LoanProceedComponent_div_10_div_26_div_2_div_37_Template_p_calendar_ngModelChange_13_listener", "scheduleDate", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_6_Template_p_checkbox_ngModelChange_1_listener", "_r17", "isHold", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_4_Template_p_calendar_ngModelChange_1_listener", "_r19", "titleReleaseDate", "getReleaseDateMinDate", "getReleaseDateMaxDate", "ɵɵpipeBind2", "titleReleaseHoldDate", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_4_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_div_5_Template", "holdType", "value", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_7_Template_p_dropdown_ngModelChange_4_listener", "_r20", "mailFeeInfo", "postageFee", "selected_r22", "uccProviderName", "address", "provider_r23", "ɵɵtextInterpolate3", "holdContactInfo", "city", "state", "zipCode", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_p_8_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_Template_p_dropdown_ngModelChange_5_listener", "_r21", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_6_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_ng_template_7_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_div_8_Template", "uccProviderList", "uccProviderId", "newLocationInfo", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_ngModelChange_5_listener", "_r24", "newContactInfo", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_onChange_5_listener", "shippingContactChange", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_ngModelChange_10_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template_p_dropdown_onChange_10_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_p_19_Template", "newContactDtoList", "newLocationDtoList", "firstName", "lastName", "address1", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_10_Template_textarea_ngModelChange_4_listener", "_r25", "note", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_1_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_Template_p_dropdown_ngModelChange_6_listener", "_r18", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_7_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_8_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_9_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_div_10_Template", "holdTypeList", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_7_Template_p_checkbox_ngModelChange_1_listener", "_r27", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_p_dropdown_ngModelChange_5_listener", "_r28", "contactInfo", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_p_dropdown_onChange_5_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_button_click_6_listener", "addContactDialog", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template_button_click_7_listener", "editContactDialog", "contactDtoList", "isDisabledEdit", "addressLine1", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_ngModelChange_5_listener", "_r29", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_onChange_5_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_ngModelChange_10_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template_p_dropdown_onChange_10_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_p_19_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template_p_calendar_ngModelChange_5_listener", "_r26", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_6_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_7_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template_p_dropdown_ngModelChange_12_listener", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_13_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_div_14_Template", "holdSwitch", "contactSwitch", "LoanProceedComponent_div_10_div_26_div_2_div_38_Template_button_click_5_listener", "_r16", "viewTitle", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_6_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_7_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_div_8_Template", "isHasTitleFile", "LoanProceedComponent_div_10_div_26_div_2_Template_i_click_2_listener", "_r8", "<PERSON><PERSON><PERSON><PERSON>", "LoanProceedComponent_div_10_div_26_div_2_i_23_Template", "LoanProceedComponent_div_10_div_26_div_2_i_24_Template", "LoanProceedComponent_div_10_div_26_div_2_div_29_Template", "LoanProceedComponent_div_10_div_26_div_2_div_30_Template", "LoanProceedComponent_div_10_div_26_div_2_Template_p_calendar_ngModelChange_35_listener", "LoanProceedComponent_div_10_div_26_div_2_Template_p_calendar_onSelect_35_listener", "scheduleDateChange", "LoanProceedComponent_div_10_div_26_div_2_div_36_Template", "LoanProceedComponent_div_10_div_26_div_2_div_37_Template", "LoanProceedComponent_div_10_div_26_div_2_div_38_Template", "LoanProceedComponent_div_10_div_26_div_2_div_39_Template", "vin", "year", "make", "model", "nextDueDate", "payoff", "buttonName", "totalMoney", "is<PERSON>ayOff", "isPartialPayment", "getMinDate", "getMaxDate", "isShowPrincipal", "displayMail", "isTrusted", "LoanProceedComponent_div_10_div_26_div_1_Template", "LoanProceedComponent_div_10_div_26_div_2_Template", "dealerLevelFeeList", "dtoList", "LoanProceedComponent_div_10_Template_p_dropdown_ngModelChange_22_listener", "_r1", "bankAccount", "LoanProceedComponent_div_10_ng_template_23_Template", "LoanProceedComponent_div_10_ng_template_24_Template", "LoanProceedComponent_div_10_Template_img_click_25_listener", "toggle<PERSON><PERSON>er", "LoanProceedComponent_div_10_div_26_Template", "dealerCode", "dba", "getSubTotal", "bankAccountList", "isExpanded", "ɵɵsanitizeUrl", "LoanProceedComponent_ng_template_17_Template_button_click_1_listener", "_r30", "cancelOtherAmount", "LoanProceedComponent_ng_template_17_Template_button_click_2_listener", "confirmOtherAmount", "LoanProceedComponent_ng_template_57_Template_button_click_1_listener", "_r31", "cancelContactDialog", "LoanProceedComponent_ng_template_57_Template_button_click_2_listener", "saveContact", "LoanProceedComponent", "dealer", "constructor", "router", "route", "loanSchedulePaymentService", "messageService", "formdata", "stateList", "showOtherAmountDialog", "selectedItem", "tempOtherAmount", "calendarConfig", "showButtonBar", "monthNavigator", "yearNavigator", "dateFormat", "showTime", "showIcon", "readonlyInput", "appendTo", "showContactDialog", "selectedContactItem", "contactDialogMode", "editingContact", "tempContactNumber", "contactForm", "contactReference", "addressLine2", "phone", "email", "ngOnInit", "mockData", "selectData", "data", "JSON", "stringify", "add", "severity", "summary", "detail", "navigate", "paymentData", "parse", "selectDealer<PERSON>ee", "getMakePaymentInfo", "subscribe", "next", "response", "code", "results", "releaseHoldTypeList", "releaseHoldSwitch", "i", "length", "undefined", "j", "newLocationDto", "newContactDto", "releaseDate", "delayDays", "format", "holdTypes", "filter", "c", "paymentSource", "bankAccountId", "bankAccountDtoId", "for<PERSON>ach", "dto", "toDate", "scheduleDateEnd", "currentDate", "item", "message", "error", "console", "getTotal", "total", "isNaN", "parseFloat", "dealerInfo", "getDatePickerConfig", "config", "minDate", "Date", "defaultDate", "maxDate", "now", "diffDays", "dateDiff", "diffInterest", "interestDaily", "diffInsurance", "insuranceDaily", "interestPriceTemp", "insurancePriceTemp", "tempTotalMoney", "sDate1", "sDate2", "aDate", "oDate1", "oDate2", "iDays", "split", "parseInt", "Math", "abs", "getTime", "toString", "pay", "isContinue", "isOnlyPrincipalSave", "isSave", "isSaveL", "editMakePayment", "status", "cancel", "getPaymentLength", "totalLength", "feeItem", "index", "findIndex", "splice", "recalculateTotal", "dealerLevelFeeTotal", "reduce", "fee", "payOffPrincipal", "newContact", "push", "dealerId", "loan", "includes", "updatedContact", "fileManagementUrl", "window", "open", "_", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "LoanSchedulePaymentService", "i3", "_2", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "LoanProceedComponent_Template", "rf", "ctx", "LoanProceedComponent_Template_button_click_8_listener", "LoanProceedComponent_Template_button_click_9_listener", "LoanProceedComponent_div_10_Template", "LoanProceedComponent_Template_p_dialog_visibleChange_11_listener", "LoanProceedComponent_Template_p_inputNumber_ngModelChange_16_listener", "LoanProceedComponent_ng_template_17_Template", "LoanProceedComponent_Template_p_dialog_visibleChange_18_listener", "LoanProceedComponent_Template_input_ngModelChange_24_listener", "LoanProceedComponent_Template_input_ngModelChange_28_listener", "LoanProceedComponent_Template_input_ngModelChange_32_listener", "LoanProceedComponent_Template_input_ngModelChange_36_listener", "LoanProceedComponent_Template_input_ngModelChange_40_listener", "LoanProceedComponent_Template_p_dropdown_ngModelChange_44_listener", "LoanProceedComponent_Template_input_ngModelChange_48_listener", "LoanProceedComponent_Template_input_ngModelChange_52_listener", "LoanProceedComponent_Template_input_ngModelChange_56_listener", "LoanProceedComponent_ng_template_57_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "otherAmountLimit", "_c1"], "sources": ["D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-proceed\\loan-proceed.component.ts", "D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-proceed\\loan-proceed.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport moment from 'moment';\nimport { LoanSchedulePaymentService } from '../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service';\nimport { MessageService } from 'primeng/api';\n\n@Component({\n  selector: 'app-loan-proceed',\n  templateUrl: './loan-proceed.component.html',\n  styleUrl: './loan-proceed.component.scss',\n  providers: [MessageService]\n})\nexport class LoanProceedComponent implements OnInit {\n  payoff: boolean = false;\n  formdata: any[] = [];\n  postageFee: any[] = [];\n  stateList: any[] = [];\n  holdTypeList: any[] = [];\n  uccProviderList: any[] = [];\n  holdSwitch: boolean = false;\n  isShowEstimation: boolean = false;\n\n  // Toggle dealer panel\n  toggleDealer(dealer: any) {\n    dealer.isExpanded = !dealer.isExpanded;\n  }\n\n  // Additional Payment dialog variables\n  showOtherAmountDialog: boolean = false;\n  selectedItem: any = null;\n  tempOtherAmount: number = 0;\n\n  // Calendar configuration\n  calendarConfig: any = {\n    showButtonBar: true,\n    monthNavigator: true,\n    yearNavigator: true,\n    dateFormat: 'mm/dd/yy',\n    showTime: false,\n    showIcon: false,\n    readonlyInput: true,\n    appendTo: 'body'\n  };\n\n  // Contact Dialog\n  showContactDialog: boolean = false;\n  selectedContactItem: any = null;\n  contactDialogMode: 'add' | 'edit' = 'add';\n  editingContact: any = null;\n  tempContactNumber: number = 0;\n\n  // Contact Form\n  contactForm = {\n    contactReference: '',\n    firstName: '',\n    lastName: '',\n    addressLine1: '',\n    addressLine2: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    phone: '',\n    email: ''\n  };\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private loanSchedulePaymentService: LoanSchedulePaymentService,\n    private messageService: MessageService\n  ) {}\n\n  ngOnInit() {\n    // 临时使用mock数据替代路由参数\n    const mockData = {\n      selectData: [\n        {\n          \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 351764,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": null,\n          \"soldDisp\": \"\",\n          \"financeTag\": null,\n          \"assetId\": null,\n          \"titleStatus\": \"RE\",\n          \"titleStatusDisplay\": \"Trusted Title Received\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2022,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287165\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 90,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 90,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 0,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 0,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 214,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287165-BMW 320Li 2022\",\n          \"feeAmountNoReserve\": 90,\n          \"reserveFee\": 0,\n          \"extraAmountList\": [\n            {\n              \"feeId\": null,\n              \"feeName\": \"TERM_3\",\n              \"createDate\": null,\n              \"amount\": 50,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 0,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            }\n          ],\n          \"extraAmountListPayoff\": [\n            {\n              \"feeId\": null,\n              \"feeName\": \"TERM_3\",\n              \"createDate\": null,\n              \"amount\": 50,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 0,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            }\n          ],\n          \"extraAmountListPartial\": [\n            {\n              \"active\": true,\n              \"fixAmount\": 50,\n              \"tempAmount\": 50,\n              \"feeId\": null,\n              \"feeName\": \"TERM_3\",\n              \"createDate\": null,\n              \"amount\": 50,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 0,\n              \"tempAmount\": 0,\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 0,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 25,\n              \"tempAmount\": 25,\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 15,\n              \"tempAmount\": 15,\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            }\n          ],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        },\n        {\n          \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 352478,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": \"\",\n          \"soldDisp\": \"\",\n          \"financeTag\": \"\",\n          \"assetId\": null,\n          \"titleStatus\": \"PT\",\n          \"titleStatusDisplay\": \"Pending Release Trusted\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2023,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287243\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 2313,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 2313,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 2000,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 2000,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 312,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287243-BMW 320Li 2023\",\n          \"feeAmountNoReserve\": 65,\n          \"reserveFee\": 248,\n          \"extraAmountList\": [\n            {\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 2000,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            }\n          ],\n          \"extraAmountListPayoff\": [\n            {\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 2000,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"RESERVE\",\n              \"feeName\": \"Reserve\",\n              \"createDate\": null,\n              \"amount\": 248,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            },\n            {\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            }\n          ],\n          \"extraAmountListPartial\": [\n            {\n              \"active\": true,\n              \"fixAmount\": 2000,\n              \"tempAmount\": 2000,\n              \"feeId\": null,\n              \"feeName\": \"Principal\",\n              \"createDate\": null,\n              \"amount\": 2000,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 25,\n              \"tempAmount\": 25,\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            },\n            {\n              \"active\": false,\n              \"fixAmount\": 0,\n              \"tempAmount\": 0,\n              \"feeId\": \"RESERVE\",\n              \"feeName\": \"Reserve\",\n              \"createDate\": null,\n              \"amount\": 248,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 15,\n              \"tempAmount\": 15,\n              \"feeId\": \"ADMIN_FEE\",\n              \"feeName\": \"Admin Fee\",\n              \"createDate\": null,\n              \"amount\": 15,\n              \"payFlag\": null\n            },\n            {\n              \"active\": true,\n              \"fixAmount\": 25,\n              \"tempAmount\": 25,\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\n              \"feeName\": \"Trusted Title Fee\",\n              \"createDate\": null,\n              \"amount\": 25,\n              \"payFlag\": null\n            }\n          ],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        }\n      ],\n      \"selectDealerFee\": [\n        {\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\n          \"feeName\": \"Audit Fee\",\n          \"description\": \"Audit Fee\",\n          \"remainingAmount\": 100,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2023-06-20T09:29:05\",\n          \"postpaymentDealerFeeAmount\": 100,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        },\n        {\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\n          \"feeName\": \"OC Defense Fee_CO\",\n          \"description\": \"OC Defense Fee_CO\",\n          \"remainingAmount\": 123,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2025-05-29T02:44:35\",\n          \"postpaymentDealerFeeAmount\": 123,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        }\n      ]\n    };\n\n    const data = JSON.stringify(mockData);\n\n    if (!data) {\n      this.messageService.add({severity:'error', summary: 'Error', detail: 'No payment data found'});\n      this.router.navigate(['/loan/schedule-payment']);\n      return;\n    }\n\n    const paymentData = {\n      selectData: JSON.parse(data).selectData,\n      selectDealerFee: JSON.parse(data).selectDealerFee\n    };\n\n    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\n      next: (response: any) => {\n        if (response.code === 200) {\n          this.formdata = response.data.results || [];\n          this.postageFee = response.data.postageFee || [];\n          this.stateList = response.data.stateList || [];\n          // add by NH-3938 start\n          this.holdTypeList = response.data.releaseHoldTypeList || [];\n          this.uccProviderList = response.data.uccProviderList || [];\n          this.holdSwitch = response.data.releaseHoldSwitch || false;\n          // add by NH-3938 end\n\n          // select default post address - First loop (lines 113-145 in AngularJS)\n          for (let i = 0; i < this.formdata.length; i++) {\n            const dtoList = this.formdata[i].dtoList;\n            if (dtoList != null && dtoList != undefined) {\n              for (let j = 0; j < dtoList.length; j++) {\n                // NH-1947\n                if (dtoList[j].isPayOff == true && !dtoList[j].isPartialPayment) {\n                  // NH-2428\n                  // ----------------- Edit Start -----------------\n                  if (dtoList[j].contactSwitch) {\n                    this.formdata[i].dtoList[j].newLocationInfo = dtoList[j].newLocationDto;\n                    this.formdata[i].dtoList[j].newContactInfo = dtoList[j].newContactDto;\n                  } else {\n                    this.formdata[i].dtoList[j].contactInfo = dtoList[j].contactDtoList[0];\n                  }\n                  // ----------------- Edit End -----------------\n                  this.formdata[i].dtoList[j].mailFeeInfo = this.postageFee[0];\n                }\n\n                const releaseDate = moment(this.formdata[i].dtoList[j].scheduleDate).add(this.formdata[i].dtoList[j].delayDays, 'days').format(\"MM/DD/YYYY\");\n                this.formdata[i].dtoList[j].titleReleaseDate = releaseDate;\n\n                // add by NH-3938 start\n                this.formdata[i].dtoList[j].uccProviderList = this.uccProviderList;\n                if (this.holdSwitch && this.formdata[i].dtoList[j].isHold) {\n                  const holdTypes = this.holdTypeList.filter((c: any) => c.value == \"H\");\n                  if (holdTypes.length > 0) {\n                    this.formdata[i].dtoList[j].holdType = holdTypes[0];\n                  }\n                }\n                // add by NH-3938 end\n              }\n            }\n          }\n\n          // Second loop for bank account matching (lines 147-159 in AngularJS)\n          for (let i = 0; i < this.formdata.length; i++) {\n            if (this.formdata[i].paymentSource != null && this.formdata[i].paymentSource != undefined\n              && this.formdata[i].bankAccountList != null && this.formdata[i].bankAccountList != undefined) {\n              for (let j = 0; j < this.formdata[i].bankAccountList.length; j++) {\n                if (this.formdata[i].paymentSource.bankAccountId == this.formdata[i].bankAccountList[j].bankAccountDtoId) {\n                  this.formdata[i].bankAccount = this.formdata[i].bankAccountList[j];\n                  break;\n                }\n              }\n            }\n          }\n\n          // Convert date fields and set expanded state after processing\n          this.formdata.forEach((dealer: any) => {\n            dealer.isExpanded = true;\n            if (dealer.dtoList) {\n              dealer.dtoList.forEach((dto: any) => {\n                if(dto.scheduleDate) {\n                  dto.scheduleDate = moment(dto.scheduleDate).toDate();\n                }\n                if(dto.scheduleDateEnd) {\n                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\n                }\n                if(dto.currentDate) {\n                  dto.currentDate = moment(dto.currentDate).toDate();\n                }\n              });\n            }\n          });\n\n          // 初始化数据\n          this.formdata.forEach((dealer: any) => {\n            if (dealer.dtoList) {\n              dealer.dtoList.forEach((item: any) => {\n                this.scheduleDateChange(item);\n              });\n            }\n          });\n        } else {\n          this.messageService.add({severity:'error', summary: 'Error', detail: response.message});\n          this.router.navigate(['/loan/schedule-payment']);\n        }\n      },\n      error: (error: Error) => {\n        this.messageService.add({severity:'error', summary: 'Error', detail: 'Failed to get payment info'});\n        console.error('Error getting payment info:', error);\n      }\n    });\n\n  }\n\n  showPayoff() {\n    this.payoff = !this.payoff;\n  }\n\n  getTotal(): number {\n    let total = 0.0;\n    if (this.formdata != null) {\n      for (let i = 0; i < this.formdata.length; i++) {\n        if (this.formdata[i].dealerLevelFeeList != null) {\n          for (let j = 0; j < this.formdata[i].dealerLevelFeeList.length; j++) {\n            if (isNaN(this.formdata[i].dealerLevelFeeList[j].remainingAmount) == false) {\n              total += this.formdata[i].dealerLevelFeeList[j].remainingAmount;\n            }\n          }\n        }\n        if (this.formdata[i].dtoList != null) {\n          for (let j = 0; j < this.formdata[i].dtoList.length; j++) {\n            if (isNaN(this.formdata[i].dtoList[j].totalMoney) == false) {\n              total += this.formdata[i].dtoList[j].totalMoney;\n            }\n            if (isNaN(this.formdata[i].dtoList[j].otherAmount) == false) {\n              total += parseFloat(this.formdata[i].dtoList[j].otherAmount);\n            }\n          }\n        }\n      }\n    }\n    return total;\n  }\n\n  getSubTotal(dealerInfo: any): number {\n    let total = 0;\n\n    if (dealerInfo != null) {\n      if (dealerInfo.dealerLevelFeeList != null) {\n        for (let j = 0; j < dealerInfo.dealerLevelFeeList.length; j++) {\n          total += dealerInfo.dealerLevelFeeList[j].remainingAmount;\n        }\n      }\n      if (dealerInfo.dtoList != null) {\n        for (let j = 0; j < dealerInfo.dtoList.length; j++) {\n          total += dealerInfo.dtoList[j].totalMoney;\n          total += parseFloat(dealerInfo.dtoList[j].otherAmount);\n        }\n      }\n    }\n\n    return total;\n  }\n\n  getDatePickerConfig(scheduleDate: string, scheduleDateEnd: string): any {\n    const config = { ...this.calendarConfig };\n    if (scheduleDate) {\n      config.minDate = new Date(scheduleDate);\n      config.defaultDate = new Date(scheduleDate);\n    }\n    if (scheduleDateEnd) {\n      config.maxDate = new Date(scheduleDateEnd);\n    }\n    return config;\n  }\n\n  scheduleDateChange(item: any) {\n    const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');\n    item.titleReleaseDate = releaseDate;\n\n    const now = moment(item.currentDate).format('MM/dd/yyyy');\n    const diffDays = this.dateDiff(moment(item.scheduleDate).format('MM/DD/YYYY'), now);\n\n    if (diffDays > 0) {\n      this.isShowEstimation = true;\n    } else {\n      this.isShowEstimation = false;\n    }\n\n    const diffInterest = diffDays * item.interestDaily;\n    const diffInsurance = diffDays * item.insuranceDaily;\n    item.interestPrice = item.interestPriceTemp + diffInterest;\n    item.insurancePrice = item.insurancePriceTemp + diffInsurance;\n\n    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\n  }\n\n  // DateDiff function matching AngularJS version exactly\n  private dateDiff(sDate1: string, sDate2: string): number {\n    let aDate, oDate1, oDate2, iDays;\n    aDate = sDate1.split(\"/\");\n    // oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])    // 12-18-2006\n    oDate1 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]);    // 12-18-2006\n    aDate = sDate2.split(\"/\");\n    // oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])\n    oDate2 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]);\n    iDays = parseInt((Math.abs(oDate1.getTime() - oDate2.getTime()) / 1000 / 60 / 60 / 24).toString());    // Converts the number of milliseconds to days\n    return iDays;\n  }\n\n  pay() {\n    let isContinue = true;\n    let isOnlyPrincipalSave = true;\n\n    // Use forEach to match AngularJS logic exactly\n    this.formdata.forEach((data: any) => {\n      if (data.bankAccount == null || data.bankAccount == undefined) {\n        this.messageService.add({\n          severity:'error',\n          summary: 'Error',\n          detail: `${data.dba}'s Bank Account is required, please edit`\n        });\n        isContinue = false;\n      }\n\n      // 2018-10-17 sunhongyang NH-1504\n      let isSave = true;\n      let isSaveL = true;\n      if (data.dtoList != null && data.dtoList != undefined) {\n        for (let i = 0; i < data.dtoList.length; i++) {\n          // NH-2428\n          // ----------------- Edit Start -----------------\n          if (data.dtoList[i].contactSwitch) {\n            if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && !(data.dtoList[i].displayMail && data.dtoList[i].isTrusted)) {\n\n              // Mod by NH-3938 start\n              if (this.holdSwitch) {\n\n                // Special Title Handling check on\n                if (data.dtoList[i].isHold) {\n                  if (data.dtoList[i].holdType == undefined || data.dtoList[i].holdType == null || data.dtoList[i].holdType.length == 0) {\n                    this.messageService.add({\n                      severity:'error',\n                      summary: 'Error',\n                      detail: 'Special Title Type is required'\n                    });\n                    isContinue = false;\n                  }\n                  else if (data.dtoList[i].holdType.value == \"T\" && (data.dtoList[i].holdContactInfo == undefined || data.dtoList[i].holdContactInfo == null || data.dtoList[i].holdContactInfo.length == 0)) {\n                    this.messageService.add({\n                      severity:'error',\n                      summary: 'Error',\n                      detail: 'Shipping contact is required'\n                    });\n                    isContinue = false;\n                  }\n                  else if (data.dtoList[i].holdType.value == \"D\" || data.dtoList[i].holdType.value == \"H\") {\n                    if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                      isSave = false;\n                    } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                      isSaveL = false;\n                    }\n                  }\n                }\n                else {\n                  // Special Title Handling check off\n                  if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                    isSave = false;\n                  } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                    isSaveL = false;\n                  }\n                }\n              }\n              // switch off\n              else {\n                if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {\n                  isSave = false;\n                } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {\n                  isSaveL = false;\n                }\n              }\n              // Mod by NH-3938 end\n            }\n\n          } else {\n            // NH-1947\n            if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && (data.dtoList[i].contactInfo == undefined || data.dtoList[i].contactInfo == null || data.dtoList[i].contactInfo.length == 0)) {\n              isSave = false;\n            }\n          }\n          // ----------------- Edit End -----------------\n\n          if (data.dtoList[i].onlyPrincialAmount == 0 && data.dtoList[i].isOnlyPrincial == true) {\n            this.messageService.add({\n              severity:'warning',\n              summary: 'Warning',\n              detail: 'Only Principal amount should be greater than 0!'\n            });\n            isOnlyPrincipalSave = false;\n          }\n        }\n      }\n\n      if (!isSave) {\n        isContinue = false;\n        this.messageService.add({\n          severity:'error',\n          summary: 'Error',\n          detail: 'Shipping contact is required'\n        });\n      } else if (!isSaveL) {\n        isContinue = false;\n        this.messageService.add({\n          severity:'error',\n          summary: 'Error',\n          detail: 'Shipping location is required'\n        });\n      }\n    });\n\n    if (!isOnlyPrincipalSave) {\n      return;\n    }\n\n    if (!isContinue) {\n      return;\n    }\n\n    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\n      next: (response: any) => {\n        if (response.status === 'success') {\n          this.messageService.add({\n            severity:'success',\n            summary: 'Success',\n            detail: response.results\n          });\n          this.cancel();\n        } else if (response.status === 'warning') {\n          this.messageService.add({\n            severity:'warn',\n            summary: 'Warning',\n            detail: response.results\n          });\n        } else {\n          this.messageService.add({\n            severity:'error',\n            summary: 'Error',\n            detail: response.results\n          });\n        }\n      },\n      error: (error: unknown) => {\n        this.messageService.add({\n          severity:'error',\n          summary: 'Error',\n          detail: 'Failed to process payment'\n        });\n      }\n    });\n  }\n\n  cancel() {\n    this.router.navigate(['/loan/schedule-payment']);\n  }\n\n  getPaymentLength(): number {\n    let totalLength = 0;\n    for (let i = 0; i < this.formdata.length; i++) {\n      if (this.formdata[i].dealerLevelFeeList != undefined) {\n        totalLength += this.formdata[i].dealerLevelFeeList.length;\n      }\n\n      if (this.formdata[i].dtoList != undefined) {\n        totalLength += this.formdata[i].dtoList.length;\n      }\n    }\n\n    return totalLength;\n  }\n\n  removeFee(dealer: any, feeItem: any) {\n    if (dealer && dealer.dealerLevelFeeList) {\n      const index = dealer.dealerLevelFeeList.findIndex((item: any) =>\n        item.description === feeItem.description &&\n        item.remainingAmount === feeItem.remainingAmount\n      );\n      if (index > -1) {\n        dealer.dealerLevelFeeList.splice(index, 1);\n        this.recalculateTotal(dealer);\n      }\n    }\n  }\n\n  private recalculateTotal(dealer: any) {\n    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total: number, fee: any) => {\n      return total + (fee.remainingAmount || 0);\n    }, 0);\n\n    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\n  }\n\n  // 处理仅本金支付的金额变更\n  onlyPrincipalAmountChange(item: any) {\n    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\n      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n        this.messageService.add({\n          severity:'warning',\n          summary: 'Warning',\n          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\n        });\n        item.onlyPrincialAmount = 0;\n      }\n    } else {\n      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n        this.messageService.add({\n          severity:'warning',\n          summary: 'Warning',\n          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\n        });\n        item.onlyPrincialAmount = 0;\n      }\n    }\n  }\n\n  // 打开Other Amount输入对话框\n  inputOtherAmount(item: any) {\n    this.selectedItem = item;\n    this.tempOtherAmount = item.otherAmount || 0;\n    this.showOtherAmountDialog = true;\n  }\n\n  // 取消Other Amount输入\n  cancelOtherAmount() {\n    this.showOtherAmountDialog = false;\n    this.selectedItem = null;\n    this.tempOtherAmount = 0;\n  }\n\n  // 确认Other Amount输入\n  confirmOtherAmount() {\n    if (this.selectedItem) {\n      this.selectedItem.otherAmount = this.tempOtherAmount;\n      if (this.tempOtherAmount !== 0) {\n        this.selectedItem.buttonName = 'OtherAmount';\n      } else {\n        this.selectedItem.buttonName = 'Curtailment';\n      }\n    }\n    this.showOtherAmountDialog = false;\n    this.selectedItem = null;\n    this.tempOtherAmount = 0;\n  }\n\n  // 获取最小日期\n  getMinDate(item: any): Date | null {\n    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\n  }\n\n  // 获取最大日期\n  getMaxDate(item: any): Date | null {\n    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\n  }\n\n  // Get minimum date for release date picker (based on title release date)\n  getReleaseDateMinDate(item: any): Date | null {\n    return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;\n  }\n\n  // Get maximum date for release date picker (based on title release date)\n  getReleaseDateMaxDate(item: any): Date | null {\n    return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;\n  }\n\n  // Add Contact Dialog\n  addContactDialog(item: any) {\n    this.selectedContactItem = item;\n    this.contactDialogMode = 'add';\n    this.contactForm = {\n      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\n      firstName: '',\n      lastName: '',\n      addressLine1: '',\n      addressLine2: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      phone: '',\n      email: ''\n    };\n    this.showContactDialog = true;\n  }\n\n  // Edit Contact Dialog\n  editContactDialog(contactInfo: any, item: any) {\n    this.selectedContactItem = item;\n    this.contactDialogMode = 'edit';\n    this.editingContact = contactInfo;\n    this.contactForm = {\n      contactReference: contactInfo.contactReference,\n      firstName: contactInfo.firstName,\n      lastName: contactInfo.lastName,\n      addressLine1: contactInfo.addressLine1,\n      addressLine2: contactInfo.addressLine2 || '',\n      city: contactInfo.city,\n      state: contactInfo.state,\n      zipCode: contactInfo.zipCode,\n      phone: contactInfo.phone,\n      email: contactInfo.email || ''\n    };\n    this.showContactDialog = true;\n  }\n\n  // Save Contact\n  saveContact() {\n    if (this.contactDialogMode === 'add') {\n      const newContact = { ...this.contactForm };\n      \n      // Add to contact list\n      if (this.selectedContactItem.contactDtoList) {\n        this.selectedContactItem.contactDtoList.push(newContact);\n      } else {\n        this.selectedContactItem.contactDtoList = [newContact];\n      }\n\n      // Set as current contact\n      this.selectedContactItem.contactInfo = newContact;\n\n      // Update all loans for same dealer\n      this.formdata.forEach((dealer: any) => {\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\n          dealer.dtoList?.forEach((loan: any) => {\n            if (loan.contactDtoList) {\n              loan.contactDtoList.push({ ...newContact });\n              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n                loan.isDisabledEdit = true;\n              }\n            }\n          });\n        }\n      });\n    } else {\n      // Edit mode\n      const updatedContact = { ...this.contactForm };\n      \n      // Update in all places\n      this.formdata.forEach((dealer: any) => {\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\n          dealer.dtoList?.forEach((loan: any) => {\n            if (loan.contactDtoList) {\n              // Update in contact list\n              const index = loan.contactDtoList.findIndex(\n                (c: any) => c.contactReference === this.editingContact.contactReference\n              );\n              if (index > -1) {\n                loan.contactDtoList[index] = updatedContact;\n              }\n\n              // Update current selection if matches\n              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\n                loan.contactInfo = updatedContact;\n              }\n            }\n          });\n        }\n      });\n    }\n\n    this.showContactDialog = false;\n    this.selectedContactItem = null;\n    this.editingContact = null;\n  }\n\n  // Cancel Contact Dialog\n  cancelContactDialog() {\n    if (this.contactDialogMode === 'add') {\n      this.tempContactNumber--;\n    }\n    this.showContactDialog = false;\n    this.selectedContactItem = null;\n    this.editingContact = null;\n  }\n\n  // Handle shipping contact change\n  shippingContactChange(item: any) {\n    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n      item.isDisabledEdit = true;\n    } else {\n      item.isDisabledEdit = false;\n    }\n  }\n\n  // Remove payoff item\n  removePayoff(dealer: any, item: any) {\n    if (dealer.dtoList) {\n      const index = dealer.dtoList.findIndex((dto: any) => dto === item);\n      if (index > -1) {\n        dealer.dtoList.splice(index, 1);\n      }\n    }\n  }\n\n  // View Title\n  viewTitle(item: any) {\n    if (item.fileManagementUrl) {\n      window.open(item.fileManagementUrl, '_blank');\n    }\n  }\n  \n}\n", "<div class=\"flex flex-column\">\n  <div class=\"flex justify-content-between align-items-center mb-4\">\n    <div class=\"flex\">Total: <span class=\"colorce3434 font-bold pl-2\">{{getTotal() | currency}}</span></div>\n    <div class=\"flex align-items-center gap-3\">\n      <button pButton pRipple type=\"button\" class=\"greyButton\" label=\"Cancel\" (click)=\"cancel()\"></button>\n      <button pButton pRipple type=\"button\" class=\"greenButton\" label=\"Submit\"\n        [disabled]=\"getPaymentLength() === 0\" (click)=\"pay()\"></button>\n    </div>\n  </div>\n  <div class=\"panel border-round p-3 text-sm flex flex-column\" *ngFor=\"let dealer of formdata\">\n    <div class=\"flex justify-content-between align-items-center\">\n      <div class=\"flex align-items-center flex-1\">\n        <div class=\"flex w-3\">\n          <div class=\"text-right color2B2E3A pr-2\">Dealer Code</div>\n          <div class=\" pl-2 color2B2E3A font-bold\">{{dealer.dealerCode}}</div>\n        </div>\n        <div class=\"flex w-3\">\n          <div class=\" text-right color2B2E3A pr-2\">Dealer Name</div>\n          <div class=\" pl-2 color2B2E3A font-bold\">{{dealer.dba}}</div>\n        </div>\n        <div class=\"flex w-3\">\n          <div class=\" text-right color2B2E3A pr-2\">Subtotal</div>\n          <div class=\" pl-2 font-bold colorce3434\">{{getSubTotal(dealer) | currency}}</div>\n        </div>\n        <div class=\"flex w-3 align-items-center\">\n          <div class=\" text-right color2B2E3A pr-2\">Bank Account</div>\n          <p-dropdown class=\"dropdownStyle pl-2 w-15rem\"\n                      [(ngModel)]=\"dealer.bankAccount\"\n                      [options]=\"dealer.bankAccountList\"\n                      placeholder=\"Select\"\n                      [optionLabel]=\"'reference'\"\n                      [optionValue]=\"'bankAccountDtoId'\"\n                      [filter]=\"true\"\n                      filterBy=\"reference\">\n            <ng-template pTemplate=\"selectedItem\" let-item>\n              {{item.bankAccountRanking}}.{{item.reference}}\n            </ng-template>\n            <ng-template pTemplate=\"item\" let-item>\n              <div class=\"text-ellipsis\">{{item.bankAccountRanking}}.{{item.reference}}</div>\n            </ng-template>\n          </p-dropdown>\n        </div>\n      </div>\n      <img [src]=\"!dealer.isExpanded ? './assets/img/upicon.png' : './assets/img/downicon.png'\" class=\"cursor-pointer\" (click)=\"toggleDealer(dealer)\">\n    </div>\n\n    <div *ngIf=\"dealer.isExpanded\">\n      <div class=\"panel border-round p-3 flex mt-3 color2B2E3A relative\" *ngFor=\"let feeItem of dealer.dealerLevelFeeList\">\n        <div class=\"absolute closeIcon z-1\">\n          <i class=\"pi pi-times-circle text-xl colorce3434 cursor-pointer\" (click)=\"removeFee(dealer, feeItem)\"></i>\n        </div>\n        <div class=\"flex w-full\">\n          <div class=\"flex w-4\">\n            <div class=\"text-right color2B2E3A pr-2\">Fee Name:</div>\n            <div class=\"pl-2\">{{feeItem.description}}</div>\n          </div>\n          <div class=\"flex w-8\">\n            <div class=\"text-right color2B2E3A pr-2\">Pay Amount:</div>\n            <div class=\"pl-2 colorce3434\">{{feeItem.remainingAmount | currency}}</div>\n          </div>\n        </div>\n      </div>\n\n    <div class=\"panel border-round p-3 flex mt-3 color2B2E3A relative\" *ngFor=\"let item of dealer.dtoList\">\n      <div class=\"absolute closeIcon z-1\">\n        <i class=\"pi pi-times-circle text-xl colorce3434 cursor-pointer\" (click)=\"removePayoff(dealer, item)\"></i>\n      </div>\n      \n      <!-- Vehicle Info -->\n      <div class=\"w-3 flex flex-column gap-3 p-3 border-right-1 border-color-AEB9CC\">\n        <div class=\"flex align-items-center gap-2 font-bold\"><i class=\"pi pi-car\"></i> Vehicle Info</div>\n        <div class=\"flex\">{{item.vin}}</div>\n        <div class=\"flex\">{{item.year}} {{item.make}} {{item.model}}</div>\n        <div class=\"flex align-items-center gap-3\">\n          <span class=\"color2B2E3A\">Due Date:</span>\n          <span class=\"color2B2E3A\">{{item.nextDueDate | date:'MM/dd/yy'}}</span>\n        </div>\n      </div>\n\n      <!-- Payment Detail -->\n      <div class=\"w-4 flex flex-column gap-2 py-3 px-5 border-right-1 border-color-AEB9CC\">\n        <div class=\"flex align-items-center gap-2 font-bold\"><i class=\"pi pi-book\"></i> Payment Detail</div>\n        \n        <!-- Payment Type and Amount -->\n        <div class=\"flex align-items-center justify-content-between gap-2\">\n          <div class=\"flex align-items-center gap-2 pl-3\">\n            <i class=\"pi pi-angle-down cursor-pointer\" *ngIf=\"!payoff\" (click)=\"showPayoff()\"></i>\n            <i class=\"pi pi-angle-up cursor-pointer\" *ngIf=\"payoff\" (click)=\"showPayoff()\"></i>\n            {{item.buttonName}}\n          </div>\n          <div>{{item.totalMoney | currency}}</div>\n        </div>\n\n        <!-- Payment Details Breakdown -->\n        <div class=\"flex flex-column trbg border-round-sm color82808F\" *ngIf=\"payoff\">\n          <div class=\"flex flex-column w-12 p-3 gap-2\">\n            <!-- Principal -->\n            <div class=\"flex justify-content-between align-items-center\">\n              <div>Principal</div>\n              <div>{{item.principal | currency}}</div>\n            </div>\n\n            <!-- Extra Amounts -->\n            <div class=\"flex justify-content-between align-items-center\" *ngFor=\"let fee of item.extraAmountList\">\n              <div>{{fee.feeName}}</div>\n              <div>{{fee.amount | currency}}</div>\n            </div>\n\n            <!-- Interest -->\n            <div class=\"flex justify-content-between align-items-center\">\n              <div>Interest</div>\n              <div class=\"flex align-items-center gap-2\">\n                <i class=\"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\"\n                   pTooltip=\"Estimated Amount\"\n                   tooltipPosition=\"top\"\n                   *ngIf=\"isShowEstimation\"></i>\n                {{item.interestPrice | currency}}\n              </div>\n            </div>\n\n            <!-- WIP -->\n            <div class=\"flex justify-content-between align-items-center\">\n              <div>WIP</div>\n              <div class=\"flex align-items-center gap-2\">\n                <i class=\"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\"\n                   pTooltip=\"Estimated Amount\"\n                   tooltipPosition=\"top\"\n                   *ngIf=\"isShowEstimation\"></i>\n                {{item.insurancePrice | currency}}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Additional Payment -->\n        <div class=\"flex justify-content-between align-items-center\" *ngIf=\"!item.isPayOff && !item.isPartialPayment\">\n          <div class=\"flex align-items-center gap-2 pl-3\">Additional Payment</div>\n          <div class=\"flex align-items-center gap-2\">\n            <span>{{item.otherAmount | currency}}</span>\n            <a class=\"text-primary cursor-pointer\" (click)=\"inputOtherAmount(item)\">Change</a>\n          </div>\n        </div>\n\n        <!-- Schedule Date -->\n        <div class=\"flex justify-content-between align-items-center\">\n          <div class=\"w-6 pl-3\">Schedule Date</div>\n          <div class=\"w-6\">\n            <p-calendar\n              [(ngModel)]=\"item.scheduleDate\"\n              (onSelect)=\"scheduleDateChange(item)\"\n              [selectOtherMonths]=\"true\"\n              [showButtonBar]=\"false\"\n              [monthNavigator]=\"true\"\n              [yearNavigator]=\"true\"\n              [dateFormat]=\"'mm/dd/yy'\"\n              [showTime]=\"false\"\n              [showIcon]=\"false\"\n              [readonlyInput]=\"true\"\n              [minDate]=\"getMinDate(item)\"\n              [maxDate]=\"getMaxDate(item)\"\n              [disabled]=\"item.isOnlyPrincial\"\n              class=\"calendarStyle w-full\">\n            </p-calendar>\n          </div>\n        </div>\n\n        <!-- Principal Only Section -->\n        <div class=\"flex flex-column\" *ngIf=\"item.buttonName === 'PayOff' && item.isShowPrincipal\">\n          <div class=\"w-12 flex gap-2 justify-content-between align-items-center\">\n            <div class=\"w-6\">Principal Only</div>\n            <p-checkbox\n              class=\"checkbox w-6\"\n              [(ngModel)]=\"item.isOnlyPrincial\"\n              [binary]=\"true\"\n              inputId=\"principalOnly\">\n            </p-checkbox>\n          </div>\n        </div>\n\n        <!-- Principal Only Payment -->\n        <div class=\"flex flex-column gap-2\" *ngIf=\"item.isOnlyPrincial\">\n          <div class=\"flex justify-content-between align-items-center\">\n            <div class=\"w-6\">Payment</div>\n            <div class=\"w-6\">\n              <p-inputGroup class=\"w-full\">\n                <p-inputGroupAddon>$</p-inputGroupAddon>\n                <p-inputNumber class=\"inputNumberRadius w-full\"\n                              inputId=\"onlyPrincialAmount\"\n                              mode=\"decimal\"\n                              [minFractionDigits]=\"2\"\n                              [maxFractionDigits]=\"2\"\n                              [(ngModel)]=\"item.onlyPrincialAmount\"\n                              [maxlength]=\"14\"\n                              (ngModelChange)=\"onlyPrincipalAmountChange(item)\">\n                </p-inputNumber>\n              </p-inputGroup>\n            </div>\n          </div>\n\n          <div class=\"flex justify-content-between align-items-center\">\n            <div class=\"w-6\">Schedule Date</div>\n            <div class=\"w-6\">\n              <p-calendar\n                [(ngModel)]=\"item.scheduleDate\"\n                [disabled]=\"true\"\n                [selectOtherMonths]=\"true\"\n                [showButtonBar]=\"true\"\n                [monthNavigator]=\"true\"\n                [yearNavigator]=\"true\"\n                [dateFormat]=\"'mm/dd/yy'\"\n                class=\"calendarStyle w-full\">\n              </p-calendar>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"w-5 flex flex-column gap-2 py-3 px-5\" *ngIf=\"item.displayMail && !item.isTrusted\">\n        <!-- Title Header -->\n        <div class=\"flex align-items-center gap-2 font-bold\">\n          <i class=\"pi pi-envelope\"></i>\n          <span>Title Shipping Info</span>\n          <button pButton\n                  type=\"button\"\n                  icon=\"pi pi-file\"\n                  class=\"p-button-rounded p-button-text ml-2\"\n                  [disabled]=\"!item.isHasTitleFile\"\n                  (click)=\"viewTitle(item)\">\n          </button>\n        </div>\n\n        <!-- Special Title Handling Checkbox -->\n        <div class=\"w-full\" *ngIf=\"holdSwitch\">\n          <p-checkbox\n            class=\"checkbox\"\n            [(ngModel)]=\"item.isHold\"\n            [binary]=\"true\"\n            [disabled]=\"item.isOnlyPrincial\"\n            label=\"Special Title Handling\">\n          </p-checkbox>\n        </div>\n\n        <!-- Special Title Handling Section -->\n        <div class=\"flex flex-column gap-2\" *ngIf=\"holdSwitch && item.isHold\">\n          <!-- Release Date -->\n          <div class=\"flex align-items-center\" *ngIf=\"item.holdType\">\n            <div class=\"w-4\">Release Date</div>\n            <div class=\"w-8\">\n              <div *ngIf=\"item.holdType.value === 'T' || item.holdType.value === 'H'\">\n                <p-calendar\n                  [(ngModel)]=\"item.titleReleaseDate\"\n                  [showButtonBar]=\"false\"\n                  [monthNavigator]=\"true\"\n                  [yearNavigator]=\"true\"\n                  [readonlyInput]=\"true\"\n                  [dateFormat]=\"'mm/dd/yy'\"\n                  [minDate]=\"getReleaseDateMinDate(item)\"\n                  [maxDate]=\"getReleaseDateMaxDate(item)\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  class=\"w-full\">\n                </p-calendar>\n              </div>\n              <div *ngIf=\"item.holdType.value === 'D'\" class=\"p-2\">\n                {{item.titleReleaseHoldDate | date:'MM/dd/yyyy'}}\n              </div>\n            </div>\n          </div>\n\n          <!-- Special Title Type -->\n          <div class=\"flex align-items-center\">\n            <div class=\"w-4\">Special Title Type</div>\n            <div class=\"w-8\">\n              <p-dropdown\n                [(ngModel)]=\"item.holdType\"\n                [options]=\"holdTypeList\"\n                optionLabel=\"text\"\n                [disabled]=\"item.isOnlyPrincial\"\n                placeholder=\"Select\"\n                class=\"w-full\">\n              </p-dropdown>\n            </div>\n          </div>\n\n          <!-- Shipping Method -->\n          <div class=\"flex align-items-center\" *ngIf=\"item.holdType\">\n            <div class=\"w-4\">Shipping Method</div>\n            <div class=\"w-8\">\n              <p-dropdown\n                [(ngModel)]=\"item.mailFeeInfo\"\n                [options]=\"postageFee\"\n                optionLabel=\"text\"\n                [disabled]=\"item.isOnlyPrincial\"\n                placeholder=\"Select\"\n                class=\"w-full\">\n              </p-dropdown>\n            </div>\n          </div>\n\n          <!-- UCC Provider Contact (Type T) -->\n          <div class=\"flex flex-column gap-2\" *ngIf=\"item.holdType?.value === 'T'\">\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Contact</div>\n              <div class=\"w-8\">\n                <p-dropdown\n                  [(ngModel)]=\"item.holdContactInfo\"\n                  [options]=\"item.uccProviderList\"\n                  optionLabel=\"uccProviderName\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  class=\"w-full\">\n                  <ng-template pTemplate=\"selectedItem\" let-selected>\n                    {{selected.uccProviderName}} - {{selected.address}}\n                  </ng-template>\n                  <ng-template pTemplate=\"item\" let-provider>\n                    {{provider.uccProviderName}} - {{provider.address}}\n                  </ng-template>\n                </p-dropdown>\n              </div>\n            </div>\n\n            <!-- UCC Provider Address -->\n            <div class=\"flex align-items-start my-2\" *ngIf=\"item.holdContactInfo?.uccProviderId\">\n              <div class=\"w-4\">Shipping Address</div>\n              <div class=\"w-8\">\n                <p class=\"m-0\">{{item.holdContactInfo.uccProviderName}} Title Dept</p>\n                <p class=\"m-0\">{{item.holdContactInfo.address}}</p>\n                <p class=\"m-0\" *ngIf=\"item.holdContactInfo\">\n                  {{item.holdContactInfo.city}}, {{item.holdContactInfo.state}}, {{item.holdContactInfo.zipCode}}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Dealer Contact (Type D or H) -->\n          <div class=\"flex flex-column gap-2\" *ngIf=\"item.holdType?.value === 'D' || item.holdType?.value === 'H'\">\n            <!-- Contact Selection -->\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Contact</div>\n              <div class=\"w-8\">\n                <p-dropdown\n                  [(ngModel)]=\"item.newContactInfo\"\n                  [options]=\"item.newContactDtoList\"\n                  optionLabel=\"contactName\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  (onChange)=\"shippingContactChange(item)\"\n                  class=\"w-full\">\n                </p-dropdown>\n              </div>\n            </div>\n\n            <!-- Location Selection -->\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Location</div>\n              <div class=\"w-8\">\n                <p-dropdown\n                  [(ngModel)]=\"item.newLocationInfo\"\n                  [options]=\"item.newLocationDtoList\"\n                  optionLabel=\"address1\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  (onChange)=\"shippingContactChange(item)\"\n                  class=\"w-full\">\n                </p-dropdown>\n              </div>\n            </div>\n\n            <!-- Address Display -->\n            <div class=\"flex align-items-start my-2\">\n              <div class=\"w-4\">Shipping Address</div>\n              <div class=\"w-8\">\n                <p class=\"m-0\">{{item.newContactInfo?.firstName}} {{item.newContactInfo?.lastName}}</p>\n                <p class=\"m-0\">{{item.newLocationInfo?.address1}}</p>\n                <p class=\"m-0\" *ngIf=\"item.newLocationInfo\">\n                  {{item.newLocationInfo.city}}, {{item.newLocationInfo.state}}, {{item.newLocationInfo.zipCode}}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Note -->\n          <div class=\"flex align-items-start\" *ngIf=\"item.holdType\">\n            <div class=\"w-4\">Note</div>\n            <div class=\"w-8\">\n              <textarea pInputTextarea\n                        [(ngModel)]=\"item.note\"\n                        [rows]=\"3\"\n                        [maxlength]=\"256\"\n                        class=\"w-full\">\n              </textarea>\n            </div>\n          </div>\n        </div>\n\n        <!-- Regular Shipping Section -->\n        <div class=\"flex flex-column gap-2\" *ngIf=\"!item.isHold || !holdSwitch\">\n          <!-- Release Date -->\n          <div class=\"flex align-items-center\">\n            <div class=\"w-4\">Release Date</div>\n            <div class=\"w-8\">\n              <p-calendar\n                [(ngModel)]=\"item.titleReleaseDate\"\n                [showButtonBar]=\"true\"\n                [monthNavigator]=\"true\"\n                [yearNavigator]=\"true\"\n                [readonlyInput]=\"true\"\n                [dateFormat]=\"'mm/dd/yy'\"\n                [minDate]=\"getReleaseDateMinDate(item)\"\n                [maxDate]=\"getReleaseDateMaxDate(item)\"\n                [disabled]=\"item.isOnlyPrincial\"\n                class=\"w-full\">\n              </p-calendar>\n            </div>\n            <div class=\"w-2 text-right\" *ngIf=\"!holdSwitch\">Hold</div>\n            <div class=\"w-2\" *ngIf=\"!holdSwitch\">\n              <p-checkbox\n                class=\"checkbox\"\n                [(ngModel)]=\"item.isHold\"\n                [binary]=\"true\"\n                [disabled]=\"item.isOnlyPrincial\">\n              </p-checkbox>\n            </div>\n          </div>\n\n          <!-- Shipping Method -->\n          <div class=\"flex align-items-center\">\n            <div class=\"w-4\">Shipping Method</div>\n            <div class=\"w-8\">\n              <p-dropdown\n                [(ngModel)]=\"item.mailFeeInfo\"\n                [options]=\"postageFee\"\n                optionLabel=\"text\"\n                [disabled]=\"item.isOnlyPrincial\"\n                placeholder=\"Select\"\n                class=\"w-full\">\n              </p-dropdown>\n            </div>\n          </div>\n\n          <!-- Regular Contact Info -->\n          <div class=\"flex flex-column gap-2\" *ngIf=\"!item.contactSwitch\">\n            <!-- Contact Selection -->\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Contact</div>\n              <div class=\"w-8 flex gap-2\">\n                <p-dropdown\n                  [(ngModel)]=\"item.contactInfo\"\n                  [options]=\"item.contactDtoList\"\n                  optionLabel=\"contactReference\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  (onChange)=\"shippingContactChange(item)\"\n                  class=\"w-full\">\n                </p-dropdown>\n                <button pButton\n                        type=\"button\"\n                        icon=\"pi pi-plus\"\n                        class=\"p-button-rounded p-button-text\"\n                        [disabled]=\"item.isOnlyPrincial\"\n                        (click)=\"addContactDialog(item)\">\n                </button>\n                <button pButton\n                        type=\"button\"\n                        icon=\"pi pi-pencil\"\n                        class=\"p-button-rounded p-button-text\"\n                        [disabled]=\"!item.isDisabledEdit || item.isOnlyPrincial\"\n                        (click)=\"editContactDialog(item.contactInfo, item)\">\n                </button>\n              </div>\n            </div>\n\n            <!-- Address Display -->\n            <div class=\"flex align-items-start my-2\">\n              <div class=\"w-4\">Shipping Address</div>\n              <div class=\"w-8\">\n                <p class=\"m-0\">{{item.contactInfo?.firstName}} {{item.contactInfo?.lastName}}</p>\n                <p class=\"m-0\">{{item.contactInfo?.addressLine1}}</p>\n                <p class=\"m-0\">{{item.contactInfo?.city}}, {{item.contactInfo?.state}}, {{item.contactInfo?.zipCode}}</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- New Contact Info -->\n          <div class=\"flex flex-column gap-2\" *ngIf=\"item.contactSwitch\">\n            <!-- Contact Selection -->\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Contact</div>\n              <div class=\"w-8\">\n                <p-dropdown\n                  [(ngModel)]=\"item.newContactInfo\"\n                  [options]=\"item.newContactDtoList\"\n                  optionLabel=\"contactName\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  (onChange)=\"shippingContactChange(item)\"\n                  class=\"w-full\">\n                </p-dropdown>\n              </div>\n            </div>\n\n            <!-- Location Selection -->\n            <div class=\"flex align-items-center\">\n              <div class=\"w-4\">Shipping Location</div>\n              <div class=\"w-8\">\n                <p-dropdown\n                  [(ngModel)]=\"item.newLocationInfo\"\n                  [options]=\"item.newLocationDtoList\"\n                  optionLabel=\"address1\"\n                  [disabled]=\"item.isOnlyPrincial\"\n                  placeholder=\"Select\"\n                  (onChange)=\"shippingContactChange(item)\"\n                  class=\"w-full\">\n                </p-dropdown>\n              </div>\n            </div>\n\n            <!-- Address Display -->\n            <div class=\"flex align-items-start my-2\">\n              <div class=\"w-4\">Shipping Address</div>\n              <div class=\"w-8\">\n                <p class=\"m-0\">{{item.newContactInfo?.firstName}} {{item.newContactInfo?.lastName}}</p>\n                <p class=\"m-0\">{{item.newLocationInfo?.address1}}</p>\n                <p class=\"m-0\" *ngIf=\"item.newLocationInfo\">\n                  {{item.newLocationInfo.city}}, {{item.newLocationInfo.state}}, {{item.newLocationInfo.zipCode}}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"item.displayMail && item.isTrusted\" class=\"w-5 flex flex-column gap-2 py-3 px-5\">\n        <div>Title Released</div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Other Amount Dialog -->\n<p-dialog\n  [(visible)]=\"showOtherAmountDialog\"\n  [modal]=\"true\"\n  [draggable]=\"false\"\n  [resizable]=\"false\"\n  [style]=\"{width: '30vw'}\"\n  header=\"Other Amount\"\n  [closeOnEscape]=\"false\"\n  [closable]=\"false\">\n  <div class=\"flex flex-column gap-3 p-3\">\n    <div class=\"flex justify-content-between align-items-center\">\n      <label for=\"otherAmount\">Amount</label>\n      <p-inputNumber\n        id=\"otherAmount\"\n        [(ngModel)]=\"tempOtherAmount\"\n        mode=\"currency\"\n        currency=\"USD\"\n        [minFractionDigits]=\"2\"\n        [maxFractionDigits]=\"2\"\n        [maxlength]=\"14\"\n        [max]=\"selectedItem?.otherAmountLimit\">\n      </p-inputNumber>\n    </div>\n  </div>\n  <ng-template pTemplate=\"footer\">\n    <div class=\"flex justify-content-end gap-2\">\n      <button pButton pRipple type=\"button\" label=\"Cancel\" class=\"greyButton\" (click)=\"cancelOtherAmount()\"></button>\n      <button pButton pRipple type=\"button\" label=\"Confirm\" class=\"greenButton\" (click)=\"confirmOtherAmount()\"></button>\n    </div>\n  </ng-template>\n</p-dialog>\n\n<!-- Contact Dialog -->\n<p-dialog\n  [(visible)]=\"showContactDialog\"\n  [modal]=\"true\"\n  [draggable]=\"false\"\n  [resizable]=\"false\"\n  [style]=\"{width: '45vw'}\"\n  [header]=\"contactDialogMode === 'add' ? 'Add Contact' : 'Edit Contact'\"\n  [closeOnEscape]=\"false\"\n  [closable]=\"false\">\n  <div class=\"flex flex-column gap-3 p-3\">\n    <!-- Contact Form -->\n    <div class=\"flex flex-column gap-3\">\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"firstName\">First Name *</label>\n        <input pInputText id=\"firstName\" [(ngModel)]=\"contactForm.firstName\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"lastName\">Last Name *</label>\n        <input pInputText id=\"lastName\" [(ngModel)]=\"contactForm.lastName\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"addressLine1\">Address Line 1 *</label>\n        <input pInputText id=\"addressLine1\" [(ngModel)]=\"contactForm.addressLine1\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"addressLine2\">Address Line 2</label>\n        <input pInputText id=\"addressLine2\" [(ngModel)]=\"contactForm.addressLine2\" />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"city\">City *</label>\n        <input pInputText id=\"city\" [(ngModel)]=\"contactForm.city\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"state\">State *</label>\n        <p-dropdown id=\"state\"\n                    [(ngModel)]=\"contactForm.state\"\n                    [options]=\"stateList\"\n                    optionLabel=\"text\"\n                    optionValue=\"value\"\n                    placeholder=\"Select\"\n                    required>\n        </p-dropdown>\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"zipCode\">Zip Code *</label>\n        <input pInputText id=\"zipCode\" [(ngModel)]=\"contactForm.zipCode\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"phone\">Phone *</label>\n        <input pInputText id=\"phone\" [(ngModel)]=\"contactForm.phone\" required />\n      </div>\n\n      <div class=\"flex justify-content-between align-items-center\">\n        <label for=\"email\">Email</label>\n        <input pInputText id=\"email\" [(ngModel)]=\"contactForm.email\" />\n      </div>\n    </div>\n  </div>\n\n  <ng-template pTemplate=\"footer\">\n    <div class=\"flex justify-content-end gap-2\">\n      <button pButton pRipple type=\"button\" label=\"Cancel\" class=\"greyButton\" (click)=\"cancelContactDialog()\"></button>\n      <button pButton pRipple type=\"button\" label=\"Save\" class=\"greenButton\" (click)=\"saveContact()\"></button>\n    </div>\n  </ng-template>\n</p-dialog>\n"], "mappings": "AAEA,OAAOA,MAAM,MAAM,QAAQ;AAE3B,SAASC,cAAc,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;IC+B9BC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,OAAA,CAAAC,kBAAA,OAAAD,OAAA,CAAAE,SAAA,MACF;;;;;IAEEL,EAAA,CAAAM,cAAA,cAA2B;IAAAN,EAAA,CAAAC,MAAA,GAA8C;IAAAD,EAAA,CAAAO,YAAA,EAAM;;;;IAApDP,EAAA,CAAAQ,SAAA,EAA8C;IAA9CR,EAAA,CAAAE,kBAAA,KAAAO,OAAA,CAAAL,kBAAA,OAAAK,OAAA,CAAAJ,SAAA,KAA8C;;;;;;IAW7EL,EAFJ,CAAAM,cAAA,cAAqH,cAC/E,YACoE;IAArCN,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,UAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAE,SAAA,CAAAJ,SAAA,EAAAJ,UAAA,CAA0B;IAAA,EAAC;IACvGZ,EADwG,CAAAO,YAAA,EAAI,EACtG;IAGFP,EAFJ,CAAAM,cAAA,cAAyB,cACD,cACqB;IAAAN,EAAA,CAAAC,MAAA,gBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACxDP,EAAA,CAAAM,cAAA,cAAkB;IAAAN,EAAA,CAAAC,MAAA,GAAuB;IAC3CD,EAD2C,CAAAO,YAAA,EAAM,EAC3C;IAEJP,EADF,CAAAM,cAAA,cAAsB,eACqB;IAAAN,EAAA,CAAAC,MAAA,mBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1DP,EAAA,CAAAM,cAAA,eAA8B;IAAAN,EAAA,CAAAC,MAAA,IAAsC;;IAG1ED,EAH0E,CAAAO,YAAA,EAAM,EACtE,EACF,EACF;;;;IAPkBP,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAqB,iBAAA,CAAAT,UAAA,CAAAU,WAAA,CAAuB;IAIXtB,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,QAAAX,UAAA,CAAAY,eAAA,EAAsC;;;;;;IA4BpExB,EAAA,CAAAM,cAAA,YAAkF;IAAvBN,EAAA,CAAAU,UAAA,mBAAAe,0EAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAa,IAAA;MAAA,MAAAR,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAS,UAAA,EAAY;IAAA,EAAC;IAAC3B,EAAA,CAAAO,YAAA,EAAI;;;;;;IACtFP,EAAA,CAAAM,cAAA,YAA+E;IAAvBN,EAAA,CAAAU,UAAA,mBAAAkB,0EAAA;MAAA5B,EAAA,CAAAa,aAAA,CAAAgB,IAAA;MAAA,MAAAX,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAS,UAAA,EAAY;IAAA,EAAC;IAAC3B,EAAA,CAAAO,YAAA,EAAI;;;;;IAiBjFP,EADF,CAAAM,cAAA,cAAsG,UAC/F;IAAAN,EAAA,CAAAC,MAAA,GAAe;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1BP,EAAA,CAAAM,cAAA,UAAK;IAAAN,EAAA,CAAAC,MAAA,GAAyB;;IAChCD,EADgC,CAAAO,YAAA,EAAM,EAChC;;;;IAFCP,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAqB,iBAAA,CAAAS,OAAA,CAAAC,OAAA,CAAe;IACf/B,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAO,OAAA,CAAAE,MAAA,EAAyB;;;;;IAO5BhC,EAAA,CAAAiC,SAAA,YAGgC;;;;;IAShCjC,EAAA,CAAAiC,SAAA,YAGgC;;;;;IA7BlCjC,EAJN,CAAAM,cAAA,cAA8E,cAC/B,cAEkB,UACtD;IAAAN,EAAA,CAAAC,MAAA,gBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACpBP,EAAA,CAAAM,cAAA,UAAK;IAAAN,EAAA,CAAAC,MAAA,GAA6B;;IACpCD,EADoC,CAAAO,YAAA,EAAM,EACpC;IAGNP,EAAA,CAAAkC,UAAA,IAAAC,8DAAA,kBAAsG;IAOpGnC,EADF,CAAAM,cAAA,cAA6D,WACtD;IAAAN,EAAA,CAAAC,MAAA,gBAAQ;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACnBP,EAAA,CAAAM,cAAA,eAA2C;IACzCN,EAAA,CAAAkC,UAAA,KAAAE,6DAAA,gBAG4B;IAC5BpC,EAAA,CAAAC,MAAA,IACF;;IACFD,EADE,CAAAO,YAAA,EAAM,EACF;IAIJP,EADF,CAAAM,cAAA,eAA6D,WACtD;IAAAN,EAAA,CAAAC,MAAA,WAAG;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACdP,EAAA,CAAAM,cAAA,eAA2C;IACzCN,EAAA,CAAAkC,UAAA,KAAAG,6DAAA,gBAG4B;IAC5BrC,EAAA,CAAAC,MAAA,IACF;;IAGND,EAHM,CAAAO,YAAA,EAAM,EACF,EACF,EACF;;;;;IAjCKP,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAe,OAAA,CAAAC,SAAA,EAA6B;IAIyCvC,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAwC,UAAA,YAAAF,OAAA,CAAAG,eAAA,CAAuB;IAY5FzC,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAwB,gBAAA,CAAsB;IAC1B1C,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAAuB,WAAA,QAAAe,OAAA,CAAAM,aAAA,OACF;IAUM5C,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAwB,gBAAA,CAAsB;IAC1B1C,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAAuB,WAAA,SAAAe,OAAA,CAAAO,cAAA,OACF;;;;;;IAOJ7C,EADF,CAAAM,cAAA,cAA8G,cAC5D;IAAAN,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEtEP,EADF,CAAAM,cAAA,cAA2C,WACnC;IAAAN,EAAA,CAAAC,MAAA,GAA+B;;IAAAD,EAAA,CAAAO,YAAA,EAAO;IAC5CP,EAAA,CAAAM,cAAA,YAAwE;IAAjCN,EAAA,CAAAU,UAAA,mBAAAoC,4EAAA;MAAA9C,EAAA,CAAAa,aAAA,CAAAkC,IAAA;MAAA,MAAAT,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA8B,gBAAA,CAAAV,OAAA,CAAsB;IAAA,EAAC;IAACtC,EAAA,CAAAC,MAAA,aAAM;IAElFD,EAFkF,CAAAO,YAAA,EAAI,EAC9E,EACF;;;;IAHIP,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAe,OAAA,CAAAW,WAAA,EAA+B;;;;;;IA+BrCjD,EAFJ,CAAAM,cAAA,aAA2F,cACjB,cACrD;IAAAN,EAAA,CAAAC,MAAA,qBAAc;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACrCP,EAAA,CAAAM,cAAA,qBAI0B;IAFxBN,EAAA,CAAAkD,gBAAA,2BAAAC,6FAAAC,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAwC,IAAA;MAAA,MAAAf,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAiB,cAAA,EAAAH,MAAA,MAAAd,OAAA,CAAAiB,cAAA,GAAAH,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAiC;IAKvCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;IALAP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAiB,cAAA,CAAiC;IACjCvD,EAAA,CAAAwC,UAAA,gBAAe;;;;;;IASjBxC,EAFJ,CAAAM,cAAA,cAAgE,cACD,cAC1C;IAAAN,EAAA,CAAAC,MAAA,cAAO;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAG1BP,EAFJ,CAAAM,cAAA,cAAiB,uBACc,wBACR;IAAAN,EAAA,CAAAC,MAAA,QAAC;IAAAD,EAAA,CAAAO,YAAA,EAAoB;IACxCP,EAAA,CAAAM,cAAA,wBAOgE;IAFlDN,EAAA,CAAAkD,gBAAA,2BAAAO,gGAAAL,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAqB,kBAAA,EAAAP,MAAA,MAAAd,OAAA,CAAAqB,kBAAA,GAAAP,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAqC;IAErCpD,EAAA,CAAAU,UAAA,2BAAA+C,gGAAA;MAAAzD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAiBD,MAAA,CAAA0C,yBAAA,CAAAtB,OAAA,CAA+B;IAAA,EAAC;IAIrEtC,EAHM,CAAAO,YAAA,EAAgB,EACH,EACX,EACF;IAGJP,EADF,CAAAM,cAAA,cAA6D,eAC1C;IAAAN,EAAA,CAAAC,MAAA,qBAAa;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAElCP,EADF,CAAAM,cAAA,eAAiB,sBASgB;IAP7BN,EAAA,CAAAkD,gBAAA,2BAAAW,8FAAAT,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAwB,YAAA,EAAAV,MAAA,MAAAd,OAAA,CAAAwB,YAAA,GAAAV,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA+B;IAWvCpD,EAHM,CAAAO,YAAA,EAAa,EACT,EACF,EACF;;;;IAzBgBP,EAAA,CAAAQ,SAAA,GAAuB;IACvBR,EADA,CAAAwC,UAAA,wBAAuB,wBACA;IACvBxC,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAqB,kBAAA,CAAqC;IACrC3D,EAAA,CAAAwC,UAAA,iBAAgB;IAW9BxC,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAwB,YAAA,CAA+B;IAM/B9D,EALA,CAAAwC,UAAA,kBAAiB,2BACS,uBACJ,wBACC,uBACD,0BACG;;;;;;IAuB/BxC,EADF,CAAAM,cAAA,cAAuC,qBAMJ;IAH/BN,EAAA,CAAAkD,gBAAA,2BAAAa,mGAAAX,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAmD,IAAA;MAAA,MAAA1B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA2B,MAAA,EAAAb,MAAA,MAAAd,OAAA,CAAA2B,MAAA,GAAAb,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAyB;IAK7BpD,EADE,CAAAO,YAAA,EAAa,EACT;;;;IALFP,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA2B,MAAA,CAAyB;IAEzBjE,EADA,CAAAwC,UAAA,gBAAe,aAAAF,OAAA,CAAAiB,cAAA,CACiB;;;;;;IAY5BvD,EADF,CAAAM,cAAA,UAAwE,sBAWrD;IATfN,EAAA,CAAAkD,gBAAA,2BAAAgB,+GAAAd,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAsD,IAAA;MAAA,MAAA7B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA8B,gBAAA,EAAAhB,MAAA,MAAAd,OAAA,CAAA8B,gBAAA,GAAAhB,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAmC;IAWvCpD,EADE,CAAAO,YAAA,EAAa,EACT;;;;;IAXFP,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA8B,gBAAA,CAAmC;IAQnCpE,EAPA,CAAAwC,UAAA,wBAAuB,wBACA,uBACD,uBACA,0BACG,YAAAtB,MAAA,CAAAmD,qBAAA,CAAA/B,OAAA,EACc,YAAApB,MAAA,CAAAoD,qBAAA,CAAAhC,OAAA,EACA,aAAAA,OAAA,CAAAiB,cAAA,CACP;;;;;IAIpCvD,EAAA,CAAAM,cAAA,eAAqD;IACnDN,EAAA,CAAAC,MAAA,GACF;;IAAAD,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAAuE,WAAA,OAAAjC,OAAA,CAAAkC,oBAAA,qBACF;;;;;IAlBFxE,EADF,CAAAM,cAAA,cAA2D,cACxC;IAAAN,EAAA,CAAAC,MAAA,mBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACnCP,EAAA,CAAAM,cAAA,cAAiB;IAefN,EAdA,CAAAkC,UAAA,IAAAuC,0EAAA,kBAAwE,IAAAC,0EAAA,mBAcnB;IAIzD1E,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAlBIP,EAAA,CAAAQ,SAAA,GAAgE;IAAhER,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAqC,QAAA,CAAAC,KAAA,YAAAtC,OAAA,CAAAqC,QAAA,CAAAC,KAAA,SAAgE;IAchE5E,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAqC,QAAA,CAAAC,KAAA,SAAiC;;;;;;IAuBzC5E,EADF,CAAAM,cAAA,cAA2D,cACxC;IAAAN,EAAA,CAAAC,MAAA,sBAAe;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEpCP,EADF,CAAAM,cAAA,cAAiB,qBAOE;IALfN,EAAA,CAAAkD,gBAAA,2BAAA2B,yGAAAzB,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAiE,IAAA;MAAA,MAAAxC,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAyC,WAAA,EAAA3B,MAAA,MAAAd,OAAA,CAAAyC,WAAA,GAAA3B,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA8B;IAQpCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;;IARAP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAyC,WAAA,CAA8B;IAG9B/E,EAFA,CAAAwC,UAAA,YAAAtB,MAAA,CAAA8D,UAAA,CAAsB,aAAA1C,OAAA,CAAAiB,cAAA,CAEU;;;;;IAoB5BvD,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAA+E,YAAA,CAAAC,eAAA,SAAAD,YAAA,CAAAE,OAAA,MACF;;;;;IAEEnF,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAkF,YAAA,CAAAF,eAAA,SAAAE,YAAA,CAAAD,OAAA,MACF;;;;;IAWFnF,EAAA,CAAAM,cAAA,aAA4C;IAC1CN,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAO,YAAA,EAAI;;;;IADFP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAqF,kBAAA,MAAA/C,OAAA,CAAAgD,eAAA,CAAAC,IAAA,QAAAjD,OAAA,CAAAgD,eAAA,CAAAE,KAAA,QAAAlD,OAAA,CAAAgD,eAAA,CAAAG,OAAA,MACF;;;;;IANFzF,EADF,CAAAM,cAAA,eAAqF,cAClE;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,cAAiB,aACA;IAAAN,EAAA,CAAAC,MAAA,GAAmD;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACtEP,EAAA,CAAAM,cAAA,aAAe;IAAAN,EAAA,CAAAC,MAAA,GAAgC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACnDP,EAAA,CAAAkC,UAAA,IAAAwD,8EAAA,iBAA4C;IAIhD1F,EADE,CAAAO,YAAA,EAAM,EACF;;;;IANaP,EAAA,CAAAQ,SAAA,GAAmD;IAAnDR,EAAA,CAAA2C,kBAAA,KAAAL,OAAA,CAAAgD,eAAA,CAAAJ,eAAA,gBAAmD;IACnDlF,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAAgD,eAAA,CAAAH,OAAA,CAAgC;IAC/BnF,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAgD,eAAA,CAA0B;;;;;;IAzB5CtF,EAFJ,CAAAM,cAAA,cAAyE,cAClC,cAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,cAAiB,sBAOE;IALfN,EAAA,CAAAkD,gBAAA,2BAAAyC,yGAAAvC,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA+E,IAAA;MAAA,MAAAtD,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAgD,eAAA,EAAAlC,MAAA,MAAAd,OAAA,CAAAgD,eAAA,GAAAlC,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAkC;IASlCpD,EAHA,CAAAkC,UAAA,IAAA2D,kFAAA,0BAAmD,IAAAC,kFAAA,0BAGR;IAKjD9F,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAGNP,EAAA,CAAAkC,UAAA,IAAA6D,0EAAA,mBAAqF;IAUvF/F,EAAA,CAAAO,YAAA,EAAM;;;;IA3BEP,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAgD,eAAA,CAAkC;IAGlCtF,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAA0D,eAAA,CAAgC,aAAA1D,OAAA,CAAAiB,cAAA,CAEA;IAcIvD,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAgD,eAAA,kBAAAhD,OAAA,CAAAgD,eAAA,CAAAW,aAAA,CAAyC;;;;;IAoD/EjG,EAAA,CAAAM,cAAA,aAA4C;IAC1CN,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAO,YAAA,EAAI;;;;IADFP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAqF,kBAAA,MAAA/C,OAAA,CAAA4D,eAAA,CAAAX,IAAA,QAAAjD,OAAA,CAAA4D,eAAA,CAAAV,KAAA,QAAAlD,OAAA,CAAA4D,eAAA,CAAAT,OAAA,MACF;;;;;;IAtCFzF,EAHJ,CAAAM,cAAA,cAAyG,cAElE,cAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,cAAiB,sBAQE;IANfN,EAAA,CAAAkD,gBAAA,2BAAAiD,yGAAA/C,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAuF,IAAA;MAAA,MAAA9D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA+D,cAAA,EAAAjD,MAAA,MAAAd,OAAA,CAAA+D,cAAA,GAAAjD,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAiC;IAKjCpD,EAAA,CAAAU,UAAA,sBAAA4F,oGAAA;MAAAtG,EAAA,CAAAa,aAAA,CAAAuF,IAAA;MAAA,MAAA9D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqF,qBAAA,CAAAjE,OAAA,CAA2B;IAAA,EAAC;IAI9CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAIJP,EADF,CAAAM,cAAA,cAAqC,cAClB;IAAAN,EAAA,CAAAC,MAAA,wBAAiB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEtCP,EADF,CAAAM,cAAA,cAAiB,uBAQE;IANfN,EAAA,CAAAkD,gBAAA,2BAAAsD,0GAAApD,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAuF,IAAA;MAAA,MAAA9D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA4D,eAAA,EAAA9C,MAAA,MAAAd,OAAA,CAAA4D,eAAA,GAAA9C,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAkC;IAKlCpD,EAAA,CAAAU,UAAA,sBAAA+F,qGAAA;MAAAzG,EAAA,CAAAa,aAAA,CAAAuF,IAAA;MAAA,MAAA9D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqF,qBAAA,CAAAjE,OAAA,CAA2B;IAAA,EAAC;IAI9CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAIJP,EADF,CAAAM,cAAA,gBAAyC,eACtB;IAAAN,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,eAAiB,cACA;IAAAN,EAAA,CAAAC,MAAA,IAAoE;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACvFP,EAAA,CAAAM,cAAA,cAAe;IAAAN,EAAA,CAAAC,MAAA,IAAkC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACrDP,EAAA,CAAAkC,UAAA,KAAAwE,yEAAA,iBAA4C;IAKlD1G,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;IAtCEP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA+D,cAAA,CAAiC;IAGjCrG,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAAqE,iBAAA,CAAkC,aAAArE,OAAA,CAAAiB,cAAA,CAEF;IAahCvD,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA4D,eAAA,CAAkC;IAGlClG,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAAsE,kBAAA,CAAmC,aAAAtE,OAAA,CAAAiB,cAAA,CAEH;IAYnBvD,EAAA,CAAAQ,SAAA,GAAoE;IAApER,EAAA,CAAAE,kBAAA,KAAAoC,OAAA,CAAA+D,cAAA,kBAAA/D,OAAA,CAAA+D,cAAA,CAAAQ,SAAA,OAAAvE,OAAA,CAAA+D,cAAA,kBAAA/D,OAAA,CAAA+D,cAAA,CAAAS,QAAA,KAAoE;IACpE9G,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA4D,eAAA,kBAAA5D,OAAA,CAAA4D,eAAA,CAAAa,QAAA,CAAkC;IACjC/G,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA4D,eAAA,CAA0B;;;;;;IAS9ClG,EADF,CAAAM,cAAA,eAA0D,cACvC;IAAAN,EAAA,CAAAC,MAAA,WAAI;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEzBP,EADF,CAAAM,cAAA,cAAiB,oBAKU;IAHfN,EAAA,CAAAkD,gBAAA,2BAAA8D,wGAAA5D,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAoG,IAAA;MAAA,MAAA3E,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA4E,IAAA,EAAA9D,MAAA,MAAAd,OAAA,CAAA4E,IAAA,GAAA9D,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAuB;IAIjCpD,EAAA,CAAAC,MAAA;IAEJD,EAFI,CAAAO,YAAA,EAAW,EACP,EACF;;;;IANQP,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA4E,IAAA,CAAuB;IAEvBlH,EADA,CAAAwC,UAAA,WAAU,kBACO;;;;;;IAhJjCxC,EAAA,CAAAM,cAAA,cAAsE;IAEpEN,EAAA,CAAAkC,UAAA,IAAAiF,oEAAA,kBAA2D;IAyBzDnH,EADF,CAAAM,cAAA,cAAqC,cAClB;IAAAN,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEvCP,EADF,CAAAM,cAAA,cAAiB,qBAOE;IALfN,EAAA,CAAAkD,gBAAA,2BAAAkE,mGAAAhE,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAwG,IAAA;MAAA,MAAA/E,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAqC,QAAA,EAAAvB,MAAA,MAAAd,OAAA,CAAAqC,QAAA,GAAAvB,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA2B;IAQjCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAoGNP,EAjGA,CAAAkC,UAAA,IAAAoF,oEAAA,kBAA2D,IAAAC,oEAAA,kBAec,IAAAC,oEAAA,oBAmCgC,KAAAC,qEAAA,kBA+C/C;IAW5DzH,EAAA,CAAAO,YAAA,EAAM;;;;;IAnJkCP,EAAA,CAAAQ,SAAA,EAAmB;IAAnBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAqC,QAAA,CAAmB;IA4BnD3E,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAqC,QAAA,CAA2B;IAG3B3E,EAFA,CAAAwC,UAAA,YAAAtB,MAAA,CAAAwG,YAAA,CAAwB,aAAApF,OAAA,CAAAiB,cAAA,CAEQ;IAQAvD,EAAA,CAAAQ,SAAA,EAAmB;IAAnBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAqC,QAAA,CAAmB;IAepB3E,EAAA,CAAAQ,SAAA,EAAkC;IAAlCR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAAqC,QAAA,kBAAArC,OAAA,CAAAqC,QAAA,CAAAC,KAAA,UAAkC;IAmClC5E,EAAA,CAAAQ,SAAA,EAAkE;IAAlER,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAAqC,QAAA,kBAAArC,OAAA,CAAAqC,QAAA,CAAAC,KAAA,cAAAtC,OAAA,CAAAqC,QAAA,kBAAArC,OAAA,CAAAqC,QAAA,CAAAC,KAAA,UAAkE;IA+ClE5E,EAAA,CAAAQ,SAAA,EAAmB;IAAnBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAqC,QAAA,CAAmB;;;;;IAgCtD3E,EAAA,CAAAM,cAAA,eAAgD;IAAAN,EAAA,CAAAC,MAAA,WAAI;IAAAD,EAAA,CAAAO,YAAA,EAAM;;;;;;IAExDP,EADF,CAAAM,cAAA,eAAqC,sBAKA;IAFjCN,EAAA,CAAAkD,gBAAA,2BAAAyE,yGAAAvE,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA+G,IAAA;MAAA,MAAAtF,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA2B,MAAA,EAAAb,MAAA,MAAAd,OAAA,CAAA2B,MAAA,GAAAb,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAyB;IAI7BpD,EADE,CAAAO,YAAA,EAAa,EACT;;;;IAJFP,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA2B,MAAA,CAAyB;IAEzBjE,EADA,CAAAwC,UAAA,gBAAe,aAAAF,OAAA,CAAAiB,cAAA,CACiB;;;;;;IAwBlCvD,EAHJ,CAAAM,cAAA,cAAgE,cAEzB,cAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,eAA4B,sBAQT;IANfN,EAAA,CAAAkD,gBAAA,2BAAA2E,0GAAAzE,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAiH,IAAA;MAAA,MAAAxF,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAyF,WAAA,EAAA3E,MAAA,MAAAd,OAAA,CAAAyF,WAAA,GAAA3E,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA8B;IAK9BpD,EAAA,CAAAU,UAAA,sBAAAsH,qGAAA;MAAAhI,EAAA,CAAAa,aAAA,CAAAiH,IAAA;MAAA,MAAAxF,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqF,qBAAA,CAAAjE,OAAA,CAA2B;IAAA,EAAC;IAE1CtC,EAAA,CAAAO,YAAA,EAAa;IACbP,EAAA,CAAAM,cAAA,kBAKyC;IAAjCN,EAAA,CAAAU,UAAA,mBAAAuH,8FAAA;MAAAjI,EAAA,CAAAa,aAAA,CAAAiH,IAAA;MAAA,MAAAxF,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAgH,gBAAA,CAAA5F,OAAA,CAAsB;IAAA,EAAC;IACxCtC,EAAA,CAAAO,YAAA,EAAS;IACTP,EAAA,CAAAM,cAAA,kBAK4D;IAApDN,EAAA,CAAAU,UAAA,mBAAAyH,8FAAA;MAAAnI,EAAA,CAAAa,aAAA,CAAAiH,IAAA;MAAA,MAAAxF,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAkH,iBAAA,CAAA9F,OAAA,CAAAyF,WAAA,EAAAzF,OAAA,CAAyC;IAAA,EAAC;IAG/DtC,EAFI,CAAAO,YAAA,EAAS,EACL,EACF;IAIJP,EADF,CAAAM,cAAA,eAAyC,cACtB;IAAAN,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,eAAiB,cACA;IAAAN,EAAA,CAAAC,MAAA,IAA8D;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACjFP,EAAA,CAAAM,cAAA,cAAe;IAAAN,EAAA,CAAAC,MAAA,IAAkC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACrDP,EAAA,CAAAM,cAAA,cAAe;IAAAN,EAAA,CAAAC,MAAA,IAAsF;IAG3GD,EAH2G,CAAAO,YAAA,EAAI,EACrG,EACF,EACF;;;;IAlCEP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAyF,WAAA,CAA8B;IAG9B/H,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAA+F,cAAA,CAA+B,aAAA/F,OAAA,CAAAiB,cAAA,CAEC;IAS1BvD,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAwC,UAAA,aAAAF,OAAA,CAAAiB,cAAA,CAAgC;IAOhCvD,EAAA,CAAAQ,SAAA,EAAwD;IAAxDR,EAAA,CAAAwC,UAAA,cAAAF,OAAA,CAAAgG,cAAA,IAAAhG,OAAA,CAAAiB,cAAA,CAAwD;IAUjDvD,EAAA,CAAAQ,SAAA,GAA8D;IAA9DR,EAAA,CAAAE,kBAAA,KAAAoC,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAlB,SAAA,OAAAvE,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAjB,QAAA,KAA8D;IAC9D9G,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAQ,YAAA,CAAkC;IAClCvI,EAAA,CAAAQ,SAAA,GAAsF;IAAtFR,EAAA,CAAAqF,kBAAA,KAAA/C,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAxC,IAAA,QAAAjD,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAvC,KAAA,QAAAlD,OAAA,CAAAyF,WAAA,kBAAAzF,OAAA,CAAAyF,WAAA,CAAAtC,OAAA,KAAsF;;;;;IA6CrGzF,EAAA,CAAAM,cAAA,aAA4C;IAC1CN,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAO,YAAA,EAAI;;;;IADFP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAqF,kBAAA,MAAA/C,OAAA,CAAA4D,eAAA,CAAAX,IAAA,QAAAjD,OAAA,CAAA4D,eAAA,CAAAV,KAAA,QAAAlD,OAAA,CAAA4D,eAAA,CAAAT,OAAA,MACF;;;;;;IAtCFzF,EAHJ,CAAAM,cAAA,cAA+D,cAExB,cAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,cAAiB,sBAQE;IANfN,EAAA,CAAAkD,gBAAA,2BAAAsF,0GAAApF,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA4H,IAAA;MAAA,MAAAnG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA+D,cAAA,EAAAjD,MAAA,MAAAd,OAAA,CAAA+D,cAAA,GAAAjD,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAiC;IAKjCpD,EAAA,CAAAU,UAAA,sBAAAgI,qGAAA;MAAA1I,EAAA,CAAAa,aAAA,CAAA4H,IAAA;MAAA,MAAAnG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqF,qBAAA,CAAAjE,OAAA,CAA2B;IAAA,EAAC;IAI9CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAIJP,EADF,CAAAM,cAAA,cAAqC,cAClB;IAAAN,EAAA,CAAAC,MAAA,wBAAiB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEtCP,EADF,CAAAM,cAAA,cAAiB,uBAQE;IANfN,EAAA,CAAAkD,gBAAA,2BAAAyF,2GAAAvF,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA4H,IAAA;MAAA,MAAAnG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA4D,eAAA,EAAA9C,MAAA,MAAAd,OAAA,CAAA4D,eAAA,GAAA9C,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAkC;IAKlCpD,EAAA,CAAAU,UAAA,sBAAAkI,sGAAA;MAAA5I,EAAA,CAAAa,aAAA,CAAA4H,IAAA;MAAA,MAAAnG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqF,qBAAA,CAAAjE,OAAA,CAA2B;IAAA,EAAC;IAI9CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAIJP,EADF,CAAAM,cAAA,gBAAyC,eACtB;IAAAN,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,eAAiB,cACA;IAAAN,EAAA,CAAAC,MAAA,IAAoE;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACvFP,EAAA,CAAAM,cAAA,cAAe;IAAAN,EAAA,CAAAC,MAAA,IAAkC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACrDP,EAAA,CAAAkC,UAAA,KAAA2G,0EAAA,iBAA4C;IAKlD7I,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;IAtCEP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA+D,cAAA,CAAiC;IAGjCrG,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAAqE,iBAAA,CAAkC,aAAArE,OAAA,CAAAiB,cAAA,CAEF;IAahCvD,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA4D,eAAA,CAAkC;IAGlClG,EAFA,CAAAwC,UAAA,YAAAF,OAAA,CAAAsE,kBAAA,CAAmC,aAAAtE,OAAA,CAAAiB,cAAA,CAEH;IAYnBvD,EAAA,CAAAQ,SAAA,GAAoE;IAApER,EAAA,CAAAE,kBAAA,KAAAoC,OAAA,CAAA+D,cAAA,kBAAA/D,OAAA,CAAA+D,cAAA,CAAAQ,SAAA,OAAAvE,OAAA,CAAA+D,cAAA,kBAAA/D,OAAA,CAAA+D,cAAA,CAAAS,QAAA,KAAoE;IACpE9G,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA4D,eAAA,kBAAA5D,OAAA,CAAA4D,eAAA,CAAAa,QAAA,CAAkC;IACjC/G,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA4D,eAAA,CAA0B;;;;;;IA5H9ClG,EAHJ,CAAAM,cAAA,cAAwE,cAEjC,cAClB;IAAAN,EAAA,CAAAC,MAAA,mBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEjCP,EADF,CAAAM,cAAA,cAAiB,sBAWE;IATfN,EAAA,CAAAkD,gBAAA,2BAAA4F,mGAAA1F,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAkI,IAAA;MAAA,MAAAzG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA8B,gBAAA,EAAAhB,MAAA,MAAAd,OAAA,CAAA8B,gBAAA,GAAAhB,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAmC;IAWvCpD,EADE,CAAAO,YAAA,EAAa,EACT;IAENP,EADA,CAAAkC,UAAA,IAAA8G,oEAAA,mBAAgD,IAAAC,oEAAA,mBACX;IAQvCjJ,EAAA,CAAAO,YAAA,EAAM;IAIJP,EADF,CAAAM,cAAA,cAAqC,cAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAe;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEpCP,EADF,CAAAM,cAAA,eAAiB,sBAOE;IALfN,EAAA,CAAAkD,gBAAA,2BAAAgG,oGAAA9F,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAkI,IAAA;MAAA,MAAAzG,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAyC,WAAA,EAAA3B,MAAA,MAAAd,OAAA,CAAAyC,WAAA,GAAA3B,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA8B;IAQpCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IA8CNP,EA3CA,CAAAkC,UAAA,KAAAiH,qEAAA,oBAAgE,KAAAC,qEAAA,oBA2CD;IA6CjEpJ,EAAA,CAAAO,YAAA,EAAM;;;;;IA/HEP,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA8B,gBAAA,CAAmC;IAQnCpE,EAPA,CAAAwC,UAAA,uBAAsB,wBACC,uBACD,uBACA,0BACG,YAAAtB,MAAA,CAAAmD,qBAAA,CAAA/B,OAAA,EACc,YAAApB,MAAA,CAAAoD,qBAAA,CAAAhC,OAAA,EACA,aAAAA,OAAA,CAAAiB,cAAA,CACP;IAIPvD,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAwC,UAAA,UAAAtB,MAAA,CAAAmI,UAAA,CAAiB;IAC5BrJ,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAwC,UAAA,UAAAtB,MAAA,CAAAmI,UAAA,CAAiB;IAe/BrJ,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAyC,WAAA,CAA8B;IAG9B/E,EAFA,CAAAwC,UAAA,YAAAtB,MAAA,CAAA8D,UAAA,CAAsB,aAAA1C,OAAA,CAAAiB,cAAA,CAEU;IAQDvD,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAAgH,aAAA,CAAyB;IA2CzBtJ,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAgH,aAAA,CAAwB;;;;;;IAxQ/DtJ,EAFF,CAAAM,cAAA,cAA8F,cAEvC;IACnDN,EAAA,CAAAiC,SAAA,YAA8B;IAC9BjC,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAC,MAAA,0BAAmB;IAAAD,EAAA,CAAAO,YAAA,EAAO;IAChCP,EAAA,CAAAM,cAAA,iBAKkC;IAA1BN,EAAA,CAAAU,UAAA,mBAAA6I,iFAAA;MAAAvJ,EAAA,CAAAa,aAAA,CAAA2I,IAAA;MAAA,MAAAlH,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAuI,SAAA,CAAAnH,OAAA,CAAe;IAAA,EAAC;IAEnCtC,EADE,CAAAO,YAAA,EAAS,EACL;IAsKNP,EAnKA,CAAAkC,UAAA,IAAAwH,8DAAA,kBAAuC,IAAAC,8DAAA,mBAW+B,IAAAC,8DAAA,oBAwJE;IAsI1E5J,EAAA,CAAAO,YAAA,EAAM;;;;;IA/SMP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwC,UAAA,cAAAF,OAAA,CAAAuH,cAAA,CAAiC;IAMtB7J,EAAA,CAAAQ,SAAA,EAAgB;IAAhBR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAmI,UAAA,CAAgB;IAWArJ,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAmI,UAAA,IAAA/G,OAAA,CAAA2B,MAAA,CAA+B;IAwJ/BjE,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAA2B,MAAA,KAAA/C,MAAA,CAAAmI,UAAA,CAAiC;;;;;IAwItErJ,EADF,CAAAM,cAAA,cAA6F,UACtF;IAAAN,EAAA,CAAAC,MAAA,qBAAc;IACrBD,EADqB,CAAAO,YAAA,EAAM,EACrB;;;;;;IAldJP,EAFJ,CAAAM,cAAA,cAAuG,cACjE,YACoE;IAArCN,EAAA,CAAAU,UAAA,mBAAAoJ,qEAAA;MAAA,MAAAxH,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAkJ,GAAA,EAAAhJ,SAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA8I,YAAA,CAAAhJ,SAAA,EAAAsB,OAAA,CAA0B;IAAA,EAAC;IACvGtC,EADwG,CAAAO,YAAA,EAAI,EACtG;IAIJP,EADF,CAAAM,cAAA,cAA+E,cACxB;IAAAN,EAAA,CAAAiC,SAAA,YAAyB;IAACjC,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACjGP,EAAA,CAAAM,cAAA,aAAkB;IAAAN,EAAA,CAAAC,MAAA,GAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACpCP,EAAA,CAAAM,cAAA,aAAkB;IAAAN,EAAA,CAAAC,MAAA,IAA0C;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEhEP,EADF,CAAAM,cAAA,cAA2C,gBACf;IAAAN,EAAA,CAAAC,MAAA,iBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAO;IAC1CP,EAAA,CAAAM,cAAA,gBAA0B;IAAAN,EAAA,CAAAC,MAAA,IAAsC;;IAEpED,EAFoE,CAAAO,YAAA,EAAO,EACnE,EACF;IAIJP,EADF,CAAAM,cAAA,eAAqF,eAC9B;IAAAN,EAAA,CAAAiC,SAAA,aAA0B;IAACjC,EAAA,CAAAC,MAAA,uBAAc;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAIlGP,EADF,CAAAM,cAAA,eAAmE,eACjB;IAE9CN,EADA,CAAAkC,UAAA,KAAA+H,sDAAA,gBAAkF,KAAAC,sDAAA,gBACH;IAC/ElK,EAAA,CAAAC,MAAA,IACF;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACNP,EAAA,CAAAM,cAAA,WAAK;IAAAN,EAAA,CAAAC,MAAA,IAA8B;;IACrCD,EADqC,CAAAO,YAAA,EAAM,EACrC;IA4CNP,EAzCA,CAAAkC,UAAA,KAAAiI,wDAAA,oBAA8E,KAAAC,wDAAA,kBAyCgC;IAU5GpK,EADF,CAAAM,cAAA,eAA6D,eACrC;IAAAN,EAAA,CAAAC,MAAA,qBAAa;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEvCP,EADF,CAAAM,cAAA,eAAiB,sBAegB;IAb7BN,EAAA,CAAAkD,gBAAA,2BAAAmH,uFAAAjH,MAAA;MAAA,MAAAd,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAkJ,GAAA,EAAAhJ,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAwB,YAAA,EAAAV,MAAA,MAAAd,OAAA,CAAAwB,YAAA,GAAAV,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA+B;IAC/BpD,EAAA,CAAAU,UAAA,sBAAA4J,kFAAA;MAAA,MAAAhI,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAkJ,GAAA,EAAAhJ,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqJ,kBAAA,CAAAjI,OAAA,CAAwB;IAAA,EAAC;IAe3CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAgBNP,EAbA,CAAAkC,UAAA,KAAAsI,wDAAA,kBAA2F,KAAAC,wDAAA,oBAa3B;IAmClEzK,EAAA,CAAAO,YAAA,EAAM;IA0TNP,EAzTA,CAAAkC,UAAA,KAAAwI,wDAAA,kBAA8F,KAAAC,wDAAA,kBAyTD;IAG/F3K,EAAA,CAAAO,YAAA,EAAM;;;;;IA7cgBP,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAAsI,GAAA,CAAY;IACZ5K,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAqF,kBAAA,KAAA/C,OAAA,CAAAuI,IAAA,OAAAvI,OAAA,CAAAwI,IAAA,OAAAxI,OAAA,CAAAyI,KAAA,KAA0C;IAGhC/K,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuE,WAAA,SAAAjC,OAAA,CAAA0I,WAAA,cAAsC;IAWlBhL,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAwC,UAAA,UAAAtB,MAAA,CAAA+J,MAAA,CAAa;IACfjL,EAAA,CAAAQ,SAAA,EAAY;IAAZR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAA+J,MAAA,CAAY;IACtDjL,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAAL,OAAA,CAAA4I,UAAA,MACF;IACKlL,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,SAAAe,OAAA,CAAA6I,UAAA,EAA8B;IAI2BnL,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAA+J,MAAA,CAAY;IAyCdjL,EAAA,CAAAQ,SAAA,EAA8C;IAA9CR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAA8I,QAAA,KAAA9I,OAAA,CAAA+I,gBAAA,CAA8C;IAatGrL,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAwB,YAAA,CAA+B;IAY/B9D,EAVA,CAAAwC,UAAA,2BAA0B,wBACH,wBACA,uBACD,0BACG,mBACP,mBACA,uBACI,YAAAtB,MAAA,CAAAoK,UAAA,CAAAhJ,OAAA,EACM,YAAApB,MAAA,CAAAqK,UAAA,CAAAjJ,OAAA,EACA,aAAAA,OAAA,CAAAiB,cAAA,CACI;IAOPvD,EAAA,CAAAQ,SAAA,EAA0D;IAA1DR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA4I,UAAA,iBAAA5I,OAAA,CAAAkJ,eAAA,CAA0D;IAapDxL,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAiB,cAAA,CAAyB;IAoCbvD,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAmJ,WAAA,KAAAnJ,OAAA,CAAAoJ,SAAA,CAAyC;IAyTtF1L,EAAA,CAAAQ,SAAA,EAAwC;IAAxCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAmJ,WAAA,IAAAnJ,OAAA,CAAAoJ,SAAA,CAAwC;;;;;IAnehD1L,EAAA,CAAAM,cAAA,UAA+B;IAiB/BN,EAhBE,CAAAkC,UAAA,IAAAyJ,iDAAA,mBAAqH,IAAAC,iDAAA,oBAgBhB;IAsdzG5L,EAAA,CAAAO,YAAA,EAAM;;;;IAteqFP,EAAA,CAAAQ,SAAA,EAA4B;IAA5BR,EAAA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA6K,kBAAA,CAA4B;IAgBjC7L,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA8K,OAAA,CAAiB;;;;;;IAlD/F9L,EAJR,CAAAM,cAAA,cAA6F,cAC9B,cACf,cACpB,cACqB;IAAAN,EAAA,CAAAC,MAAA,kBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1DP,EAAA,CAAAM,cAAA,cAAyC;IAAAN,EAAA,CAAAC,MAAA,GAAqB;IAChED,EADgE,CAAAO,YAAA,EAAM,EAChE;IAEJP,EADF,CAAAM,cAAA,cAAsB,cACsB;IAAAN,EAAA,CAAAC,MAAA,mBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC3DP,EAAA,CAAAM,cAAA,eAAyC;IAAAN,EAAA,CAAAC,MAAA,IAAc;IACzDD,EADyD,CAAAO,YAAA,EAAM,EACzD;IAEJP,EADF,CAAAM,cAAA,eAAsB,eACsB;IAAAN,EAAA,CAAAC,MAAA,gBAAQ;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACxDP,EAAA,CAAAM,cAAA,eAAyC;IAAAN,EAAA,CAAAC,MAAA,IAAkC;;IAC7ED,EAD6E,CAAAO,YAAA,EAAM,EAC7E;IAEJP,EADF,CAAAM,cAAA,eAAyC,eACG;IAAAN,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC5DP,EAAA,CAAAM,cAAA,sBAOiC;IANrBN,EAAA,CAAAkD,gBAAA,2BAAA6I,0EAAA3I,MAAA;MAAA,MAAApC,SAAA,GAAAhB,EAAA,CAAAa,aAAA,CAAAmL,GAAA,EAAAjL,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAtC,SAAA,CAAAiL,WAAA,EAAA7I,MAAA,MAAApC,SAAA,CAAAiL,WAAA,GAAA7I,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAgC;IAU1CpD,EAHA,CAAAkC,UAAA,KAAAgK,mDAAA,0BAA+C,KAAAC,mDAAA,0BAGR;IAK7CnM,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IACNP,EAAA,CAAAM,cAAA,eAAgJ;IAA/BN,EAAA,CAAAU,UAAA,mBAAA0L,2DAAA;MAAA,MAAApL,SAAA,GAAAhB,EAAA,CAAAa,aAAA,CAAAmL,GAAA,EAAAjL,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAmL,YAAA,CAAArL,SAAA,CAAoB;IAAA,EAAC;IACjJhB,EADE,CAAAO,YAAA,EAAgJ,EAC5I;IAENP,EAAA,CAAAkC,UAAA,KAAAoK,2CAAA,kBAA+B;IAwenCtM,EAAA,CAAAO,YAAA,EAAM;;;;;IAxgB6CP,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAqB,iBAAA,CAAAL,SAAA,CAAAuL,UAAA,CAAqB;IAIrBvM,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAqB,iBAAA,CAAAL,SAAA,CAAAwL,GAAA,CAAc;IAIdxM,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,SAAAL,MAAA,CAAAuL,WAAA,CAAAzL,SAAA,GAAkC;IAK/DhB,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAwD,gBAAA,YAAAxC,SAAA,CAAAiL,WAAA,CAAgC;IAKhCjM,EAJA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA0L,eAAA,CAAkC,4BAEP,mCACO,gBACnB;IAW1B1M,EAAA,CAAAQ,SAAA,GAAoF;IAApFR,EAAA,CAAAwC,UAAA,SAAAxB,SAAA,CAAA2L,UAAA,4DAAA3M,EAAA,CAAA4M,aAAA,CAAoF;IAGrF5M,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAwC,UAAA,SAAAxB,SAAA,CAAA2L,UAAA,CAAuB;;;;;;IAqgB3B3M,EADF,CAAAM,cAAA,eAA4C,gBAC4D;IAA9BN,EAAA,CAAAU,UAAA,mBAAAmM,qEAAA;MAAA7M,EAAA,CAAAa,aAAA,CAAAiM,IAAA;MAAA,MAAA5L,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA6L,iBAAA,EAAmB;IAAA,EAAC;IAAC/M,EAAA,CAAAO,YAAA,EAAS;IAC/GP,EAAA,CAAAM,cAAA,kBAAyG;IAA/BN,EAAA,CAAAU,UAAA,mBAAAsM,qEAAA;MAAAhN,EAAA,CAAAa,aAAA,CAAAiM,IAAA;MAAA,MAAA5L,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA+L,kBAAA,EAAoB;IAAA,EAAC;IAC1GjN,EAD2G,CAAAO,YAAA,EAAS,EAC9G;;;;;;IAyEJP,EADF,CAAAM,cAAA,eAA4C,gBAC8D;IAAhCN,EAAA,CAAAU,UAAA,mBAAAwM,qEAAA;MAAAlN,EAAA,CAAAa,aAAA,CAAAsM,IAAA;MAAA,MAAAjM,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAkM,mBAAA,EAAqB;IAAA,EAAC;IAACpN,EAAA,CAAAO,YAAA,EAAS;IACjHP,EAAA,CAAAM,cAAA,kBAA+F;IAAxBN,EAAA,CAAAU,UAAA,mBAAA2M,qEAAA;MAAArN,EAAA,CAAAa,aAAA,CAAAsM,IAAA;MAAA,MAAAjM,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAoM,WAAA,EAAa;IAAA,EAAC;IAChGtN,EADiG,CAAAO,YAAA,EAAS,EACpG;;;ADpnBV,WAAagN,oBAAoB;EAA3B,MAAOA,oBAAoB;IAU/B;IACAlB,YAAYA,CAACmB,MAAW;MACtBA,MAAM,CAACb,UAAU,GAAG,CAACa,MAAM,CAACb,UAAU;IACxC;IAwCAc,YACUC,MAAc,EACdC,KAAqB,EACrBC,0BAAsD,EACtDC,cAA8B;MAH9B,KAAAH,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,0BAA0B,GAA1BA,0BAA0B;MAC1B,KAAAC,cAAc,GAAdA,cAAc;MAxDxB,KAAA5C,MAAM,GAAY,KAAK;MACvB,KAAA6C,QAAQ,GAAU,EAAE;MACpB,KAAA9I,UAAU,GAAU,EAAE;MACtB,KAAA+I,SAAS,GAAU,EAAE;MACrB,KAAArG,YAAY,GAAU,EAAE;MACxB,KAAA1B,eAAe,GAAU,EAAE;MAC3B,KAAAqD,UAAU,GAAY,KAAK;MAC3B,KAAA3G,gBAAgB,GAAY,KAAK;MAOjC;MACA,KAAAsL,qBAAqB,GAAY,KAAK;MACtC,KAAAC,YAAY,GAAQ,IAAI;MACxB,KAAAC,eAAe,GAAW,CAAC;MAE3B;MACA,KAAAC,cAAc,GAAQ;QACpBC,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,IAAI;QACpBC,aAAa,EAAE,IAAI;QACnBC,UAAU,EAAE,UAAU;QACtBC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;OACX;MAED;MACA,KAAAC,iBAAiB,GAAY,KAAK;MAClC,KAAAC,mBAAmB,GAAQ,IAAI;MAC/B,KAAAC,iBAAiB,GAAmB,KAAK;MACzC,KAAAC,cAAc,GAAQ,IAAI;MAC1B,KAAAC,iBAAiB,GAAW,CAAC;MAE7B;MACA,KAAAC,WAAW,GAAG;QACZC,gBAAgB,EAAE,EAAE;QACpBrI,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZyB,YAAY,EAAE,EAAE;QAChB4G,YAAY,EAAE,EAAE;QAChB5J,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACX2J,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;OACR;IAOE;IAEHC,QAAQA,CAAA;MACN;MACA,MAAMC,QAAQ,GAAG;QACfC,UAAU,EAAE,CACV;UACE,QAAQ,EAAE,sCAAsC;UAChD,cAAc,EAAE,sCAAsC;UACtD,iBAAiB,EAAE,IAAI;UACvB,oBAAoB,EAAE,sCAAsC;UAC5D,eAAe,EAAE,MAAM;UACvB,MAAM,EAAE,IAAI;UACZ,aAAa,EAAE,IAAI;UACnB,MAAM,EAAE,EAAE;UACV,mBAAmB,EAAE,IAAI;UACzB,UAAU,EAAE,EAAE;UACd,YAAY,EAAE,IAAI;UAClB,SAAS,EAAE,IAAI;UACf,aAAa,EAAE,IAAI;UACnB,oBAAoB,EAAE,wBAAwB;UAC9C,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,mBAAmB,EAAE,CAAC;UACtB,oBAAoB,EAAE,CAAC;UACvB,gBAAgB,EAAE,KAAK;UACvB,oBAAoB,EAAE,CAAC;UACvB,WAAW,EAAE,IAAI;UACjB,MAAM,EAAE,IAAI;UACZ,gBAAgB,EAAE,IAAI;UACtB,aAAa,EAAE,KAAK;UACpB,MAAM,EAAE,KAAK;UACb,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,mBAAmB;UAC1B,kBAAkB,EAAE,IAAI;UACxB,WAAW,EAAE,CAAC;UACd,sBAAsB,EAAE,IAAI;UAC5B,UAAU,EAAE,IAAI;UAChB,gBAAgB,EAAE,IAAI;UACtB,eAAe,EAAE,IAAI;UACrB,WAAW,EAAE,IAAI;UACjB,gBAAgB,EAAE,KAAK;UACvB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,KAAK;UACtB,gBAAgB,EAAE,IAAI;UACtB,YAAY,EAAE,IAAI;UAClB,oBAAoB,EAAE,IAAI;UAC1B,gBAAgB,EAAE,IAAI;UACtB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,IAAI;UACrB,UAAU,EAAE,IAAI;UAChB,cAAc,EAAE,qBAAqB;UACrC,aAAa,EAAE,qBAAqB;UACpC,SAAS,EAAE,qBAAqB;UAChC,QAAQ,EAAE,KAAK;UACf,WAAW,EAAE,KAAK;UAClB,QAAQ,EAAE,EAAE;UACZ,UAAU,EAAE,IAAI;UAChB,kBAAkB,EAAE,KAAK;UACzB,eAAe,EAAE,KAAK;UACtB,iBAAiB,EAAE,KAAK;UACxB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,oBAAoB,EAAE,CAAC;UACvB,YAAY,EAAE,EAAE;UAChB,cAAc,EAAE,qBAAqB;UACrC,qBAAqB,EAAE,IAAI;UAC3B,aAAa,EAAE,IAAI;UACnB,iBAAiB,EAAE,IAAI;UACvB,UAAU,EAAE,sCAAsC;UAClD,QAAQ,EAAE,sCAAsC;UAChD,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,QAAQ;UACtB,YAAY,EAAE,IAAI;UAClB,UAAU,EAAE,sCAAsC;UAClD,KAAK,EAAE,UAAU;UACjB,WAAW,EAAE,UAAU;UACvB,YAAY,EAAE,QAAQ;UACtB,eAAe,EAAE,uCAAuC;UACxD,kBAAkB,EAAE,uCAAuC;UAC3D,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,qBAAqB;UACtC,iBAAiB,EAAE,CAAC;UACpB,kBAAkB,EAAE,CAAC;UACrB,eAAe,EAAE,CAAC;UAClB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,iBAAiB,EAAE,CAAC;UACpB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,SAAS,EAAE,CAAC;UACZ,aAAa,EAAE,IAAI;UACnB,WAAW,EAAE,CAAC;UACd,WAAW,EAAE,IAAI;UACjB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,YAAY,EAAE,IAAI;UAClB,eAAe,EAAE,IAAI;UACrB,cAAc,EAAE,IAAI;UACpB,iBAAiB,EAAE,CAAC;UACpB,YAAY,EAAE,CAAC;UACf,gBAAgB,EAAE,CAAC;UACnB,UAAU,EAAE,CAAC;UACb,WAAW,EAAE,CAAC;UACd,wBAAwB,EAAE,CAAC;UAC3B,yBAAyB,EAAE,CAAC;UAC5B,SAAS,EAAE,GAAG;UACd,kBAAkB,EAAE,CAAC;UACrB,aAAa,EAAE,KAAK;UACpB,SAAS,EAAE,uBAAuB;UAClC,oBAAoB,EAAE,EAAE;UACxB,YAAY,EAAE,CAAC;UACf,iBAAiB,EAAE,CACjB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,uBAAuB,EAAE,CACvB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,wBAAwB,EAAE,CACxB;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,aAAa,EAAE,IAAI;UACnB,YAAY,EAAE,GAAG;UACjB,aAAa,EAAE,KAAK;UACpB,cAAc,EAAE,KAAK;UACrB,UAAU,EAAE,CAAC;UACb,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,KAAK;UACtB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,SAAS,EAAE,CAAC;UACZ,SAAS,EAAE,CAAC;UACZ,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,oBAAoB,EAAE;SACvB,EACD;UACE,QAAQ,EAAE,sCAAsC;UAChD,cAAc,EAAE,sCAAsC;UACtD,iBAAiB,EAAE,IAAI;UACvB,oBAAoB,EAAE,sCAAsC;UAC5D,eAAe,EAAE,MAAM;UACvB,MAAM,EAAE,IAAI;UACZ,aAAa,EAAE,IAAI;UACnB,MAAM,EAAE,EAAE;UACV,mBAAmB,EAAE,EAAE;UACvB,UAAU,EAAE,EAAE;UACd,YAAY,EAAE,EAAE;UAChB,SAAS,EAAE,IAAI;UACf,aAAa,EAAE,IAAI;UACnB,oBAAoB,EAAE,yBAAyB;UAC/C,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,mBAAmB,EAAE,CAAC;UACtB,oBAAoB,EAAE,CAAC;UACvB,gBAAgB,EAAE,KAAK;UACvB,oBAAoB,EAAE,CAAC;UACvB,WAAW,EAAE,IAAI;UACjB,MAAM,EAAE,IAAI;UACZ,gBAAgB,EAAE,IAAI;UACtB,aAAa,EAAE,KAAK;UACpB,MAAM,EAAE,KAAK;UACb,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,mBAAmB;UAC1B,kBAAkB,EAAE,IAAI;UACxB,WAAW,EAAE,CAAC;UACd,sBAAsB,EAAE,IAAI;UAC5B,UAAU,EAAE,IAAI;UAChB,gBAAgB,EAAE,IAAI;UACtB,eAAe,EAAE,IAAI;UACrB,WAAW,EAAE,IAAI;UACjB,gBAAgB,EAAE,KAAK;UACvB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,KAAK;UACtB,gBAAgB,EAAE,IAAI;UACtB,YAAY,EAAE,IAAI;UAClB,oBAAoB,EAAE,IAAI;UAC1B,gBAAgB,EAAE,IAAI;UACtB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,IAAI;UACrB,UAAU,EAAE,IAAI;UAChB,cAAc,EAAE,qBAAqB;UACrC,aAAa,EAAE,qBAAqB;UACpC,SAAS,EAAE,qBAAqB;UAChC,QAAQ,EAAE,KAAK;UACf,WAAW,EAAE,KAAK;UAClB,QAAQ,EAAE,IAAI;UACd,UAAU,EAAE,IAAI;UAChB,kBAAkB,EAAE,KAAK;UACzB,eAAe,EAAE,KAAK;UACtB,iBAAiB,EAAE,KAAK;UACxB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,oBAAoB,EAAE,CAAC;UACvB,YAAY,EAAE,IAAI;UAClB,cAAc,EAAE,qBAAqB;UACrC,qBAAqB,EAAE,IAAI;UAC3B,aAAa,EAAE,IAAI;UACnB,iBAAiB,EAAE,IAAI;UACvB,UAAU,EAAE,sCAAsC;UAClD,QAAQ,EAAE,sCAAsC;UAChD,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,QAAQ;UACtB,YAAY,EAAE,IAAI;UAClB,UAAU,EAAE,sCAAsC;UAClD,KAAK,EAAE,UAAU;UACjB,WAAW,EAAE,UAAU;UACvB,YAAY,EAAE,QAAQ;UACtB,eAAe,EAAE,iHAAiH;UAClI,kBAAkB,EAAE,iHAAiH;UACrI,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,qBAAqB;UACtC,iBAAiB,EAAE,IAAI;UACvB,kBAAkB,EAAE,CAAC;UACrB,eAAe,EAAE,CAAC;UAClB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,iBAAiB,EAAE,CAAC;UACpB,eAAe,EAAE,IAAI;UACrB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,SAAS,EAAE,CAAC;UACZ,aAAa,EAAE,IAAI;UACnB,WAAW,EAAE,CAAC;UACd,WAAW,EAAE,IAAI;UACjB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,YAAY,EAAE,IAAI;UAClB,eAAe,EAAE,IAAI;UACrB,cAAc,EAAE,IAAI;UACpB,iBAAiB,EAAE,CAAC;UACpB,YAAY,EAAE,CAAC;UACf,gBAAgB,EAAE,CAAC;UACnB,UAAU,EAAE,CAAC;UACb,WAAW,EAAE,CAAC;UACd,wBAAwB,EAAE,CAAC;UAC3B,yBAAyB,EAAE,CAAC;UAC5B,SAAS,EAAE,GAAG;UACd,kBAAkB,EAAE,CAAC;UACrB,aAAa,EAAE,KAAK;UACpB,SAAS,EAAE,uBAAuB;UAClC,oBAAoB,EAAE,EAAE;UACxB,YAAY,EAAE,GAAG;UACjB,iBAAiB,EAAE,CACjB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,uBAAuB,EAAE,CACvB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,wBAAwB,EAAE,CACxB;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,aAAa,EAAE,IAAI;UACnB,YAAY,EAAE,GAAG;UACjB,aAAa,EAAE,KAAK;UACpB,cAAc,EAAE,KAAK;UACrB,UAAU,EAAE,CAAC;UACb,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,KAAK;UACtB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,SAAS,EAAE,CAAC;UACZ,SAAS,EAAE,CAAC;UACZ,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,oBAAoB,EAAE;SACvB,CACF;QACD,iBAAiB,EAAE,CACjB;UACE,UAAU,EAAE,sCAAsC;UAClD,0BAA0B,EAAE,sCAAsC;UAClE,SAAS,EAAE,WAAW;UACtB,aAAa,EAAE,WAAW;UAC1B,iBAAiB,EAAE,GAAG;UACtB,2BAA2B,EAAE,CAAC;UAC9B,YAAY,EAAE,IAAI;UAClB,KAAK,EAAE,UAAU;UACjB,MAAM,EAAE,UAAU;UAClB,YAAY,EAAE,QAAQ;UACtB,cAAc,EAAE,qBAAqB;UACrC,SAAS,EAAE,IAAI;UACf,YAAY,EAAE,qBAAqB;UACnC,4BAA4B,EAAE,GAAG;UACjC,WAAW,EAAE,IAAI;UACjB,SAAS,EAAE;SACZ,EACD;UACE,UAAU,EAAE,sCAAsC;UAClD,0BAA0B,EAAE,sCAAsC;UAClE,SAAS,EAAE,mBAAmB;UAC9B,aAAa,EAAE,mBAAmB;UAClC,iBAAiB,EAAE,GAAG;UACtB,2BAA2B,EAAE,CAAC;UAC9B,YAAY,EAAE,IAAI;UAClB,KAAK,EAAE,UAAU;UACjB,MAAM,EAAE,UAAU;UAClB,YAAY,EAAE,QAAQ;UACtB,cAAc,EAAE,qBAAqB;UACrC,SAAS,EAAE,IAAI;UACf,YAAY,EAAE,qBAAqB;UACnC,4BAA4B,EAAE,GAAG;UACjC,WAAW,EAAE,IAAI;UACjB,SAAS,EAAE;SACZ;OAEJ;MAED,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC;MAErC,IAAI,CAACE,IAAI,EAAE;QACT,IAAI,CAAC5B,cAAc,CAAC+B,GAAG,CAAC;UAACC,QAAQ,EAAC,OAAO;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAuB,CAAC,CAAC;QAC9F,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAChD;MACF;MAEA,MAAMC,WAAW,GAAG;QAClBT,UAAU,EAAEE,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACD,UAAU;QACvCW,eAAe,EAAET,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACU;OACnC;MAED,IAAI,CAACvC,0BAA0B,CAACwC,kBAAkB,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAACC,IAAI,KAAK,GAAG,EAAE;YACzB,IAAI,CAAC1C,QAAQ,GAAGyC,QAAQ,CAACd,IAAI,CAACgB,OAAO,IAAI,EAAE;YAC3C,IAAI,CAACzL,UAAU,GAAGuL,QAAQ,CAACd,IAAI,CAACzK,UAAU,IAAI,EAAE;YAChD,IAAI,CAAC+I,SAAS,GAAGwC,QAAQ,CAACd,IAAI,CAAC1B,SAAS,IAAI,EAAE;YAC9C;YACA,IAAI,CAACrG,YAAY,GAAG6I,QAAQ,CAACd,IAAI,CAACiB,mBAAmB,IAAI,EAAE;YAC3D,IAAI,CAAC1K,eAAe,GAAGuK,QAAQ,CAACd,IAAI,CAACzJ,eAAe,IAAI,EAAE;YAC1D,IAAI,CAACqD,UAAU,GAAGkH,QAAQ,CAACd,IAAI,CAACkB,iBAAiB,IAAI,KAAK;YAC1D;YAEA;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,MAAM,EAAED,CAAC,EAAE,EAAE;cAC7C,MAAM9E,OAAO,GAAG,IAAI,CAACgC,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO;cACxC,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAIgF,SAAS,EAAE;gBAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,OAAO,CAAC+E,MAAM,EAAEE,CAAC,EAAE,EAAE;kBACvC;kBACA,IAAIjF,OAAO,CAACiF,CAAC,CAAC,CAAC3F,QAAQ,IAAI,IAAI,IAAI,CAACU,OAAO,CAACiF,CAAC,CAAC,CAAC1F,gBAAgB,EAAE;oBAC/D;oBACA;oBACA,IAAIS,OAAO,CAACiF,CAAC,CAAC,CAACzH,aAAa,EAAE;sBAC5B,IAAI,CAACwE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC7K,eAAe,GAAG4F,OAAO,CAACiF,CAAC,CAAC,CAACC,cAAc;sBACvE,IAAI,CAAClD,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC1K,cAAc,GAAGyF,OAAO,CAACiF,CAAC,CAAC,CAACE,aAAa;oBACvE,CAAC,MAAM;sBACL,IAAI,CAACnD,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAChJ,WAAW,GAAG+D,OAAO,CAACiF,CAAC,CAAC,CAAC1I,cAAc,CAAC,CAAC,CAAC;oBACxE;oBACA;oBACA,IAAI,CAACyF,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAChM,WAAW,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;kBAC9D;kBAEA,MAAMkM,WAAW,GAAGpR,MAAM,CAAC,IAAI,CAACgO,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAACjN,YAAY,CAAC,CAAC8L,GAAG,CAAC,IAAI,CAAC9B,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAACI,SAAS,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;kBAC5I,IAAI,CAACtD,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC3M,gBAAgB,GAAG8M,WAAW;kBAE1D;kBACA,IAAI,CAACpD,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC/K,eAAe,GAAG,IAAI,CAACA,eAAe;kBAClE,IAAI,IAAI,CAACqD,UAAU,IAAI,IAAI,CAACyE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC9M,MAAM,EAAE;oBACzD,MAAMoN,SAAS,GAAG,IAAI,CAAC3J,YAAY,CAAC4J,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC3M,KAAK,IAAI,GAAG,CAAC;oBACtE,IAAIyM,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;sBACxB,IAAI,CAAC/C,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAACpM,QAAQ,GAAG0M,SAAS,CAAC,CAAC,CAAC;oBACrD;kBACF;kBACA;gBACF;cACF;YACF;YAEA;YACA,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,MAAM,EAAED,CAAC,EAAE,EAAE;cAC7C,IAAI,IAAI,CAAC9C,QAAQ,CAAC8C,CAAC,CAAC,CAACY,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC1D,QAAQ,CAAC8C,CAAC,CAAC,CAACY,aAAa,IAAIV,SAAS,IACpF,IAAI,CAAChD,QAAQ,CAAC8C,CAAC,CAAC,CAAClE,eAAe,IAAI,IAAI,IAAI,IAAI,CAACoB,QAAQ,CAAC8C,CAAC,CAAC,CAAClE,eAAe,IAAIoE,SAAS,EAAE;gBAC9F,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAAC8C,CAAC,CAAC,CAAClE,eAAe,CAACmE,MAAM,EAAEE,CAAC,EAAE,EAAE;kBAChE,IAAI,IAAI,CAACjD,QAAQ,CAAC8C,CAAC,CAAC,CAACY,aAAa,CAACC,aAAa,IAAI,IAAI,CAAC3D,QAAQ,CAAC8C,CAAC,CAAC,CAAClE,eAAe,CAACqE,CAAC,CAAC,CAACW,gBAAgB,EAAE;oBACxG,IAAI,CAAC5D,QAAQ,CAAC8C,CAAC,CAAC,CAAC3E,WAAW,GAAG,IAAI,CAAC6B,QAAQ,CAAC8C,CAAC,CAAC,CAAClE,eAAe,CAACqE,CAAC,CAAC;oBAClE;kBACF;gBACF;cACF;YACF;YAEA;YACA,IAAI,CAACjD,QAAQ,CAAC6D,OAAO,CAAEnE,MAAW,IAAI;cACpCA,MAAM,CAACb,UAAU,GAAG,IAAI;cACxB,IAAIa,MAAM,CAAC1B,OAAO,EAAE;gBAClB0B,MAAM,CAAC1B,OAAO,CAAC6F,OAAO,CAAEC,GAAQ,IAAI;kBAClC,IAAGA,GAAG,CAAC9N,YAAY,EAAE;oBACnB8N,GAAG,CAAC9N,YAAY,GAAGhE,MAAM,CAAC8R,GAAG,CAAC9N,YAAY,CAAC,CAAC+N,MAAM,EAAE;kBACtD;kBACA,IAAGD,GAAG,CAACE,eAAe,EAAE;oBACtBF,GAAG,CAACE,eAAe,GAAGhS,MAAM,CAAC8R,GAAG,CAACE,eAAe,CAAC,CAACD,MAAM,EAAE;kBAC5D;kBACA,IAAGD,GAAG,CAACG,WAAW,EAAE;oBAClBH,GAAG,CAACG,WAAW,GAAGjS,MAAM,CAAC8R,GAAG,CAACG,WAAW,CAAC,CAACF,MAAM,EAAE;kBACpD;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;YAEF;YACA,IAAI,CAAC/D,QAAQ,CAAC6D,OAAO,CAAEnE,MAAW,IAAI;cACpC,IAAIA,MAAM,CAAC1B,OAAO,EAAE;gBAClB0B,MAAM,CAAC1B,OAAO,CAAC6F,OAAO,CAAEK,IAAS,IAAI;kBACnC,IAAI,CAACzH,kBAAkB,CAACyH,IAAI,CAAC;gBAC/B,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAACnE,cAAc,CAAC+B,GAAG,CAAC;cAACC,QAAQ,EAAC,OAAO;cAAEC,OAAO,EAAE,OAAO;cAAEC,MAAM,EAAEQ,QAAQ,CAAC0B;YAAO,CAAC,CAAC;YACvF,IAAI,CAACvE,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;UAClD;QACF,CAAC;QACDkC,KAAK,EAAGA,KAAY,IAAI;UACtB,IAAI,CAACrE,cAAc,CAAC+B,GAAG,CAAC;YAACC,QAAQ,EAAC,OAAO;YAAEC,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAE;UAA4B,CAAC,CAAC;UACnGoC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;IAEJ;IAEAvQ,UAAUA,CAAA;MACR,IAAI,CAACsJ,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC5B;IAEAmH,QAAQA,CAAA;MACN,IAAIC,KAAK,GAAG,GAAG;MACf,IAAI,IAAI,CAACvE,QAAQ,IAAI,IAAI,EAAE;QACzB,KAAK,IAAI8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,MAAM,EAAED,CAAC,EAAE,EAAE;UAC7C,IAAI,IAAI,CAAC9C,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,IAAI,IAAI,EAAE;YAC/C,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,CAACgF,MAAM,EAAEE,CAAC,EAAE,EAAE;cACnE,IAAIuB,KAAK,CAAC,IAAI,CAACxE,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,CAACkF,CAAC,CAAC,CAACvP,eAAe,CAAC,IAAI,KAAK,EAAE;gBAC1E6Q,KAAK,IAAI,IAAI,CAACvE,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,CAACkF,CAAC,CAAC,CAACvP,eAAe;cACjE;YACF;UACF;UACA,IAAI,IAAI,CAACsM,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,IAAI,IAAI,EAAE;YACpC,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAAC+E,MAAM,EAAEE,CAAC,EAAE,EAAE;cACxD,IAAIuB,KAAK,CAAC,IAAI,CAACxE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC5F,UAAU,CAAC,IAAI,KAAK,EAAE;gBAC1DkH,KAAK,IAAI,IAAI,CAACvE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC5F,UAAU;cACjD;cACA,IAAImH,KAAK,CAAC,IAAI,CAACxE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC9N,WAAW,CAAC,IAAI,KAAK,EAAE;gBAC3DoP,KAAK,IAAIE,UAAU,CAAC,IAAI,CAACzE,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAACiF,CAAC,CAAC,CAAC9N,WAAW,CAAC;cAC9D;YACF;UACF;QACF;MACF;MACA,OAAOoP,KAAK;IACd;IAEA5F,WAAWA,CAAC+F,UAAe;MACzB,IAAIH,KAAK,GAAG,CAAC;MAEb,IAAIG,UAAU,IAAI,IAAI,EAAE;QACtB,IAAIA,UAAU,CAAC3G,kBAAkB,IAAI,IAAI,EAAE;UACzC,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,UAAU,CAAC3G,kBAAkB,CAACgF,MAAM,EAAEE,CAAC,EAAE,EAAE;YAC7DsB,KAAK,IAAIG,UAAU,CAAC3G,kBAAkB,CAACkF,CAAC,CAAC,CAACvP,eAAe;UAC3D;QACF;QACA,IAAIgR,UAAU,CAAC1G,OAAO,IAAI,IAAI,EAAE;UAC9B,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,UAAU,CAAC1G,OAAO,CAAC+E,MAAM,EAAEE,CAAC,EAAE,EAAE;YAClDsB,KAAK,IAAIG,UAAU,CAAC1G,OAAO,CAACiF,CAAC,CAAC,CAAC5F,UAAU;YACzCkH,KAAK,IAAIE,UAAU,CAACC,UAAU,CAAC1G,OAAO,CAACiF,CAAC,CAAC,CAAC9N,WAAW,CAAC;UACxD;QACF;MACF;MAEA,OAAOoP,KAAK;IACd;IAEAI,mBAAmBA,CAAC3O,YAAoB,EAAEgO,eAAuB;MAC/D,MAAMY,MAAM,GAAG;QAAE,GAAG,IAAI,CAACvE;MAAc,CAAE;MACzC,IAAIrK,YAAY,EAAE;QAChB4O,MAAM,CAACC,OAAO,GAAG,IAAIC,IAAI,CAAC9O,YAAY,CAAC;QACvC4O,MAAM,CAACG,WAAW,GAAG,IAAID,IAAI,CAAC9O,YAAY,CAAC;MAC7C;MACA,IAAIgO,eAAe,EAAE;QACnBY,MAAM,CAACI,OAAO,GAAG,IAAIF,IAAI,CAACd,eAAe,CAAC;MAC5C;MACA,OAAOY,MAAM;IACf;IAEAnI,kBAAkBA,CAACyH,IAAS;MAC1B,MAAMd,WAAW,GAAGpR,MAAM,CAACkS,IAAI,CAAClO,YAAY,CAAC,CAAC8L,GAAG,CAACoC,IAAI,CAACb,SAAS,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;MAC9FY,IAAI,CAAC5N,gBAAgB,GAAG8M,WAAW;MAEnC,MAAM6B,GAAG,GAAGjT,MAAM,CAACkS,IAAI,CAACD,WAAW,CAAC,CAACX,MAAM,CAAC,YAAY,CAAC;MACzD,MAAM4B,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAACnT,MAAM,CAACkS,IAAI,CAAClO,YAAY,CAAC,CAACsN,MAAM,CAAC,YAAY,CAAC,EAAE2B,GAAG,CAAC;MAEnF,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAI,CAACtQ,gBAAgB,GAAG,IAAI;MAC9B,CAAC,MAAM;QACL,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC/B;MAEA,MAAMwQ,YAAY,GAAGF,QAAQ,GAAGhB,IAAI,CAACmB,aAAa;MAClD,MAAMC,aAAa,GAAGJ,QAAQ,GAAGhB,IAAI,CAACqB,cAAc;MACpDrB,IAAI,CAACpP,aAAa,GAAGoP,IAAI,CAACsB,iBAAiB,GAAGJ,YAAY;MAC1DlB,IAAI,CAACnP,cAAc,GAAGmP,IAAI,CAACuB,kBAAkB,GAAGH,aAAa;MAE7DpB,IAAI,CAAC7G,UAAU,GAAG6G,IAAI,CAACwB,cAAc,GAAGN,YAAY,GAAGE,aAAa;IACtE;IAEA;IACQH,QAAQA,CAACQ,MAAc,EAAEC,MAAc;MAC7C,IAAIC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK;MAChCH,KAAK,GAAGF,MAAM,CAACM,KAAK,CAAC,GAAG,CAAC;MACzB;MACAH,MAAM,GAAG,IAAIhB,IAAI,CAACe,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAI;MAClEA,KAAK,GAAGD,MAAM,CAACK,KAAK,CAAC,GAAG,CAAC;MACzB;MACAF,MAAM,GAAG,IAAIjB,IAAI,CAACe,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC7DG,KAAK,GAAGE,QAAQ,CAAC,CAACC,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,OAAO,EAAE,GAAGN,MAAM,CAACM,OAAO,EAAE,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAEC,QAAQ,EAAE,CAAC,CAAC,CAAI;MACvG,OAAON,KAAK;IACd;IAEAO,GAAGA,CAAA;MACD,IAAIC,UAAU,GAAG,IAAI;MACrB,IAAIC,mBAAmB,GAAG,IAAI;MAE9B;MACA,IAAI,CAACzG,QAAQ,CAAC6D,OAAO,CAAElC,IAAS,IAAI;QAClC,IAAIA,IAAI,CAACxD,WAAW,IAAI,IAAI,IAAIwD,IAAI,CAACxD,WAAW,IAAI6E,SAAS,EAAE;UAC7D,IAAI,CAACjD,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE,GAAGN,IAAI,CAACjD,GAAG;WACpB,CAAC;UACF8H,UAAU,GAAG,KAAK;QACpB;QAEA;QACA,IAAIE,MAAM,GAAG,IAAI;QACjB,IAAIC,OAAO,GAAG,IAAI;QAClB,IAAIhF,IAAI,CAAC3D,OAAO,IAAI,IAAI,IAAI2D,IAAI,CAAC3D,OAAO,IAAIgF,SAAS,EAAE;UACrD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAAC3D,OAAO,CAAC+E,MAAM,EAAED,CAAC,EAAE,EAAE;YAC5C;YACA;YACA,IAAInB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACtH,aAAa,EAAE;cACjC,IAAImG,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACxF,QAAQ,IAAI,CAACqE,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvF,gBAAgB,IAAI,EAAEoE,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACnF,WAAW,IAAIgE,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAClF,SAAS,CAAC,EAAE;gBAEhI;gBACA,IAAI,IAAI,CAACrC,UAAU,EAAE;kBAEnB;kBACA,IAAIoG,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC3M,MAAM,EAAE;oBAC1B,IAAIwL,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,IAAImM,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,IAAI,IAAI,IAAI8K,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,CAACkM,MAAM,IAAI,CAAC,EAAE;sBACrH,IAAI,CAAChD,cAAc,CAAC+B,GAAG,CAAC;wBACtBC,QAAQ,EAAC,OAAO;wBAChBC,OAAO,EAAE,OAAO;wBAChBC,MAAM,EAAE;uBACT,CAAC;sBACFuE,UAAU,GAAG,KAAK;oBACpB,CAAC,MACI,IAAI7E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,CAACC,KAAK,IAAI,GAAG,KAAK6K,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACtL,eAAe,IAAIwL,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACtL,eAAe,IAAI,IAAI,IAAImK,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACtL,eAAe,CAACuL,MAAM,IAAI,CAAC,CAAC,EAAE;sBAC1L,IAAI,CAAChD,cAAc,CAAC+B,GAAG,CAAC;wBACtBC,QAAQ,EAAC,OAAO;wBAChBC,OAAO,EAAE,OAAO;wBAChBC,MAAM,EAAE;uBACT,CAAC;sBACFuE,UAAU,GAAG,KAAK;oBACpB,CAAC,MACI,IAAI7E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,CAACC,KAAK,IAAI,GAAG,IAAI6K,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjM,QAAQ,CAACC,KAAK,IAAI,GAAG,EAAE;sBACvF,IAAI6K,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAIyK,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAI,IAAI,IAAIoJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,CAACwK,MAAM,IAAI,CAAC,EAAE;wBACvI2D,MAAM,GAAG,KAAK;sBAChB,CAAC,MAAM,IAAI/E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI4K,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI,IAAI,IAAIuJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,CAAC2K,MAAM,IAAI,CAAC,EAAE;wBACjJ4D,OAAO,GAAG,KAAK;sBACjB;oBACF;kBACF,CAAC,MACI;oBACH;oBACA,IAAIhF,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAIyK,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAI,IAAI,IAAIoJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,CAACwK,MAAM,IAAI,CAAC,EAAE;sBACvI2D,MAAM,GAAG,KAAK;oBAChB,CAAC,MAAM,IAAI/E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI4K,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI,IAAI,IAAIuJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,CAAC2K,MAAM,IAAI,CAAC,EAAE;sBACjJ4D,OAAO,GAAG,KAAK;oBACjB;kBACF;gBACF;gBACA;gBAAA,KACK;kBACH,IAAIhF,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAIyK,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,IAAI,IAAI,IAAIoJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvK,cAAc,CAACwK,MAAM,IAAI,CAAC,EAAE;oBACvI2D,MAAM,GAAG,KAAK;kBAChB,CAAC,MAAM,IAAI/E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI4K,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,IAAI,IAAI,IAAIuJ,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC1K,eAAe,CAAC2K,MAAM,IAAI,CAAC,EAAE;oBACjJ4D,OAAO,GAAG,KAAK;kBACjB;gBACF;gBACA;cACF;YAEF,CAAC,MAAM;cACL;cACA,IAAIhF,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACxF,QAAQ,IAAI,CAACqE,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACvF,gBAAgB,KAAKoE,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC7I,WAAW,IAAI+I,SAAS,IAAIrB,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC7I,WAAW,IAAI,IAAI,IAAI0H,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAAC7I,WAAW,CAAC8I,MAAM,IAAI,CAAC,CAAC,EAAE;gBACjM2D,MAAM,GAAG,KAAK;cAChB;YACF;YACA;YAEA,IAAI/E,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACjN,kBAAkB,IAAI,CAAC,IAAI8L,IAAI,CAAC3D,OAAO,CAAC8E,CAAC,CAAC,CAACrN,cAAc,IAAI,IAAI,EAAE;cACrF,IAAI,CAACsK,cAAc,CAAC+B,GAAG,CAAC;gBACtBC,QAAQ,EAAC,SAAS;gBAClBC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE;eACT,CAAC;cACFwE,mBAAmB,GAAG,KAAK;YAC7B;UACF;QACF;QAEA,IAAI,CAACC,MAAM,EAAE;UACXF,UAAU,GAAG,KAAK;UAClB,IAAI,CAACzG,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE;WACT,CAAC;QACJ,CAAC,MAAM,IAAI,CAAC0E,OAAO,EAAE;UACnBH,UAAU,GAAG,KAAK;UAClB,IAAI,CAACzG,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE;WACT,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAI,CAACwE,mBAAmB,EAAE;QACxB;MACF;MAEA,IAAI,CAACD,UAAU,EAAE;QACf;MACF;MAEA,IAAI,CAAC1G,0BAA0B,CAAC8G,eAAe,CAAC,IAAI,CAAC5G,QAAQ,CAAC,CAACuC,SAAS,CAAC;QACvEC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAACoE,MAAM,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC9G,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,SAAS;cAClBC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;YACF,IAAI,CAACmE,MAAM,EAAE;UACf,CAAC,MAAM,IAAIrE,QAAQ,CAACoE,MAAM,KAAK,SAAS,EAAE;YACxC,IAAI,CAAC9G,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,MAAM;cACfC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAAC5C,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,OAAO;cAChBC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;UACJ;QACF,CAAC;QACDyB,KAAK,EAAGA,KAAc,IAAI;UACxB,IAAI,CAACrE,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACJ;IAEA6E,MAAMA,CAAA;MACJ,IAAI,CAAClH,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAClD;IAEA6E,gBAAgBA,CAAA;MACd,IAAIC,WAAW,GAAG,CAAC;MACnB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,MAAM,EAAED,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAAC9C,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,IAAIiF,SAAS,EAAE;UACpDgE,WAAW,IAAI,IAAI,CAAChH,QAAQ,CAAC8C,CAAC,CAAC,CAAC/E,kBAAkB,CAACgF,MAAM;QAC3D;QAEA,IAAI,IAAI,CAAC/C,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,IAAIgF,SAAS,EAAE;UACzCgE,WAAW,IAAI,IAAI,CAAChH,QAAQ,CAAC8C,CAAC,CAAC,CAAC9E,OAAO,CAAC+E,MAAM;QAChD;MACF;MAEA,OAAOiE,WAAW;IACpB;IAEA1T,SAASA,CAACoM,MAAW,EAAEuH,OAAY;MACjC,IAAIvH,MAAM,IAAIA,MAAM,CAAC3B,kBAAkB,EAAE;QACvC,MAAMmJ,KAAK,GAAGxH,MAAM,CAAC3B,kBAAkB,CAACoJ,SAAS,CAAEjD,IAAS,IAC1DA,IAAI,CAAC1Q,WAAW,KAAKyT,OAAO,CAACzT,WAAW,IACxC0Q,IAAI,CAACxQ,eAAe,KAAKuT,OAAO,CAACvT,eAAe,CACjD;QACD,IAAIwT,KAAK,GAAG,CAAC,CAAC,EAAE;UACdxH,MAAM,CAAC3B,kBAAkB,CAACqJ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC1C,IAAI,CAACG,gBAAgB,CAAC3H,MAAM,CAAC;QAC/B;MACF;IACF;IAEQ2H,gBAAgBA,CAAC3H,MAAW;MAClC,MAAM4H,mBAAmB,GAAG,CAAC5H,MAAM,CAAC3B,kBAAkB,IAAI,EAAE,EAAEwJ,MAAM,CAAC,CAAChD,KAAa,EAAEiD,GAAQ,KAAI;QAC/F,OAAOjD,KAAK,IAAIiD,GAAG,CAAC9T,eAAe,IAAI,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC;MAELgM,MAAM,CAACrC,UAAU,GAAGiK,mBAAmB,GAAG,IAAI,CAAC3I,WAAW,CAACe,MAAM,CAAC;IACpE;IAEA;IACA5J,yBAAyBA,CAACoO,IAAS;MACjC,IAAIO,UAAU,CAACP,IAAI,CAAC7G,UAAU,CAAC,GAAGoH,UAAU,CAACP,IAAI,CAACuD,eAAe,CAAC,EAAE;QAClE,IAAIhD,UAAU,CAACP,IAAI,CAACrO,kBAAkB,CAAC,GAAG4O,UAAU,CAACP,IAAI,CAACuD,eAAe,CAAC,IAAIvD,IAAI,CAACrO,kBAAkB,IAAI,CAAC,EAAE;UAC1G,IAAI,CAACkK,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,SAAS;YAClBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE;WACT,CAAC;UACFiC,IAAI,CAACrO,kBAAkB,GAAG,CAAC;QAC7B;MACF,CAAC,MAAM;QACL,IAAI4O,UAAU,CAACP,IAAI,CAACrO,kBAAkB,CAAC,IAAI4O,UAAU,CAACP,IAAI,CAACuD,eAAe,CAAC,IAAIvD,IAAI,CAACrO,kBAAkB,IAAI,CAAC,EAAE;UAC3G,IAAI,CAACkK,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,SAAS;YAClBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE;WACT,CAAC;UACFiC,IAAI,CAACrO,kBAAkB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA;IACAX,gBAAgBA,CAACgP,IAAS;MACxB,IAAI,CAAC/D,YAAY,GAAG+D,IAAI;MACxB,IAAI,CAAC9D,eAAe,GAAG8D,IAAI,CAAC/O,WAAW,IAAI,CAAC;MAC5C,IAAI,CAAC+K,qBAAqB,GAAG,IAAI;IACnC;IAEA;IACAjB,iBAAiBA,CAAA;MACf,IAAI,CAACiB,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IAC1B;IAEA;IACAjB,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACgB,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAAChL,WAAW,GAAG,IAAI,CAACiL,eAAe;QACpD,IAAI,IAAI,CAACA,eAAe,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACD,YAAY,CAAC/C,UAAU,GAAG,aAAa;QAC9C,CAAC,MAAM;UACL,IAAI,CAAC+C,YAAY,CAAC/C,UAAU,GAAG,aAAa;QAC9C;MACF;MACA,IAAI,CAAC8C,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IAC1B;IAEA;IACA5C,UAAUA,CAAC0G,IAAS;MAClB,OAAOA,IAAI,EAAElO,YAAY,GAAGhE,MAAM,CAACkS,IAAI,CAAClO,YAAY,CAAC,CAAC+N,MAAM,EAAE,GAAG,IAAI;IACvE;IAEA;IACAtG,UAAUA,CAACyG,IAAS;MAClB,OAAOA,IAAI,EAAEF,eAAe,GAAGhS,MAAM,CAACkS,IAAI,CAACF,eAAe,CAAC,CAACD,MAAM,EAAE,GAAG,IAAI;IAC7E;IAEA;IACAxN,qBAAqBA,CAAC2N,IAAS;MAC7B,OAAOA,IAAI,EAAE5N,gBAAgB,GAAGtE,MAAM,CAACkS,IAAI,CAAC5N,gBAAgB,CAAC,CAACyN,MAAM,EAAE,GAAG,IAAI;IAC/E;IAEA;IACAvN,qBAAqBA,CAAC0N,IAAS;MAC7B,OAAOA,IAAI,EAAE5N,gBAAgB,GAAGtE,MAAM,CAACkS,IAAI,CAAC5N,gBAAgB,CAAC,CAACyN,MAAM,EAAE,GAAG,IAAI;IAC/E;IAEA;IACA3J,gBAAgBA,CAAC8J,IAAS;MACxB,IAAI,CAACnD,mBAAmB,GAAGmD,IAAI;MAC/B,IAAI,CAAClD,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACG,WAAW,GAAG;QACjBC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAACF,iBAAiB,EAAE;QACjEnI,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZyB,YAAY,EAAE,EAAE;QAChB4G,YAAY,EAAE,EAAE;QAChB5J,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACX2J,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;OACR;MACD,IAAI,CAACT,iBAAiB,GAAG,IAAI;IAC/B;IAEA;IACAxG,iBAAiBA,CAACL,WAAgB,EAAEiK,IAAS;MAC3C,IAAI,CAACnD,mBAAmB,GAAGmD,IAAI;MAC/B,IAAI,CAAClD,iBAAiB,GAAG,MAAM;MAC/B,IAAI,CAACC,cAAc,GAAGhH,WAAW;MACjC,IAAI,CAACkH,WAAW,GAAG;QACjBC,gBAAgB,EAAEnH,WAAW,CAACmH,gBAAgB;QAC9CrI,SAAS,EAAEkB,WAAW,CAAClB,SAAS;QAChCC,QAAQ,EAAEiB,WAAW,CAACjB,QAAQ;QAC9ByB,YAAY,EAAER,WAAW,CAACQ,YAAY;QACtC4G,YAAY,EAAEpH,WAAW,CAACoH,YAAY,IAAI,EAAE;QAC5C5J,IAAI,EAAEwC,WAAW,CAACxC,IAAI;QACtBC,KAAK,EAAEuC,WAAW,CAACvC,KAAK;QACxBC,OAAO,EAAEsC,WAAW,CAACtC,OAAO;QAC5B2J,KAAK,EAAErH,WAAW,CAACqH,KAAK;QACxBC,KAAK,EAAEtH,WAAW,CAACsH,KAAK,IAAI;OAC7B;MACD,IAAI,CAACT,iBAAiB,GAAG,IAAI;IAC/B;IAEA;IACAtB,WAAWA,CAAA;MACT,IAAI,IAAI,CAACwB,iBAAiB,KAAK,KAAK,EAAE;QACpC,MAAM0G,UAAU,GAAG;UAAE,GAAG,IAAI,CAACvG;QAAW,CAAE;QAE1C;QACA,IAAI,IAAI,CAACJ,mBAAmB,CAACxG,cAAc,EAAE;UAC3C,IAAI,CAACwG,mBAAmB,CAACxG,cAAc,CAACoN,IAAI,CAACD,UAAU,CAAC;QAC1D,CAAC,MAAM;UACL,IAAI,CAAC3G,mBAAmB,CAACxG,cAAc,GAAG,CAACmN,UAAU,CAAC;QACxD;QAEA;QACA,IAAI,CAAC3G,mBAAmB,CAAC9G,WAAW,GAAGyN,UAAU;QAEjD;QACA,IAAI,CAAC1H,QAAQ,CAAC6D,OAAO,CAAEnE,MAAW,IAAI;UACpC,IAAIA,MAAM,CAACkI,QAAQ,KAAK,IAAI,CAAC7G,mBAAmB,CAAC6G,QAAQ,EAAE;YACzDlI,MAAM,CAAC1B,OAAO,EAAE6F,OAAO,CAAEgE,IAAS,IAAI;cACpC,IAAIA,IAAI,CAACtN,cAAc,EAAE;gBACvBsN,IAAI,CAACtN,cAAc,CAACoN,IAAI,CAAC;kBAAE,GAAGD;gBAAU,CAAE,CAAC;gBAC3C,IAAIG,IAAI,CAAC5N,WAAW,EAAEmH,gBAAgB,EAAE0G,QAAQ,CAAC,oBAAoB,CAAC,EAAE;kBACtED,IAAI,CAACrN,cAAc,GAAG,IAAI;gBAC5B;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMuN,cAAc,GAAG;UAAE,GAAG,IAAI,CAAC5G;QAAW,CAAE;QAE9C;QACA,IAAI,CAACnB,QAAQ,CAAC6D,OAAO,CAAEnE,MAAW,IAAI;UACpC,IAAIA,MAAM,CAACkI,QAAQ,KAAK,IAAI,CAAC7G,mBAAmB,CAAC6G,QAAQ,EAAE;YACzDlI,MAAM,CAAC1B,OAAO,EAAE6F,OAAO,CAAEgE,IAAS,IAAI;cACpC,IAAIA,IAAI,CAACtN,cAAc,EAAE;gBACvB;gBACA,MAAM2M,KAAK,GAAGW,IAAI,CAACtN,cAAc,CAAC4M,SAAS,CACxC1D,CAAM,IAAKA,CAAC,CAACrC,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,CACxE;gBACD,IAAI8F,KAAK,GAAG,CAAC,CAAC,EAAE;kBACdW,IAAI,CAACtN,cAAc,CAAC2M,KAAK,CAAC,GAAGa,cAAc;gBAC7C;gBAEA;gBACA,IAAIF,IAAI,CAAC5N,WAAW,EAAEmH,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,EAAE;kBAC/EyG,IAAI,CAAC5N,WAAW,GAAG8N,cAAc;gBACnC;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAACjH,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC5B;IAEA;IACA3B,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAAC0B,iBAAiB,KAAK,KAAK,EAAE;QACpC,IAAI,CAACE,iBAAiB,EAAE;MAC1B;MACA,IAAI,CAACJ,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC5B;IAEA;IACAxI,qBAAqBA,CAACyL,IAAS;MAC7B,IAAIA,IAAI,CAACjK,WAAW,EAAEmH,gBAAgB,EAAE0G,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACtE5D,IAAI,CAAC1J,cAAc,GAAG,IAAI;MAC5B,CAAC,MAAM;QACL0J,IAAI,CAAC1J,cAAc,GAAG,KAAK;MAC7B;IACF;IAEA;IACA0B,YAAYA,CAACwD,MAAW,EAAEwE,IAAS;MACjC,IAAIxE,MAAM,CAAC1B,OAAO,EAAE;QAClB,MAAMkJ,KAAK,GAAGxH,MAAM,CAAC1B,OAAO,CAACmJ,SAAS,CAAErD,GAAQ,IAAKA,GAAG,KAAKI,IAAI,CAAC;QAClE,IAAIgD,KAAK,GAAG,CAAC,CAAC,EAAE;UACdxH,MAAM,CAAC1B,OAAO,CAACoJ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC;MACF;IACF;IAEA;IACAvL,SAASA,CAACuI,IAAS;MACjB,IAAIA,IAAI,CAAC8D,iBAAiB,EAAE;QAC1BC,MAAM,CAACC,IAAI,CAAChE,IAAI,CAAC8D,iBAAiB,EAAE,QAAQ,CAAC;MAC/C;IACF;IAAC,QAAAG,CAAA,G;uBAhqCU1I,oBAAoB,EAAAvN,EAAA,CAAAkW,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApW,EAAA,CAAAkW,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAArW,EAAA,CAAAkW,iBAAA,CAAAI,EAAA,CAAAC,0BAAA,GAAAvW,EAAA,CAAAkW,iBAAA,CAAAM,EAAA,CAAAzW,cAAA;IAAA;IAAA,QAAA0W,EAAA,G;YAApBlJ,oBAAoB;MAAAmJ,SAAA;MAAAC,QAAA,GAAA3W,EAAA,CAAA4W,kBAAA,CAFpB,CAAC7W,cAAc,CAAC;MAAA8W,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRzBlX,EAFJ,CAAAM,cAAA,aAA8B,aACsC,aAC9C;UAAAN,EAAA,CAAAC,MAAA,cAAO;UAAAD,EAAA,CAAAM,cAAA,cAAyC;UAAAN,EAAA,CAAAC,MAAA,GAAyB;;UAAOD,EAAP,CAAAO,YAAA,EAAO,EAAM;UAEtGP,EADF,CAAAM,cAAA,aAA2C,gBACkD;UAAnBN,EAAA,CAAAU,UAAA,mBAAA0W,sDAAA;YAAA,OAASD,GAAA,CAAAvC,MAAA,EAAQ;UAAA,EAAC;UAAC5U,EAAA,CAAAO,YAAA,EAAS;UACpGP,EAAA,CAAAM,cAAA,gBACwD;UAAhBN,EAAA,CAAAU,UAAA,mBAAA2W,sDAAA;YAAA,OAASF,GAAA,CAAA9C,GAAA,EAAK;UAAA,EAAC;UAE3DrU,EAF4D,CAAAO,YAAA,EAAS,EAC7D,EACF;UACNP,EAAA,CAAAkC,UAAA,KAAAoV,oCAAA,mBAA6F;UAghB/FtX,EAAA,CAAAM,cAAA,mBAQqB;UAPnBN,EAAA,CAAAkD,gBAAA,2BAAAqU,iEAAAnU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAnJ,qBAAA,EAAA5K,MAAA,MAAA+T,GAAA,CAAAnJ,qBAAA,GAAA5K,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAU/BpD,EAFJ,CAAAM,cAAA,cAAwC,eACuB,iBAClC;UAAAN,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACvCP,EAAA,CAAAM,cAAA,yBAQyC;UANvCN,EAAA,CAAAkD,gBAAA,2BAAAsU,sEAAApU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAjJ,eAAA,EAAA9K,MAAA,MAAA+T,GAAA,CAAAjJ,eAAA,GAAA9K,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UASnCpD,EAFI,CAAAO,YAAA,EAAgB,EACZ,EACF;UACNP,EAAA,CAAAkC,UAAA,KAAAuV,4CAAA,0BAAgC;UAMlCzX,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAM,cAAA,oBAQqB;UAPnBN,EAAA,CAAAkD,gBAAA,2BAAAwU,iEAAAtU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAvI,iBAAA,EAAAxL,MAAA,MAAA+T,GAAA,CAAAvI,iBAAA,GAAAxL,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAYzBpD,EAJN,CAAAM,cAAA,cAAwC,eAEF,eAC2B,iBACpC;UAAAN,EAAA,CAAAC,MAAA,oBAAY;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAC3CP,EAAA,CAAAM,cAAA,iBAAgF;UAA/CN,EAAA,CAAAkD,gBAAA,2BAAAyU,8DAAAvU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAApI,SAAA,EAAAzD,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAApI,SAAA,GAAAzD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UACtEpD,EADE,CAAAO,YAAA,EAAgF,EAC5E;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACrC;UAAAN,EAAA,CAAAC,MAAA,mBAAW;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACzCP,EAAA,CAAAM,cAAA,iBAA8E;UAA9CN,EAAA,CAAAkD,gBAAA,2BAAA0U,8DAAAxU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAnI,QAAA,EAAA1D,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAnI,QAAA,GAAA1D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UACpEpD,EADE,CAAAO,YAAA,EAA8E,EAC1E;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACjC;UAAAN,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClDP,EAAA,CAAAM,cAAA,iBAAsF;UAAlDN,EAAA,CAAAkD,gBAAA,2BAAA2U,8DAAAzU,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAA1G,YAAA,EAAAnF,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAA1G,YAAA,GAAAnF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAC5EpD,EADE,CAAAO,YAAA,EAAsF,EAClF;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACjC;UAAAN,EAAA,CAAAC,MAAA,sBAAc;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChDP,EAAA,CAAAM,cAAA,iBAA6E;UAAzCN,EAAA,CAAAkD,gBAAA,2BAAA4U,8DAAA1U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAE,YAAA,EAAA/L,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAE,YAAA,GAAA/L,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAC5EpD,EADE,CAAAO,YAAA,EAA6E,EACzE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACzC;UAAAN,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChCP,EAAA,CAAAM,cAAA,iBAAsE;UAA1CN,EAAA,CAAAkD,gBAAA,2BAAA6U,8DAAA3U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAA1J,IAAA,EAAAnC,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAA1J,IAAA,GAAAnC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAC5DpD,EADE,CAAAO,YAAA,EAAsE,EAClE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClCP,EAAA,CAAAM,cAAA,sBAMqB;UALTN,EAAA,CAAAkD,gBAAA,2BAAA8U,mEAAA5U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAzJ,KAAA,EAAApC,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAzJ,KAAA,GAAApC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAO7CpD,EADE,CAAAO,YAAA,EAAa,EACT;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACtC;UAAAN,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACvCP,EAAA,CAAAM,cAAA,iBAA4E;UAA7CN,EAAA,CAAAkD,gBAAA,2BAAA+U,8DAAA7U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAxJ,OAAA,EAAArC,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAxJ,OAAA,GAAArC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAClEpD,EADE,CAAAO,YAAA,EAA4E,EACxE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClCP,EAAA,CAAAM,cAAA,iBAAwE;UAA3CN,EAAA,CAAAkD,gBAAA,2BAAAgV,8DAAA9U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAG,KAAA,EAAAhM,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAG,KAAA,GAAAhM,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC9DpD,EADE,CAAAO,YAAA,EAAwE,EACpE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChCP,EAAA,CAAAM,cAAA,iBAA+D;UAAlCN,EAAA,CAAAkD,gBAAA,2BAAAiV,8DAAA/U,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA6T,GAAA,CAAAlI,WAAA,CAAAI,KAAA,EAAAjM,MAAA,MAAA+T,GAAA,CAAAlI,WAAA,CAAAI,KAAA,GAAAjM,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAGlEpD,EAHM,CAAAO,YAAA,EAA+D,EAC3D,EACF,EACF;UAENP,EAAA,CAAAkC,UAAA,KAAAkW,4CAAA,0BAAgC;UAMlCpY,EAAA,CAAAO,YAAA,EAAW,EAloBmB;;;UAEwCP,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,QAAA4V,GAAA,CAAA/E,QAAA,IAAyB;UAIvFpS,EAAA,CAAAQ,SAAA,GAAqC;UAArCR,EAAA,CAAAwC,UAAA,aAAA2U,GAAA,CAAAtC,gBAAA,SAAqC;UAGqC7U,EAAA,CAAAQ,SAAA,EAAW;UAAXR,EAAA,CAAAwC,UAAA,YAAA2U,GAAA,CAAArJ,QAAA,CAAW;UAqhB3F9N,EAAA,CAAAQ,SAAA,EAAyB;UAAzBR,EAAA,CAAAqY,UAAA,CAAArY,EAAA,CAAAsY,eAAA,KAAAC,GAAA,EAAyB;UAJzBvY,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAnJ,qBAAA,CAAmC;UAOnChO,EANA,CAAAwC,UAAA,eAAc,oBACK,oBACA,wBAGI,mBACL;UAMZxC,EAAA,CAAAQ,SAAA,GAA6B;UAA7BR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAjJ,eAAA,CAA6B;UAM7BlO,EAHA,CAAAwC,UAAA,wBAAuB,wBACA,iBACP,QAAA2U,GAAA,CAAAlJ,YAAA,kBAAAkJ,GAAA,CAAAlJ,YAAA,CAAAuK,gBAAA,CACsB;UAkB5CxY,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAqY,UAAA,CAAArY,EAAA,CAAAsY,eAAA,KAAAG,GAAA,EAAyB;UAJzBzY,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAvI,iBAAA,CAA+B;UAO/B5O,EANA,CAAAwC,UAAA,eAAc,oBACK,oBACA,WAAA2U,GAAA,CAAArI,iBAAA,4CAEoD,wBAChD,mBACL;UAMqB9O,EAAA,CAAAQ,SAAA,GAAmC;UAAnCR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAApI,SAAA,CAAmC;UAKpC7G,EAAA,CAAAQ,SAAA,GAAkC;UAAlCR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAnI,QAAA,CAAkC;UAK9B9G,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAA1G,YAAA,CAAsC;UAKtCvI,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAE,YAAA,CAAsC;UAK9CnP,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAA1J,IAAA,CAA8B;UAM9CvF,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAzJ,KAAA,CAA+B;UAC/BxF,EAAA,CAAAwC,UAAA,YAAA2U,GAAA,CAAApJ,SAAA,CAAqB;UAUF/N,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAxJ,OAAA,CAAiC;UAKnCzF,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAG,KAAA,CAA+B;UAK/BpP,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA2T,GAAA,CAAAlI,WAAA,CAAAI,KAAA,CAA+B;;;;;;;SD3mBvD9B,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}