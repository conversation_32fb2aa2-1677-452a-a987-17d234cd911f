{"ast": null, "code": "import { SEVERITY_ERROR } from \"../../../../domain/constant\";\nimport _ from \"lodash\";\nimport moment from \"moment\";\nimport { CheckInComponent } from \"../pop/check-in/check-in.component\";\nimport { PreviewComponent } from \"../../../../component/preview/preview.component\";\nimport { TitleDetailAndEditComponent } from \"../pop/title-detail-and-edit/title-detail-and-edit.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../service/common/storage/storage.service\";\nimport * as i2 from \"../../../../service/system/data-dictionary/data-dictionary.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../../service/title/title.service\";\nimport * as i5 from \"../../../../service/system/file/file-management.service\";\nimport * as i6 from \"../../../../service/common/loading/loadingService\";\nimport * as i7 from \"../../../../service/common/msg/msg.service\";\nimport * as i8 from \"../../../../util/file-util/file-util.service\";\nimport * as i9 from \"../../../../service/common/premission/permission.service\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/api\";\nimport * as i12 from \"primeng/table\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/tooltip\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"@angular/forms\";\nimport * as i17 from \"primeng/multiselect\";\nimport * as i18 from \"primeng/ripple\";\nimport * as i19 from \"../pop/title-detail-and-edit/title-detail-and-edit.component\";\nimport * as i20 from \"../../../../component/preview/preview.component\";\nimport * as i21 from \"../../../loan/loan-management/popup/loan-pop-soldExt/loan-pop-soldExt.component\";\nimport * as i22 from \"../pop/check-in/check-in.component\";\nimport * as i23 from \"../../../../pipe/phone.pipe\";\nconst _c0 = [\"dt\"];\nconst _c1 = () => [10, 25, 50, 100];\nconst _c2 = () => ({\n  \"min-width\": \"60rem\"\n});\nconst _c3 = a0 => ({\n  \"dqRed\": a0\n});\nfunction TitleManagementComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2, \"Dealer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_22_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"dealerName\"], $event) || (ctx_r2.formData[\"dealerName\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"dealerName\"]);\n  }\n}\nfunction TitleManagementComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2, \"VIN ( or Last 6 digits VIN)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_23_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"vin\"], $event) || (ctx_r2.formData[\"vin\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"vin\"]);\n  }\n}\nfunction TitleManagementComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 11);\n    i0.ɵɵtext(2, \"Sent Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"p-calendar\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_24_Template_p_calendar_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"releasedDateFrom\"], $event) || (ctx_r2.formData[\"releasedDateFrom\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40);\n    i0.ɵɵtext(6, \" - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p-calendar\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_24_Template_p_calendar_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"releasedDateTo\"], $event) || (ctx_r2.formData[\"releasedDateTo\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showButtonBar\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"releasedDateFrom\"]);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"maxDate\", ctx_r2.formData[\"releasedDateTo\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"releasedDateTo\"]);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"minDate\", ctx_r2.formData[\"releasedDateFrom\"]);\n  }\n}\nfunction TitleManagementComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 11);\n    i0.ɵɵtext(2, \"Received Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"p-calendar\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_25_Template_p_calendar_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"receivedDateFrom\"], $event) || (ctx_r2.formData[\"receivedDateFrom\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40);\n    i0.ɵɵtext(6, \" - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p-calendar\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_25_Template_p_calendar_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"receivedDateTo\"], $event) || (ctx_r2.formData[\"receivedDateTo\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showButtonBar\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"receivedDateFrom\"]);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"maxDate\", ctx_r2.formData[\"receivedDateTo\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"receivedDateTo\"]);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"minDate\", ctx_r2.formData[\"receivedDateFrom\"]);\n  }\n}\nfunction TitleManagementComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2, \"Title Release Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-multiSelect\", 12);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_div_26_Template_p_multiSelect_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formData[\"titleReleaseType\"], $event) || (ctx_r2.formData[\"titleReleaseType\"] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r2.titleReleaseTypeSelect);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formData[\"titleReleaseType\"]);\n    i0.ɵɵproperty(\"showClear\", true);\n  }\n}\nfunction TitleManagementComponent_img_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 42);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_img_28_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.collapsed = !ctx_r2.collapsed);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TitleManagementComponent_img_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 43);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_img_29_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.collapsed = !ctx_r2.collapsed);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TitleManagementComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \" Loan ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Loan Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Dealer Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"DBA Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Legal Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 44);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_46_Template_th_click_11_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.sort(\"SN\"));\n    });\n    i0.ɵɵtext(12, \"Source Name \");\n    i0.ɵɵelement(13, \"p-sortIcon\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"VIN\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 46);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_46_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.sort(\"OP\"));\n    });\n    i0.ɵɵtext(17, \"Original Principal \");\n    i0.ɵɵelement(18, \"p-sortIcon\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Remaining Principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Title Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\");\n    i0.ɵɵtext(24, \"Title Release Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\");\n    i0.ɵɵtext(26, \"Loan Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\");\n    i0.ɵɵtext(28, \"Tracking No. from Source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 48);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_46_Template_th_click_29_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.sort(\"RD\"));\n    });\n    i0.ɵɵtext(30, \" Received Date \");\n    i0.ɵɵelement(31, \"p-sortIcon\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"Sent Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\");\n    i0.ɵɵtext(35, \"Tracking No. to Dealer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\");\n    i0.ɵɵtext(37, \"IsFunded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\");\n    i0.ɵɵtext(39, \"IsScheduled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\", 50);\n    i0.ɵɵtext(41, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = ctx.$implicit;\n    const title_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", detail_r12.label, \" :\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.loanTib[detail_r12.key]);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, TitleManagementComponent_ng_template_47_ng_template_4_div_1_Template, 5, 2, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.loanDetails);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(title_r13.dealerTib.orgStatus);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const detail_r14 = i0.ɵɵnextContext().$implicit;\n    const title_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, title_r13.dealerTib[detail_r14.key]));\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r14 = i0.ɵɵnextContext().$implicit;\n    const title_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(title_r13.dealerTib[detail_r14.key]);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_container_3_Template, 4, 3, \"ng-container\", 75)(4, TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_template_4_Template, 2, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r14 = ctx.$implicit;\n    const normalDisplay_r15 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", detail_r14.label, \" :\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r14.key === \"creditLineUtilization\" || detail_r14.key === \"creditLineInfo\")(\"ngIfElse\", normalDisplay_r15);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, TitleManagementComponent_ng_template_47_ng_template_12_div_1_Template, 6, 3, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.dealerDetails);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(title_r13.dealerTib.orgStatus);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(title_r13.dealerTib.orgStatus);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_25_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2, \"tel: \");\n    i0.ɵɵelementStart(3, \"a\", 78);\n    i0.ɵɵpipe(4, \"phone\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \", \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const dealer_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"tel:\" + i0.ɵɵpipeBind1(4, 2, dealer_r16.phone), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 4, dealer_r16.phone));\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_25_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TitleManagementComponent_ng_template_47_ng_template_25_li_1_ng_container_4_Template, 8, 6, \"ng-container\", 33);\n    i0.ɵɵelementStart(5, \"a\", 77);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const dealer_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dealer_r16.contactTypeText, \" : \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", dealer_r16.firstName, \" \", dealer_r16.lastName, \", \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dealer_r16.phone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", \"mailto:\" + dealer_r16.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dealer_r16.email);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, TitleManagementComponent_ng_template_47_ng_template_25_li_1_Template, 7, 6, \"li\", 76);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", title_r13.contactTibsM);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const title_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toDetailOrEdit(title_r13, \"edit\"));\n    });\n    i0.ɵɵelement(1, \"img\", 80);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TitleManagementComponent_ng_template_47_button_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_button_73_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const title_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleAddDelete(title_r13, ctx_r2.checkAddDeleteByLoanId(title_r13.loanId) ? \"add\" : \"delete\"));\n    });\n    i0.ɵɵelement(1, \"img\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r2.checkAddDeleteByLoanId(title_r13.loanId) ? \"Add\" : \"Remove\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.checkStatus(title_r13.titleStatus))(\"ngClass\", i0.ɵɵpureFunction1(4, _c3, title_r13.isInComplete === true));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.checkAddDeleteByLoanId(title_r13.loanId) ? \"./assets/img/add.png\" : \"./assets/img/loan-transaction-cancel.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction TitleManagementComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 51)(1, \"td\", 52)(2, \"span\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, TitleManagementComponent_ng_template_47_ng_template_4_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 52)(9, \"span\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TitleManagementComponent_ng_template_47_span_11_Template, 2, 1, \"span\", 55)(12, TitleManagementComponent_ng_template_47_ng_template_12_Template, 2, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 52)(15, \"span\", 54);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TitleManagementComponent_ng_template_47_span_17_Template, 2, 1, \"span\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 52)(19, \"span\", 54);\n    i0.ɵɵtext(20);\n    i0.ɵɵtemplate(21, TitleManagementComponent_ng_template_47_span_21_Template, 2, 1, \"span\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 52)(23, \"span\", 54);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TitleManagementComponent_ng_template_47_ng_template_25_Template, 2, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"td\");\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\");\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"td\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"td\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"td\");\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\", 56);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_Template_td_click_41_listener() {\n      const title_r13 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(title_r13.trackingNoFromSuplier && ctx_r2.openTrakingNumberFromDba(title_r13.trackingNoFromSuplier));\n    });\n    i0.ɵɵelementStart(42, \"span\", 53);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"td\");\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"td\");\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"td\", 56);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_Template_td_click_50_listener() {\n      const title_r13 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(title_r13.trackingNumber && ctx_r2.openTrakingNumberFromDba(title_r13.carrierFullUrl));\n    });\n    i0.ɵɵelementStart(51, \"span\", 53);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"td\");\n    i0.ɵɵelement(54, \"input\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"td\");\n    i0.ɵɵelement(56, \"input\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"td\", 58)(58, \"div\", 59)(59, \"span\", 60)(60, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_Template_button_click_60_listener() {\n      const title_r13 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewFile(title_r13.fileManagementUrl, title_r13.accountNumber));\n    });\n    i0.ɵɵelement(61, \"img\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"span\", 63);\n    i0.ɵɵtext(63, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"span\", 64)(65, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function TitleManagementComponent_ng_template_47_Template_button_click_65_listener() {\n      const title_r13 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toDetailOrEdit(title_r13, \"details\"));\n    });\n    i0.ɵɵelement(66, \"img\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"span\", 63);\n    i0.ɵɵtext(68, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(69, TitleManagementComponent_ng_template_47_button_69_Template, 2, 0, \"button\", 67);\n    i0.ɵɵelementStart(70, \"span\", 63);\n    i0.ɵɵtext(71, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\", 68);\n    i0.ɵɵtemplate(73, TitleManagementComponent_ng_template_47_button_73_Template, 2, 6, \"button\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const title_r13 = ctx.$implicit;\n    const accountNumber_r19 = i0.ɵɵreference(5);\n    const reference_r20 = i0.ɵɵreference(13);\n    const sourceName_r21 = i0.ɵɵreference(26);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c3, title_r13.isInComplete === true));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", accountNumber_r19)(\"autoHide\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.accountNumber);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(title_r13.loanTib.loanTypeDisp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", reference_r20)(\"autoHide\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.reference);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", title_r13.dealerTib.orgStatus != \"A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"pTooltip\", reference_r20)(\"autoHide\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.dba);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", title_r13.dealerTib.orgStatus != \"A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", reference_r20)(\"autoHide\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", title_r13.legalName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", title_r13.dealerTib.orgStatus != \"A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", title_r13.sourceName ? sourceName_r21 : \"\")(\"autoHide\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.sourceName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(title_r13.vin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 43, title_r13.cost));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(34, 45, title_r13.currentCost));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(title_r13.titleStatusText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.titleReleaseTypeText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.loanStatusText);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(title_r13.trackingNoFromSuplier ? \"cursor-pointer\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.trackingNoFromSuplier);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(46, 47, title_r13.strReceivedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(49, 50, title_r13.strReleasedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(title_r13.trackingNumber ? \"cursor-pointer\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(title_r13.trackingNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true)(\"ngModel\", title_r13.isFunded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true)(\"ngModel\", title_r13.isScheduled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", title_r13.isHasTitleFile == false)(\"ngClass\", i0.ɵɵpureFunction1(55, _c3, title_r13.isInComplete === true));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isBtnVisible(\"Title_Edit\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r2.checkAddDeleteByLoanId(title_r13.loanId) ? \"Add\" : \"Remove\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isBtnVisible(\"Title_Add\"));\n  }\n}\nfunction TitleManagementComponent_48_ng_template_0_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 84);\n    i0.ɵɵtext(2, \"No Data Available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TitleManagementComponent_48_ng_template_0_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 85);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TitleManagementComponent_48_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TitleManagementComponent_48_ng_template_0_tr_0_Template, 3, 0, \"tr\", 33)(1, TitleManagementComponent_48_ng_template_0_tr_1_Template, 2, 0, \"tr\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTitleList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isTitleList);\n  }\n}\nfunction TitleManagementComponent_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TitleManagementComponent_48_ng_template_0_Template, 2, 2, \"ng-template\", 83);\n  }\n}\nexport let TitleManagementComponent = /*#__PURE__*/(() => {\n  class TitleManagementComponent {\n    constructor(appStorageService, dataDictionaryService, datePipe, titleService, fileManagementService, loadingService, msgService, fileUtil, permissionService) {\n      this.appStorageService = appStorageService;\n      this.dataDictionaryService = dataDictionaryService;\n      this.datePipe = datePipe;\n      this.titleService = titleService;\n      this.fileManagementService = fileManagementService;\n      this.loadingService = loadingService;\n      this.msgService = msgService;\n      this.fileUtil = fileUtil;\n      this.permissionService = permissionService;\n      this.isTitleList = false;\n      this.isTooltipVisible = false;\n      this.titleList = [];\n      this.isAdding = true;\n      this.isLoanList = false;\n      this.loanIdList = [];\n      this.loanStatusSelect = [];\n      this.titleStatusSelect = [];\n      this.loanTypeSelect = [];\n      this.titleReleaseTypeSelect = [];\n      this.dealerStatusSelect = [];\n      this.isFundedSelect = [{\n        value: true,\n        text: 'Yes'\n      }, {\n        value: false,\n        text: 'No'\n      }];\n      this.formData = {};\n      this.sortOptions = {};\n      this.first = 1;\n      this.pageSize = 10;\n      this.paidTotal = 0;\n      this.collapsed = false;\n      this.loan = {};\n      this.asset = {};\n      this.dataDictionaryList = [];\n      this.searchParam = {};\n      this.permissionList = [];\n      this.loanDetails = [{\n        label: 'Loan ID',\n        key: 'accountNumber'\n      }, {\n        label: 'Loan Type',\n        key: 'loanTypeDisp'\n      }, {\n        label: 'Vehicle Info',\n        key: 'vehicleInfo'\n      }, {\n        label: 'Vin',\n        key: 'vin'\n      }, {\n        label: 'Dealer Name',\n        key: 'dealerName'\n      }, {\n        label: 'Source Name',\n        key: 'sourceName'\n      }, {\n        label: 'Floored Date',\n        key: 'acceptanceDate'\n      }, {\n        label: 'Payoff Date',\n        key: 'maturityDate'\n      }, {\n        label: 'Loan Status',\n        key: 'loanStatusDisP'\n      }];\n      this.dealerDetails = [{\n        label: 'Dealer Code',\n        key: 'reference'\n      }, {\n        label: 'Dealer Name',\n        key: 'dba'\n      }, {\n        label: 'Limit',\n        key: 'creditLineInfo'\n      }, {\n        label: 'Utilization',\n        key: 'creditLineUtilization'\n      }, {\n        label: 'Live Loans',\n        key: 'liveLoans'\n      }, {\n        label: 'On Hold',\n        key: 'isOnHold'\n      }, {\n        label: 'Boss Dealer',\n        key: 'isBossDealer'\n      }, {\n        label: 'Dealer Status',\n        key: 'orgStatusText'\n      }];\n    }\n    ngOnInit() {\n      this.loanIdList = [];\n      this.getButtonPermission();\n      this.getDropdownData();\n    }\n    // Getting the data for a dropdown value by data dictionary\n    getDropdownData() {\n      this.loadingService.show();\n      const search = {\n        condition: {\n          dataDictionaryTypeName_In: ['LoanStatus', 'TitleStatus', 'LoanType', 'TitleReleaseType', 'OrgStatus']\n        },\n        sort: []\n      };\n      this.dataDictionaryService.getDataDictionaryItems(search).subscribe({\n        next: response => {\n          this.loadingService.hide();\n          const data = response.data.content;\n          if (data) {\n            this.dataDictionaryList = data;\n            this.loanStatusSelect = data.filter(item => item['dataDictionaryTypeName'] === 'LoanStatus' && ['Cancelled', 'Charged Off', 'Paid', 'Live', 'Recovered'].includes(item['text']));\n            this.titleStatusSelect = data.filter(item => item['dataDictionaryTypeName'] === 'TitleStatus');\n            this.loanTypeSelect = data.filter(item => item['dataDictionaryTypeName'] === 'LoanType');\n            this.titleReleaseTypeSelect = data.filter(item => item['dataDictionaryTypeName'] === 'TitleReleaseType');\n            this.dealerStatusSelect = data.filter(item => item['dataDictionaryTypeName'] === 'OrgStatus');\n          }\n        },\n        error: () => {\n          this.loadingService.hide();\n        }\n      });\n    }\n    // Search data with optional page and page size parameters\n    searchData(first = 0, rows = this.pageSize, resetPage = false) {\n      if (resetPage) {\n        this.first = 0;\n      } else {\n        this.first = first;\n      }\n      this.pageSize = rows;\n      let search = {\n        Filters: this.formatSearchData(),\n        PageIndex: first,\n        PageSize: rows,\n        Sorting: this.sortOptions['property'] ? {\n          [this.sortOptions['property']]: this.sortOptions['direction']\n        } : {}\n      };\n      this.searchParam = search;\n      this.titleManagementSearch(search);\n      this.isTitleList = true;\n    }\n    openTrakingNumberFromDba(trackingNoFromSuplier) {\n      window.open('https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber=' + trackingNoFromSuplier);\n    }\n    openTrakingNumber(url) {\n      window.open(url);\n    }\n    showTooltip() {\n      this.isTooltipVisible = true;\n    }\n    hideTooltip() {\n      this.isTooltipVisible = false;\n    }\n    viewFile(fileManagementUrl, accountNumber) {\n      let body = {\n        filePath: fileManagementUrl,\n        fileFrom: 'FTP'\n      };\n      this.fileManagementService.getBase64Str(body).subscribe({\n        next: e => {\n          if (e.data) {\n            // download file if using IE\n            if (window.navigator.userAgent.indexOf(\"Trident\") > -1) {\n              const titleFileName = 'Title' + accountNumber + '.pdf';\n              this.fileUtil.downloadImageFromBase64(e.data, titleFileName);\n            } else {\n              this.previewComponent.showDialog(e.data, 'application/pdf');\n            }\n          } else {\n            let data = {\n              summary: 'Alert',\n              severity: \"warning\",\n              detail: \"No title file available.\",\n              isLeftButton: false,\n              leftButtonLabel: \"\",\n              isRightButton: true,\n              rightButtonLabel: 'OK'\n            };\n            this.msgService.notification(data);\n          }\n        }\n      });\n    }\n    sort(param) {\n      if (!this.isTitleList) {\n        return;\n      }\n      if (this.sortOptions['property'] === param && this.sortOptions['property'] != undefined) {\n        if (this.sortOptions['direction'] === 'asc') {\n          this.sortOptions['direction'] = 'desc';\n        } else {\n          this.sortOptions['direction'] = 'asc';\n        }\n      } else {\n        this.sortOptions['property'] = param;\n        this.sortOptions['direction'] = 'asc';\n      }\n      let sorting = {};\n      sorting[this.sortOptions['property']] = this.sortOptions['direction'];\n      const search = {\n        Filters: this.formatSearchData(),\n        PageIndex: 0,\n        PageSize: 10,\n        Sorting: sorting\n      };\n      this.searchParam = search;\n      this.titleManagementSearch(search);\n    }\n    titleManagementSearch(search) {\n      this.loadingService.show();\n      this.titleService.searchTitleDataList(search).subscribe({\n        next: e => {\n          this.loadingService.hide();\n          if (e.data) {\n            this.titleList = e.data['results'];\n            this.paidTotal = e.data['total'];\n            if (this.titleList && this.titleList.length > 0) {\n              this.titleList.forEach(item => {\n                if (item.loanTib && item.loanTib.acceptanceDate) {\n                  item.loanTib.acceptanceDate = this.formatDateString(item.loanTib.acceptanceDate);\n                  item.loanTib.maturityDate = this.formatDateString(item.loanTib.maturityDate);\n                }\n              });\n            } else {\n              this.paidTotal = 0;\n              this.pageSize = 10;\n            }\n          }\n        },\n        error: () => {\n          this.loadingService.hide();\n        }\n      });\n    }\n    formatDateString(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      const yyyy = date.getFullYear();\n      return `${mm}/${dd}/${yyyy}`;\n    }\n    toDetailOrEdit(title, action) {\n      this.titleDetailAndEditComponent.showDialog(this, title, action);\n    }\n    handleAddDelete(title, action) {\n      if (action === 'add') {\n        this.loanIdList.push(title);\n      } else if (action === 'delete') {\n        const index = this.loanIdList.findIndex(item => item.loanId === title.loanId);\n        if (index !== -1) {\n          this.loanIdList.splice(index, 1);\n        }\n        this.isAdding = true;\n      }\n    }\n    checkAddDeleteByLoanId(loanId) {\n      return !this.loanIdList.find(item => item.loanId === loanId);\n    }\n    checkStatus(status) {\n      if (status == 'HR' || status == 'PRLT') {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    export() {\n      this.loadingService.show();\n      let search = {\n        Filters: this.formatSearchData(),\n        PageIndex: 0,\n        PageSize: 2100000000,\n        Sorting: {},\n        IsPaging: true\n      };\n      this.titleService.exportTitleData(search).subscribe({\n        next: res => {\n          this.loadingService.hide();\n          const url = window.URL.createObjectURL(res);\n          const a = document.createElement('a');\n          a.setAttribute('style', 'display:none');\n          document.body.appendChild(a);\n          a.href = url;\n          a.download = this.generateFileName();\n          a.click();\n          window.URL.revokeObjectURL(url);\n          document.body.removeChild(a);\n        },\n        error: () => {\n          this.loadingService.hide();\n          let dialogData = {\n            summary: 'Export data error',\n            severity: SEVERITY_ERROR,\n            detail: 'Failed, please check and try again!',\n            isLeftButton: false,\n            leftButtonLabel: \"\",\n            isRightButton: true,\n            rightButtonLabel: 'OK'\n          };\n          this.msgService.notification(dialogData).onClose.subscribe();\n        }\n      });\n    }\n    generateFileName() {\n      const timestamp = moment(new Date()).format('MMDDyyyyHHmmss');\n      return `Title_${timestamp}.xls`;\n    }\n    showCheckin(title) {\n      this.checkIn.showDialog(this, title);\n    }\n    refreshData() {\n      this.searchData();\n    }\n    reset() {\n      this.formData = {};\n    }\n    moment(date, format) {\n      return date ? this.datePipe.transform(date, format) : null;\n    }\n    remove(titleList) {\n      this.loanIdList = titleList;\n    }\n    handleAction(event, first, pageSize) {\n      switch (event.action) {\n        case 'remove':\n          this.remove(event.title);\n          break;\n        case 'refreshData':\n          this.loanIdList = [];\n          this.searchData(first, pageSize);\n          break;\n      }\n    }\n    formatSearchData() {\n      const cloneFromData = _.cloneDeep(this.formData);\n      if (!this.isEmpty(cloneFromData['releasedDateFrom'])) {\n        cloneFromData['releasedDateFrom'] = this.moment(cloneFromData['releasedDateFrom'], 'MM/dd/yyyy');\n      }\n      if (!this.isEmpty(cloneFromData['releasedDateTo'])) {\n        cloneFromData['releasedDateTo'] = this.moment(cloneFromData['releasedDateTo'], 'MM/dd/yyyy');\n      }\n      if (!this.isEmpty(cloneFromData['receivedDateFrom'])) {\n        cloneFromData['receivedDateFrom'] = this.moment(cloneFromData['receivedDateFrom'], 'MM/dd/yyyy');\n      }\n      if (!this.isEmpty(cloneFromData['receivedDateTo'])) {\n        cloneFromData['receivedDateTo'] = this.moment(cloneFromData['receivedDateTo'], 'MM/dd/yyyy');\n      }\n      cloneFromData['userId'] = JSON.parse(this.appStorageService.getStoredSessionStorageByKey('userId'));\n      cloneFromData['LoanId'] = cloneFromData['LoanId'] == '' ? null : cloneFromData['LoanId'];\n      if (cloneFromData['loanStatus'] && Array.isArray(cloneFromData['loanStatus'])) {\n        cloneFromData['loanStatus'] = cloneFromData['loanStatus'].join('/');\n      }\n      if (cloneFromData['titleStatus'] && Array.isArray(cloneFromData['titleStatus'])) {\n        cloneFromData['titleStatus'] = cloneFromData['titleStatus'].join('/');\n      }\n      if (cloneFromData['dealerStatus'] && Array.isArray(cloneFromData['dealerStatus'])) {\n        cloneFromData['dealerStatus'] = cloneFromData['dealerStatus'].join('/');\n      }\n      if (cloneFromData['titleReleaseType'] && Array.isArray(cloneFromData['titleReleaseType'])) {\n        cloneFromData['titleReleaseType'] = cloneFromData['titleReleaseType'].join('/');\n      }\n      return cloneFromData;\n    }\n    onKeydown(event) {\n      if (event.key === 'Enter') {\n        this.searchData();\n      }\n    }\n    onInput(event) {\n      const input = event.target;\n      input.value = input.value.replace(/[^0-9]/g, '');\n      if (input.value.length > 10) {\n        input.value = input.value.slice(0, 10);\n      }\n      this.formData['LoanId'] = input.value;\n    }\n    isEmpty(value) {\n      return value === '' || value === null || value === undefined;\n    }\n    getButtonPermission() {\n      this.permissionService.obtainPermission(\"app.titleMaintenance\").subscribe({\n        next: e => {\n          if (e.data) {\n            this.permissionList = e.data;\n          }\n        }\n      });\n    }\n    isBtnVisible(permission) {\n      return this.permissionService.hasPermission(this.permissionList, permission);\n    }\n    static #_ = this.ɵfac = function TitleManagementComponent_Factory(t) {\n      return new (t || TitleManagementComponent)(i0.ɵɵdirectiveInject(i1.StorageService), i0.ɵɵdirectiveInject(i2.DataDictionaryService), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.TitleService), i0.ɵɵdirectiveInject(i5.FileManagementService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.MsgService), i0.ɵɵdirectiveInject(i8.FileUtilService), i0.ɵɵdirectiveInject(i9.PermissionService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TitleManagementComponent,\n      selectors: [[\"app-title-management\"]],\n      viewQuery: function TitleManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(PreviewComponent, 5);\n          i0.ɵɵviewQuery(TitleDetailAndEditComponent, 5);\n          i0.ɵɵviewQuery(CheckInComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.previewComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.titleDetailAndEditComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkIn = _t.first);\n        }\n      },\n      decls: 53,\n      vars: 36,\n      consts: [[\"dt\", \"\"], [\"accountNumber\", \"\"], [\"reference\", \"\"], [\"sourceName\", \"\"], [\"normalDisplay\", \"\"], [1, \"flex\", \"flex-column\"], [1, \"flex\", \"justify-content-between\", \"mb-3\", \"w-12\"], [1, \"font20\", \"font-bold\", \"color3D3D3D\"], [1, \"flex\", \"panel\", \"flex-wrap\", \"text-sm\", 3, \"keydown\"], [1, \"box\", \"flex\", \"flex-wrap\", \"w-12\", \"p-3\", \"border-round\"], [1, \"col-3\"], [1, \"color3D3D3D\", \"py-2\"], [\"maxFileSize\", \"********\", \"placeholder\", \"Select\", \"optionLabel\", \"text\", \"optionValue\", \"value\", \"display\", \"chip\", 1, \"multiSelectStyle\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"showClear\"], [\"placeholder\", \"Loan ID\", \"type\", \"text\", \"pInputText\", \"\", 1, \"inputStyle\", 3, \"input\", \"ngModelChange\", \"ngModel\"], [\"class\", \"col-3\", 4, \"ngIf\"], [\"class\", \"flex flex-column col-3\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"w-12\"], [\"ngSrc\", \"assets/img/sortby_up_icon.png\", \"class\", \"cursor-pointer\", \"alt\", \"\", \"height\", \"24\", \"width\", \"24\", 3, \"click\", 4, \"ngIf\"], [\"ngSrc\", \"assets/img/sortby_down_icon.png\", \"class\", \"cursor-pointer\", \"alt\", \"\", \"height\", \"24\", \"width\", \"24\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-replay\", \"label\", \"Reset\", 1, \"greyButton\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-search\", \"label\", \"Search\", 1, \"greenButton\", 3, \"click\"], [1, \"box\", \"flex\", \"flex-wrap\", \"w-12\", \"p-3\", \"border-round\", \"mt-4\"], [1, \"w-full\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-3\"], [1, \"flex\", \"align-content-center\", \"gap-3\"], [1, \"flex\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Export\", 1, \"greenButton\", 3, \"click\", \"disabled\"], [\"src\", \"./assets/img/Excel.png\", 1, \"mr-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", 1, \"greenButton\", 3, \"click\", \"label\", \"disabled\"], [\"styleClass\", \"p-datatable-striped tableWidth\", 3, \"rowsChange\", \"firstChange\", \"onPage\", \"resetPageOnSort\", \"rows\", \"showCurrentPageReport\", \"first\", \"paginator\", \"totalRecords\", \"value\", \"rowsPerPageOptions\", \"scrollable\", \"lazy\", \"currentPageReportTemplate\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [4, \"ngIf\"], [3, \"refreshDataEvent\"], [\"placeholder\", \"Dealer Code or DBA Name or Legal Name\", \"type\", \"text\", \"pInputText\", \"\", 1, \"inputStyle\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"VIN ( or Last 6 digits VIN)\", \"type\", \"text\", \"pInputText\", \"\", 1, \"inputStyle\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"flex-column\", \"col-3\"], [1, \"flex\", \"align-items-center\", \"flex-1\"], [\"placeholder\", \"From\", \"inputId\", \"navigators\", 1, \"calendarStyle\", 3, \"ngModelChange\", \"showButtonBar\", \"ngModel\", \"selectOtherMonths\", \"monthNavigator\", \"yearNavigator\", \"maxDate\"], [1, \"px-3\"], [\"placeholder\", \"To\", \"showButtonBar\", \"true\", \"inputId\", \"navigators\", 1, \"calendarStyle\", 3, \"ngModelChange\", \"ngModel\", \"selectOtherMonths\", \"monthNavigator\", \"yearNavigator\", \"minDate\"], [\"ngSrc\", \"assets/img/sortby_up_icon.png\", \"alt\", \"\", \"height\", \"24\", \"width\", \"24\", 1, \"cursor-pointer\", 3, \"click\"], [\"ngSrc\", \"assets/img/sortby_down_icon.png\", \"alt\", \"\", \"height\", \"24\", \"width\", \"24\", 1, \"cursor-pointer\", 3, \"click\"], [\"pSortableColumn\", \"SN\", 3, \"click\"], [\"field\", \"SN\"], [\"pSortableColumn\", \"OP\", 3, \"click\"], [\"field\", \"OP\"], [\"pSortableColumn\", \"RD\", 3, \"click\"], [\"field\", \"RD\"], [\"alignFrozen\", \"right\", \"pFrozenColumn\", \"\", 2, \"text-align\", \"center\"], [3, \"ngClass\"], [\"tooltipPosition\", \"top\", 3, \"pTooltip\", \"autoHide\"], [1, \"text-primary\"], [1, \"text-primary\", \"preserve-space\"], [\"class\", \"label status-danger\", 4, \"ngIf\"], [3, \"click\"], [\"type\", \"checkbox\", 3, \"disabled\", \"ngModel\"], [\"alignFrozen\", \"right\", \"pFrozenColumn\", \"\"], [1, \"flex\", \"align-items-center\"], [\"pTooltip\", \"View\", \"tooltipPosition\", \"top\", 1, \"link\"], [\"pButton\", \"\", \"pTooltip\", \"View\", \"tooltipPosition\", \"top\", 1, \"action_button\", 3, \"click\", \"disabled\", \"ngClass\"], [\"src\", \"./assets/img/title-view.png\"], [1, \"mx-2\"], [\"pTooltip\", \"Details\", \"tooltipPosition\", \"top\"], [\"pButton\", \"\", \"pTooltip\", \"Details\", \"tooltipPosition\", \"top\", 1, \"action_button\", 3, \"click\"], [\"src\", \"./assets/img/details.png\"], [\"class\", \"action_button\", \"pButton\", \"\", \"pTooltip\", \"Edit\", \"tooltipPosition\", \"top\", 3, \"click\", 4, \"ngIf\"], [\"tooltipPosition\", \"top\", 3, \"pTooltip\"], [\"class\", \"action_button\", \"pButton\", \"\", \"tooltipPosition\", \"top\", 3, \"disabled\", \"ngClass\", \"pTooltip\", \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"w-12\"], [\"class\", \"flex align-items-center gap-1 text-xs\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"text-xs\"], [1, \"font-bold\"], [1, \"label\", \"status-danger\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngFor\", \"ngForOf\"], [1, \"email-link\", 3, \"href\"], [1, \"phone-link\", 3, \"href\"], [\"pButton\", \"\", \"pTooltip\", \"Edit\", \"tooltipPosition\", \"top\", 1, \"action_button\", 3, \"click\"], [\"src\", \"./assets/img/edit.png\"], [\"pButton\", \"\", \"tooltipPosition\", \"top\", 1, \"action_button\", 3, \"click\", \"disabled\", \"ngClass\", \"pTooltip\"], [3, \"src\"], [\"pTemplate\", \"emptymessage\"], [\"colspan\", \"20\", 1, \"no_data\"], [\"colspan\", \"12\"]],\n      template: function TitleManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7);\n          i0.ɵɵtext(3, \"Title Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 8);\n          i0.ɵɵlistener(\"keydown\", function TitleManagementComponent_Template_div_keydown_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeydown($event));\n          });\n          i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\", 10)(7, \"div\", 11);\n          i0.ɵɵtext(8, \"Title Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p-multiSelect\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_Template_p_multiSelect_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.formData[\"titleStatus\"], $event) || (ctx.formData[\"titleStatus\"] = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11);\n          i0.ɵɵtext(12, \"Loans Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-multiSelect\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.formData[\"loanStatus\"], $event) || (ctx.formData[\"loanStatus\"] = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵtext(16, \"Dealer Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p-multiSelect\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_Template_p_multiSelect_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.formData[\"dealerStatus\"], $event) || (ctx.formData[\"dealerStatus\"] = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11);\n          i0.ɵɵtext(20, \"Loan ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 13);\n          i0.ɵɵlistener(\"input\", function TitleManagementComponent_Template_input_input_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInput($event));\n          });\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TitleManagementComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.formData[\"loanId\"], $event) || (ctx.formData[\"loanId\"] = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, TitleManagementComponent_div_22_Template, 4, 1, \"div\", 14)(23, TitleManagementComponent_div_23_Template, 4, 1, \"div\", 14)(24, TitleManagementComponent_div_24_Template, 8, 11, \"div\", 15)(25, TitleManagementComponent_div_25_Template, 8, 11, \"div\", 15)(26, TitleManagementComponent_div_26_Template, 4, 3, \"div\", 14);\n          i0.ɵɵelementStart(27, \"div\", 16);\n          i0.ɵɵtemplate(28, TitleManagementComponent_img_28_Template, 1, 0, \"img\", 17)(29, TitleManagementComponent_img_29_Template, 1, 0, \"img\", 18);\n          i0.ɵɵelementStart(30, \"div\", 19)(31, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function TitleManagementComponent_Template_button_click_31_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.reset());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TitleManagementComponent_Template_button_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.searchData(0, ctx.pageSize, true));\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 22)(34, \"div\", 23)(35, \"div\", 24)(36, \"span\", 7);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 25)(39, \"div\", 26)(40, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function TitleManagementComponent_Template_button_click_40_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.export());\n          });\n          i0.ɵɵelement(41, \"img\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 26)(43, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function TitleManagementComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showCheckin(ctx.loanIdList));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"p-table\", 30, 0);\n          i0.ɵɵtwoWayListener(\"rowsChange\", function TitleManagementComponent_Template_p_table_rowsChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"firstChange\", function TitleManagementComponent_Template_p_table_firstChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.first, $event) || (ctx.first = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onPage\", function TitleManagementComponent_Template_p_table_onPage_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.searchData($event.first, $event.rows));\n          });\n          i0.ɵɵtemplate(46, TitleManagementComponent_ng_template_46_Template, 42, 0, \"ng-template\", 31)(47, TitleManagementComponent_ng_template_47_Template, 74, 57, \"ng-template\", 32)(48, TitleManagementComponent_48_Template, 1, 0, null, 33);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(49, \"app-loan-pop-soldExt\");\n          i0.ɵɵelementStart(50, \"app-check-in\", 34);\n          i0.ɵɵlistener(\"refreshDataEvent\", function TitleManagementComponent_Template_app_check_in_refreshDataEvent_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleAction($event, ctx.first, ctx.pageSize));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"app-title-detail-and-edit\")(52, \"app-preview-component\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"options\", ctx.titleStatusSelect);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData[\"titleStatus\"]);\n          i0.ɵɵproperty(\"showClear\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"options\", ctx.loanStatusSelect);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData[\"loanStatus\"]);\n          i0.ɵɵproperty(\"showClear\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"options\", ctx.dealerStatusSelect);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData[\"dealerStatus\"]);\n          i0.ɵɵproperty(\"showClear\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.formData[\"loanId\"]);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.collapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.collapsed);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"Total Record: \", ctx.paidTotal, \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.titleList.length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", \"Check In(\" + ctx.loanIdList.length + \")\")(\"disabled\", ctx.loanIdList.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"resetPageOnSort\", true);\n          i0.ɵɵtwoWayProperty(\"rows\", ctx.pageSize);\n          i0.ɵɵproperty(\"showCurrentPageReport\", true);\n          i0.ɵɵtwoWayProperty(\"first\", ctx.first);\n          i0.ɵɵproperty(\"paginator\", ctx.paidTotal > 10)(\"totalRecords\", ctx.paidTotal)(\"value\", ctx.titleList)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(34, _c1))(\"scrollable\", true)(\"lazy\", true)(\"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} entries\")(\"tableStyle\", i0.ɵɵpureFunction0(35, _c2));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.titleList === undefined || ctx.titleList === null || (ctx.titleList == null ? null : ctx.titleList.length) === 0);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i10.ButtonDirective, i11.PrimeTemplate, i12.Table, i12.SortableColumn, i12.FrozenColumn, i12.SortIcon, i13.InputText, i14.Tooltip, i15.Calendar, i16.DefaultValueAccessor, i16.CheckboxControlValueAccessor, i16.NgControlStatus, i16.NgModel, i17.MultiSelect, i18.Ripple, i3.NgOptimizedImage, i19.TitleDetailAndEditComponent, i20.PreviewComponent, i21.LoanPopSoldExtComponent, i22.CheckInComponent, i3.CurrencyPipe, i3.DatePipe, i23.PhonePipe],\n      styles: [\".status-danger[_ngcontent-%COMP%] {\\n  background-color: #ff4800;\\n  color: white;\\n  padding: 2px 5px;\\n  border-radius: 3px;\\n}\\n\\n.dqRed[_ngcontent-%COMP%] {\\n  background-color: #feb8b8 !important;\\n}\\n\\n.preserve-space[_ngcontent-%COMP%] {\\n  white-space: pre;\\n}\\n\\n.phone-link[_ngcontent-%COMP%] {\\n  color: #1e90ff;\\n  text-decoration: none;\\n}\\n\\n.phone-link[_ngcontent-%COMP%]:hover {\\n  color: #1c86ee;\\n  text-decoration: underline;\\n}\\n\\n.email-link[_ngcontent-%COMP%] {\\n  color: #1e90ff;\\n  text-decoration: none;\\n}\\n\\n.email-link[_ngcontent-%COMP%]:hover {\\n  color: #1c86ee;\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGl0bGUvdGl0bGUtbWFpbnRlbmFuY2UvdGl0bGUtbWFuYWdlbWVudC90aXRsZS1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UseUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQUNGOztBQUVBO0VBQ0Usb0NBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7RUFDQSwwQkFBQTtBQUNGOztBQUdBO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBQUY7O0FBR0E7RUFDRSxjQUFBO0VBQ0EsMEJBQUE7QUFBRiIsInNvdXJjZXNDb250ZW50IjpbIi5zdGF0dXMtZGFuZ2VyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmY0ODAwO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBwYWRkaW5nOiAycHggNXB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDNweDtcclxufVxyXG5cclxuLmRxUmVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmViOGI4ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5wcmVzZXJ2ZS1zcGFjZSB7XHJcbiAgd2hpdGUtc3BhY2U6IHByZTtcclxufVxyXG5cclxuLnBob25lLWxpbmsge1xyXG4gIGNvbG9yOiAjMWU5MGZmOyBcclxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7IFxyXG59XHJcblxyXG4ucGhvbmUtbGluazpob3ZlciB7XHJcbiAgY29sb3I6ICMxYzg2ZWU7IFxyXG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOyBcclxufVxyXG5cclxuXHJcbi5lbWFpbC1saW5rIHtcclxuICBjb2xvcjogIzFlOTBmZjsgXHJcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG59XHJcblxyXG4uZW1haWwtbGluazpob3ZlciB7XHJcbiAgY29sb3I6ICMxYzg2ZWU7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return TitleManagementComponent;\n})();", "map": {"version": 3, "names": ["SEVERITY_ERROR", "_", "moment", "CheckInComponent", "PreviewComponent", "TitleDetailAndEditComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "TitleManagementComponent_div_22_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "formData", "ɵɵresetView", "ɵɵadvance", "ɵɵtwoWayProperty", "TitleManagementComponent_div_23_Template_input_ngModelChange_3_listener", "_r4", "TitleManagementComponent_div_24_Template_p_calendar_ngModelChange_4_listener", "_r5", "TitleManagementComponent_div_24_Template_p_calendar_ngModelChange_7_listener", "ɵɵproperty", "TitleManagementComponent_div_25_Template_p_calendar_ngModelChange_4_listener", "_r6", "TitleManagementComponent_div_25_Template_p_calendar_ngModelChange_7_listener", "TitleManagementComponent_div_26_Template_p_multiSelect_ngModelChange_3_listener", "_r7", "titleReleaseTypeSelect", "ɵɵlistener", "TitleManagementComponent_img_28_Template_img_click_0_listener", "_r8", "collapsed", "TitleManagementComponent_img_29_Template_img_click_0_listener", "_r9", "TitleManagementComponent_ng_template_46_Template_th_click_11_listener", "_r10", "sort", "ɵɵelement", "TitleManagementComponent_ng_template_46_Template_th_click_16_listener", "TitleManagementComponent_ng_template_46_Template_th_click_29_listener", "ɵɵtextInterpolate1", "detail_r12", "label", "ɵɵtextInterpolate", "title_r13", "loanTib", "key", "ɵɵtemplate", "TitleManagementComponent_ng_template_47_ng_template_4_div_1_Template", "loanDetails", "dealerTib", "orgStatus", "ɵɵelementContainerStart", "ɵɵpipeBind1", "detail_r14", "TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_container_3_Template", "TitleManagementComponent_ng_template_47_ng_template_12_div_1_ng_template_4_Template", "ɵɵtemplateRefExtractor", "normalDisplay_r15", "TitleManagementComponent_ng_template_47_ng_template_12_div_1_Template", "dealerDetails", "dealer_r16", "phone", "ɵɵsanitizeUrl", "TitleManagementComponent_ng_template_47_ng_template_25_li_1_ng_container_4_Template", "contactTypeText", "ɵɵtextInterpolate2", "firstName", "lastName", "email", "TitleManagementComponent_ng_template_47_ng_template_25_li_1_Template", "contactTibsM", "TitleManagementComponent_ng_template_47_button_69_Template_button_click_0_listener", "_r17", "$implicit", "toDetailOrEdit", "TitleManagementComponent_ng_template_47_button_73_Template_button_click_0_listener", "_r18", "handleAddDelete", "checkAddDeleteByLoanId", "loanId", "ɵɵpropertyInterpolate", "checkStatus", "titleStatus", "ɵɵpureFunction1", "_c3", "isInComplete", "TitleManagementComponent_ng_template_47_ng_template_4_Template", "TitleManagementComponent_ng_template_47_span_11_Template", "TitleManagementComponent_ng_template_47_ng_template_12_Template", "TitleManagementComponent_ng_template_47_span_17_Template", "TitleManagementComponent_ng_template_47_span_21_Template", "TitleManagementComponent_ng_template_47_ng_template_25_Template", "TitleManagementComponent_ng_template_47_Template_td_click_41_listener", "_r11", "trackingNoFromSuplier", "openTrakingNumberFromDba", "TitleManagementComponent_ng_template_47_Template_td_click_50_listener", "trackingNumber", "carrierFullUrl", "TitleManagementComponent_ng_template_47_Template_button_click_60_listener", "viewFile", "fileManagementUrl", "accountNumber", "TitleManagementComponent_ng_template_47_Template_button_click_65_listener", "TitleManagementComponent_ng_template_47_button_69_Template", "TitleManagementComponent_ng_template_47_button_73_Template", "accountNumber_r19", "loanTypeDisp", "reference_r20", "reference", "dba", "legalName", "sourceName", "sourceName_r21", "vin", "cost", "currentCost", "titleStatusText", "titleReleaseTypeText", "loanStatusText", "ɵɵclassMap", "ɵɵpipeBind2", "strReceivedDate", "strReleasedDate", "isFunded", "isScheduled", "isHasTitleFile", "isBtnVisible", "TitleManagementComponent_48_ng_template_0_tr_0_Template", "TitleManagementComponent_48_ng_template_0_tr_1_Template", "isTitleList", "TitleManagementComponent_48_ng_template_0_Template", "TitleManagementComponent", "constructor", "appStorageService", "dataDictionaryService", "datePipe", "titleService", "fileManagementService", "loadingService", "msgService", "fileUtil", "permissionService", "isTooltipVisible", "titleList", "isAdding", "isLoanList", "loanIdList", "loanStatusSelect", "titleStatusSelect", "loanTypeSelect", "dealerStatusSelect", "isFundedSelect", "value", "text", "sortOptions", "first", "pageSize", "paidTotal", "loan", "asset", "dataDictionaryList", "searchParam", "permissionList", "ngOnInit", "getButtonPermission", "getDropdownData", "show", "search", "condition", "dataDictionaryTypeName_In", "getDataDictionaryItems", "subscribe", "next", "response", "hide", "data", "content", "filter", "item", "includes", "error", "searchData", "rows", "resetPage", "Filters", "formatSearchData", "PageIndex", "PageSize", "Sorting", "titleManagementSearch", "window", "open", "openTrakingNumber", "url", "showTooltip", "hideTooltip", "body", "filePath", "fileFrom", "getBase64Str", "e", "navigator", "userAgent", "indexOf", "titleFileName", "downloadImageFromBase64", "previewComponent", "showDialog", "summary", "severity", "detail", "isLeftButton", "leftButtonLabel", "isRightButton", "rightButtonLabel", "notification", "param", "undefined", "sorting", "searchTitleDataList", "length", "for<PERSON>ach", "acceptanceDate", "formatDateString", "maturityDate", "dateString", "date", "Date", "mm", "String", "getMonth", "padStart", "dd", "getDate", "yyyy", "getFullYear", "title", "action", "titleDetailAndEditComponent", "push", "index", "findIndex", "splice", "find", "status", "export", "IsPaging", "exportTitleData", "res", "URL", "createObjectURL", "a", "document", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "href", "download", "generateFileName", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "dialogData", "onClose", "timestamp", "format", "showCheckin", "checkIn", "refreshData", "reset", "transform", "remove", "handleAction", "event", "cloneFromData", "cloneDeep", "isEmpty", "JSON", "parse", "getStoredSessionStorageByKey", "Array", "isArray", "join", "onKeydown", "onInput", "input", "target", "replace", "slice", "obtainPermission", "permission", "hasPermission", "ɵɵdirectiveInject", "i1", "StorageService", "i2", "DataDictionaryService", "i3", "DatePipe", "i4", "TitleService", "i5", "FileManagementService", "i6", "LoadingService", "i7", "MsgService", "i8", "FileUtilService", "i9", "PermissionService", "_2", "selectors", "viewQuery", "TitleManagementComponent_Query", "rf", "ctx", "TitleManagementComponent_Template_div_keydown_4_listener", "_r1", "TitleManagementComponent_Template_p_multiSelect_ngModelChange_9_listener", "TitleManagementComponent_Template_p_multiSelect_ngModelChange_13_listener", "TitleManagementComponent_Template_p_multiSelect_ngModelChange_17_listener", "TitleManagementComponent_Template_input_input_21_listener", "TitleManagementComponent_Template_input_ngModelChange_21_listener", "TitleManagementComponent_div_22_Template", "TitleManagementComponent_div_23_Template", "TitleManagementComponent_div_24_Template", "TitleManagementComponent_div_25_Template", "TitleManagementComponent_div_26_Template", "TitleManagementComponent_img_28_Template", "TitleManagementComponent_img_29_Template", "TitleManagementComponent_Template_button_click_31_listener", "TitleManagementComponent_Template_button_click_32_listener", "TitleManagementComponent_Template_button_click_40_listener", "TitleManagementComponent_Template_button_click_43_listener", "TitleManagementComponent_Template_p_table_rowsChange_44_listener", "TitleManagementComponent_Template_p_table_firstChange_44_listener", "TitleManagementComponent_Template_p_table_onPage_44_listener", "TitleManagementComponent_ng_template_46_Template", "TitleManagementComponent_ng_template_47_Template", "TitleManagementComponent_48_Template", "TitleManagementComponent_Template_app_check_in_refreshDataEvent_50_listener", "ɵɵpureFunction0", "_c1", "_c2"], "sources": ["D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\title\\title-maintenance\\title-management\\title-management.component.ts", "D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\title\\title-maintenance\\title-management\\title-management.component.html"], "sourcesContent": ["import {Component, ViewChild} from '@angular/core';\r\nimport {StorageService} from \"../../../../service/common/storage/storage.service\";\r\nimport {DataDictionaryService} from \"../../../../service/system/data-dictionary/data-dictionary.service\";\r\nimport {DatePipe} from \"@angular/common\";\r\nimport {LoadingService} from \"../../../../service/common/loading/loadingService\";\r\nimport {MsgService} from \"../../../../service/common/msg/msg.service\";\r\nimport {SEVERITY_ERROR, SEVERITY_SUCCESS} from \"../../../../domain/constant\";\r\nimport _ from \"lodash\";\r\nimport {Table} from \"primeng/table\";\r\nimport moment from \"moment\";\r\nimport {CheckInComponent} from \"../pop/check-in/check-in.component\";\r\nimport {TitleService} from \"../../../../service/title/title.service\";\r\nimport {PreviewComponent} from \"../../../../component/preview/preview.component\";\r\nimport {FileManagementService} from \"../../../../service/system/file/file-management.service\";\r\nimport {TitleDetailAndEditComponent} from \"../pop/title-detail-and-edit/title-detail-and-edit.component\";\r\nimport {FileUtilService} from \"../../../../util/file-util/file-util.service\";\r\nimport { PermissionService } from '../../../../service/common/premission/permission.service';\r\n\r\n@Component({\r\n  selector: 'app-title-management',\r\n  templateUrl: './title-management.component.html',\r\n  styleUrl: './title-management.component.scss'\r\n})\r\nexport class TitleManagementComponent {\r\n  @ViewChild('dt') dt!: Table;\r\n  @ViewChild(PreviewComponent) previewComponent!: PreviewComponent;\r\n  isTitleList: boolean = false;\r\n  isTooltipVisible: boolean = false;\r\n  titleList: any[] = [];\r\n  isAdding = true;\r\n  @ViewChild(TitleDetailAndEditComponent) titleDetailAndEditComponent!: TitleDetailAndEditComponent;\r\n  isLoanList: boolean = false;\r\n  loanIdList: any[] = [];\r\n  loanStatusSelect: any[] = [];\r\n  titleStatusSelect: any[] = [];\r\n  loanTypeSelect: any[] = [];\r\n  titleReleaseTypeSelect: any[] = [];\r\n  dealerStatusSelect: any[] = [];\r\n  isFundedSelect: any[] = [{value: true, text: 'Yes'}, {value: false, text: 'No'}];\r\n  formData: any = {};\r\n  sortOptions: any = {};\r\n  fileBase64: any;\r\n  first: number = 1;\r\n  pageSize: number = 10;\r\n  paidTotal: number = 0;\r\n  collapsed: boolean = false;\r\n  loan: any = {};\r\n  asset: any = {};\r\n  dataDictionaryList: any[] = []\r\n  @ViewChild(CheckInComponent) checkIn!: CheckInComponent;\r\n  searchParam: any = {};\r\n  permissionList: any[] = [];\r\n\r\n  loanDetails = [\r\n    {label: 'Loan ID', key: 'accountNumber'},\r\n    {label: 'Loan Type', key: 'loanTypeDisp'},\r\n    {label: 'Vehicle Info', key: 'vehicleInfo'},\r\n    {label: 'Vin', key: 'vin'},\r\n    {label: 'Dealer Name', key: 'dealerName'},\r\n    {label: 'Source Name', key: 'sourceName'},\r\n    {label: 'Floored Date', key: 'acceptanceDate'},\r\n    {label: 'Payoff Date', key: 'maturityDate'},\r\n    {label: 'Loan Status', key: 'loanStatusDisP'}\r\n  ];\r\n\r\n  dealerDetails = [\r\n    {label: 'Dealer Code', key: 'reference'},\r\n    {label: 'Dealer Name', key: 'dba'},\r\n    {label: 'Limit', key: 'creditLineInfo'},\r\n    {label: 'Utilization', key: 'creditLineUtilization'},\r\n    {label: 'Live Loans', key: 'liveLoans'},\r\n    {label: 'On Hold', key: 'isOnHold'},\r\n    {label: 'Boss Dealer', key: 'isBossDealer'},\r\n    {label: 'Dealer Status', key: 'orgStatusText'}\r\n  ];\r\n\r\n  constructor(\r\n    private readonly appStorageService: StorageService,\r\n    private readonly dataDictionaryService: DataDictionaryService,\r\n    private readonly datePipe: DatePipe,\r\n    private readonly titleService: TitleService,\r\n    private readonly fileManagementService: FileManagementService,\r\n    private readonly loadingService: LoadingService,\r\n    private readonly msgService: MsgService,\r\n    private readonly fileUtil: FileUtilService,\r\n    private readonly permissionService: PermissionService,\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loanIdList = []\r\n    this.getButtonPermission();\r\n    this.getDropdownData();\r\n  }\r\n\r\n  // Getting the data for a dropdown value by data dictionary\r\n  getDropdownData(): void {\r\n    this.loadingService.show();\r\n    const search: {} = {\r\n      condition: {\r\n        dataDictionaryTypeName_In: ['LoanStatus', 'TitleStatus', 'LoanType', 'TitleReleaseType', 'OrgStatus']\r\n      },\r\n      sort: []\r\n    }\r\n    this.dataDictionaryService.getDataDictionaryItems(search).subscribe({\r\n      next: (response: any): void => {\r\n        this.loadingService.hide();\r\n        const data: [] = response.data.content;\r\n        if (data) {\r\n          this.dataDictionaryList = data;\r\n          this.loanStatusSelect =\r\n            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'LoanStatus' &&\r\n              ['Cancelled', 'Charged Off', 'Paid', 'Live', 'Recovered'].includes(item['text']));\r\n          this.titleStatusSelect =\r\n            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'TitleStatus');\r\n          this.loanTypeSelect =\r\n            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'LoanType');\r\n          this.titleReleaseTypeSelect =\r\n            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'TitleReleaseType');\r\n          this.dealerStatusSelect =\r\n            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'OrgStatus');\r\n        }\r\n      },\r\n      error: (): void => {\r\n        this.loadingService.hide();\r\n      }\r\n    });\r\n  }\r\n\r\n  // Search data with optional page and page size parameters\r\n  searchData(first = 0, rows = this.pageSize, resetPage: boolean = false): void {\r\n    if (resetPage) {\r\n      this.first = 0;\r\n    } else {\r\n      this.first = first;\r\n    }\r\n    this.pageSize = rows;\r\n    let search: any = {\r\n      Filters: this.formatSearchData(),\r\n      PageIndex: first,\r\n      PageSize: rows,\r\n      Sorting: this.sortOptions['property'] ? { [this.sortOptions['property']]: this.sortOptions['direction'] } : {}\r\n    }\r\n    this.searchParam = search;\r\n    this.titleManagementSearch(search);\r\n    this.isTitleList = true;\r\n  }\r\n\r\n  openTrakingNumberFromDba(trackingNoFromSuplier: any) {\r\n    window.open('https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber=' + trackingNoFromSuplier);\r\n  }\r\n\r\n  openTrakingNumber(url: any) {\r\n    window.open(url);\r\n  }\r\n\r\n  showTooltip() {\r\n    this.isTooltipVisible = true;\r\n  }\r\n\r\n  hideTooltip() {\r\n    this.isTooltipVisible = false;\r\n  }\r\n\r\n  viewFile(fileManagementUrl: string,accountNumber: any): void {\r\n    let body: any = {\r\n      filePath: fileManagementUrl,\r\n      fileFrom: 'FTP'\r\n    }\r\n    this.fileManagementService.getBase64Str(body).subscribe({\r\n      next: (e: any): void => {\r\n        if (e.data) {\r\n          // download file if using IE\r\n          if (window.navigator.userAgent.indexOf(\"Trident\") > -1) {\r\n            const titleFileName = 'Title' + accountNumber + '.pdf';\r\n            this.fileUtil.downloadImageFromBase64(e.data, titleFileName);\r\n          } else {\r\n            this.previewComponent.showDialog(e.data, 'application/pdf');\r\n          }\r\n        } else {\r\n          let data = {\r\n            summary: 'Alert',\r\n            severity: \"warning\",\r\n            detail: \"No title file available.\",\r\n            isLeftButton: false,\r\n            leftButtonLabel: \"\",\r\n            isRightButton: true,\r\n            rightButtonLabel: 'OK'\r\n          }\r\n          this.msgService.notification(data);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  sort(param: any): void {\r\n\r\n    if (!this.isTitleList) {\r\n      return;\r\n    }\r\n\r\n    if (this.sortOptions['property'] === param && this.sortOptions['property'] != undefined) {\r\n      if (this.sortOptions['direction'] === 'asc') {\r\n        this.sortOptions['direction'] = 'desc';\r\n      } else {\r\n        this.sortOptions['direction'] = 'asc'\r\n      }\r\n    } else {\r\n      this.sortOptions['property'] = param;\r\n      this.sortOptions['direction'] = 'asc'\r\n    }\r\n\r\n    let sorting: any = {}\r\n    sorting[this.sortOptions['property']] = this.sortOptions['direction']\r\n    const search = {\r\n      Filters: this.formatSearchData(),\r\n      PageIndex: 0,\r\n      PageSize: 10,\r\n      Sorting: sorting\r\n    }\r\n\r\n    this.searchParam = search;\r\n    this.titleManagementSearch(search);\r\n  }\r\n\r\n  titleManagementSearch(search: any): void {\r\n    this.loadingService.show();\r\n    this.titleService.searchTitleDataList(search).subscribe({\r\n      next: (e: any): void => {\r\n        this.loadingService.hide();\r\n        if (e.data) {\r\n          this.titleList = e.data['results'];\r\n          this.paidTotal = e.data['total'];\r\n          if (this.titleList && this.titleList.length > 0) {\r\n            this.titleList.forEach(item => {\r\n              if (item.loanTib && item.loanTib.acceptanceDate) {\r\n                item.loanTib.acceptanceDate = this.formatDateString(item.loanTib.acceptanceDate);\r\n                item.loanTib.maturityDate = this.formatDateString(item.loanTib.maturityDate);\r\n              }\r\n            });\r\n          } else {\r\n            this.paidTotal = 0;\r\n            this.pageSize = 10;\r\n          }\r\n        }\r\n      },\r\n      error: (): void => {\r\n        this.loadingService.hide();\r\n      }\r\n    })\r\n  }\r\n\r\n  formatDateString(dateString: any) {\r\n    if (!dateString) return '';\r\n    const date = new Date(dateString);\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    const yyyy = date.getFullYear();\r\n    return `${mm}/${dd}/${yyyy}`;\r\n  }\r\n\r\n  toDetailOrEdit(title: any, action: string): void {\r\n    this.titleDetailAndEditComponent.showDialog(this, title, action)\r\n  }\r\n\r\n  handleAddDelete(title: any, action: any) {\r\n    if (action === 'add') {\r\n      this.loanIdList.push(title)\r\n    } else if (action === 'delete') {\r\n      const index = this.loanIdList.findIndex(item => item.loanId === title.loanId);\r\n      if (index !== -1) {\r\n        this.loanIdList.splice(index, 1);\r\n      }\r\n      this.isAdding = true;\r\n    }\r\n  }\r\n\r\n  checkAddDeleteByLoanId(loanId: any) {\r\n    return !this.loanIdList.find(item => item.loanId === loanId);\r\n  }\r\n\r\n\r\n  checkStatus(status: any) {\r\n    if (status == 'HR' || status == 'PRLT') {\r\n      return false;\r\n    } else {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  export() {\r\n    this.loadingService.show();\r\n    let search: any = {\r\n      Filters: this.formatSearchData(),\r\n      PageIndex: 0,\r\n      PageSize: 2100000000,\r\n      Sorting: {},\r\n      IsPaging: true\r\n    }\r\n    this.titleService.exportTitleData(search).subscribe({\r\n      next: (res: Blob): void => {\r\n        this.loadingService.hide();\r\n        const url = window.URL.createObjectURL(res);\r\n        const a = document.createElement('a');\r\n        a.setAttribute('style', 'display:none');\r\n        document.body.appendChild(a);\r\n        a.href = url;\r\n        a.download = this.generateFileName();\r\n        a.click();\r\n        window.URL.revokeObjectURL(url);\r\n        document.body.removeChild(a);\r\n      },\r\n      error: (): void => {\r\n        this.loadingService.hide();\r\n        let dialogData = {\r\n          summary: 'Export data error',\r\n          severity: SEVERITY_ERROR,\r\n          detail: 'Failed, please check and try again!',\r\n          isLeftButton: false,\r\n          leftButtonLabel: \"\",\r\n          isRightButton: true,\r\n          rightButtonLabel: 'OK'\r\n        };\r\n        this.msgService.notification(dialogData).onClose.subscribe();\r\n      }\r\n    });\r\n  }\r\n\r\n  generateFileName(): string {\r\n    const timestamp = moment(new Date()).format('MMDDyyyyHHmmss');\r\n    return `Title_${timestamp}.xls`;\r\n  }\r\n\r\n\r\n  showCheckin(title: any) {\r\n    this.checkIn.showDialog(this, title);\r\n  }\r\n\r\n  refreshData() {\r\n    this.searchData();\r\n  }\r\n\r\n  reset(): void {\r\n    this.formData = {};\r\n  }\r\n\r\n  moment(date: any, format: string): string | null {\r\n    return date ? this.datePipe.transform(date, format) : null;\r\n  }\r\n\r\n  remove(titleList: any) {\r\n    this.loanIdList = titleList\r\n  }\r\n\r\n  handleAction(event:any,first:any,pageSize:any) {\r\n    switch(event.action) {\r\n      case 'remove':\r\n        this.remove(event.title);\r\n        break;\r\n      case 'refreshData':\r\n        this.loanIdList = []\r\n        this.searchData(first,pageSize);\r\n        break;\r\n    }\r\n  }\r\n\r\n\r\n  formatSearchData(): any {\r\n    const cloneFromData: any = _.cloneDeep(this.formData);\r\n    if (!this.isEmpty(cloneFromData['releasedDateFrom'])) {\r\n      cloneFromData['releasedDateFrom'] = this.moment(cloneFromData['releasedDateFrom'], 'MM/dd/yyyy');\r\n    }\r\n    if (!this.isEmpty(cloneFromData['releasedDateTo'])) {\r\n      cloneFromData['releasedDateTo'] = this.moment(cloneFromData['releasedDateTo'], 'MM/dd/yyyy');\r\n    }\r\n    if (!this.isEmpty(cloneFromData['receivedDateFrom'])) {\r\n      cloneFromData['receivedDateFrom'] = this.moment(cloneFromData['receivedDateFrom'], 'MM/dd/yyyy');\r\n    }\r\n    if (!this.isEmpty(cloneFromData['receivedDateTo'])) {\r\n      cloneFromData['receivedDateTo'] = this.moment(cloneFromData['receivedDateTo'], 'MM/dd/yyyy');\r\n    }\r\n    cloneFromData['userId'] = JSON.parse(this.appStorageService.getStoredSessionStorageByKey('userId'));\r\n    cloneFromData['LoanId'] = cloneFromData['LoanId'] == '' ? null : cloneFromData['LoanId'];\r\n    if (cloneFromData['loanStatus'] && Array.isArray(cloneFromData['loanStatus'])) {\r\n      cloneFromData['loanStatus'] = cloneFromData['loanStatus'].join('/');\r\n    }\r\n    if (cloneFromData['titleStatus'] && Array.isArray(cloneFromData['titleStatus'])) {\r\n      cloneFromData['titleStatus'] = cloneFromData['titleStatus'].join('/');\r\n    }\r\n    if (cloneFromData['dealerStatus'] && Array.isArray(cloneFromData['dealerStatus'])) {\r\n      cloneFromData['dealerStatus'] = cloneFromData['dealerStatus'].join('/');\r\n    }\r\n    if (cloneFromData['titleReleaseType'] && Array.isArray(cloneFromData['titleReleaseType'])) {\r\n      cloneFromData['titleReleaseType'] = cloneFromData['titleReleaseType'].join('/');\r\n    }\r\n\r\n    return cloneFromData;\r\n  }\r\n\r\n  onKeydown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter') {\r\n      this.searchData();\r\n    }\r\n  }\r\n\r\n  onInput(event: any): void {\r\n    const input = event.target as HTMLInputElement;\r\n\r\n    input.value = input.value.replace(/[^0-9]/g, '');\r\n\r\n    if (input.value.length > 10) {\r\n      input.value = input.value.slice(0, 10);\r\n    }\r\n\r\n    this.formData['LoanId'] = input.value;\r\n  }\r\n\r\n  isEmpty(value: any): boolean {\r\n    return value === '' || value === null || value === undefined;\r\n  }\r\n\r\n  getButtonPermission(): void {\r\n    this.permissionService.obtainPermission(\"app.titleMaintenance\").subscribe({\r\n      next: (e: any): void => {\r\n        if (e.data) {\r\n          this.permissionList = e.data;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  isBtnVisible(permission: string): boolean {\r\n    return this.permissionService.hasPermission(this.permissionList, permission);\r\n  }\r\n\r\n}\r\n", "<div class=\"flex flex-column\">\r\n  <div class=\"flex justify-content-between mb-3 w-12\">\r\n    <div class=\"font20 font-bold color3D3D3D\">Title Management</div>\r\n  </div>\r\n  <div class=\"flex panel flex-wrap text-sm \" (keydown)=\"onKeydown($event)\">\r\n    <div class=\"box flex flex-wrap w-12 p-3 border-round\">\r\n      <div class=\"col-3\">\r\n        <div class=\"color3D3D3D py-2\">Title Status</div>\r\n        <p-multiSelect\r\n          maxFileSize=\"********\"\r\n          [options]=\"titleStatusSelect\"\r\n          [(ngModel)]=\"formData['titleStatus']\"\r\n          placeholder=\"Select\"\r\n          optionLabel=\"text\"\r\n          optionValue=\"value\"\r\n          display=\"chip\"\r\n          class=\"multiSelectStyle\"\r\n          [showClear]=\"true\"/>\r\n      </div>\r\n      <div class=\"col-3\">\r\n        <div class=\"color3D3D3D py-2\">Loans Status</div>\r\n        <p-multiSelect\r\n          maxFileSize=\"********\"\r\n          [options]=\"loanStatusSelect\"\r\n          [(ngModel)]=\"formData['loanStatus']\"\r\n          placeholder=\"Select\"\r\n          optionLabel=\"text\"\r\n          optionValue=\"value\"\r\n          display=\"chip\"\r\n          class=\"multiSelectStyle\"\r\n          [showClear]=\"true\"/>\r\n      </div>\r\n      <div class=\"col-3\">\r\n        <div class=\"color3D3D3D py-2\">Dealer Status</div>\r\n        <p-multiSelect\r\n          maxFileSize=\"********\"\r\n          [options]=\"dealerStatusSelect\"\r\n          [(ngModel)]=\"formData['dealerStatus']\"\r\n          placeholder=\"Select\"\r\n          optionLabel=\"text\"\r\n          optionValue=\"value\"\r\n          display=\"chip\"\r\n          class=\"multiSelectStyle\"\r\n          [showClear]=\"true\"/>\r\n      </div>\r\n      <div class=\"col-3\">\r\n        <div class=\"color3D3D3D py-2\">Loan ID</div>\r\n        <input class=\"inputStyle\" placeholder=\"Loan ID\" type=\"text\" pInputText (input)=\"onInput($event)\" [(ngModel)]=\"formData['loanId']\"/>\r\n      </div>\r\n      <div class=\"col-3\" *ngIf=\"!collapsed\">\r\n        <div class=\"color3D3D3D py-2\">Dealer</div>\r\n        <input class=\"inputStyle\" placeholder=\"Dealer Code or DBA Name or Legal Name\" type=\"text\" pInputText\r\n               [(ngModel)]=\"formData['dealerName']\"/>\r\n      </div>\r\n      <div class=\"col-3\" *ngIf=\"!collapsed\">\r\n        <div class=\"color3D3D3D py-2\">VIN ( or Last 6 digits VIN)</div>\r\n        <input class=\"inputStyle\" placeholder=\"VIN ( or Last 6 digits VIN)\" type=\"text\" pInputText\r\n               [(ngModel)]=\"formData['vin']\"/>\r\n      </div>\r\n      <div class=\"flex flex-column col-3\" *ngIf=\"!collapsed\">\r\n        <div class=\"color3D3D3D py-2\">Sent Date</div>\r\n        <div class=\"flex align-items-center flex-1\">\r\n          <p-calendar\r\n            [showButtonBar]=\"true\"\r\n            [(ngModel)]=\"formData['releasedDateFrom']\"\r\n            [selectOtherMonths]=\"true\"\r\n            placeholder=\"From\"\r\n            [monthNavigator]=\"true\"\r\n            [yearNavigator]=\"true\"\r\n            inputId=\"navigators\"\r\n            class=\"calendarStyle\"\r\n            [maxDate]=\"formData['releasedDateTo']\"\r\n          ></p-calendar>\r\n          <div class=\"px-3\"> - </div>\r\n          <p-calendar\r\n            [(ngModel)]=\"formData['releasedDateTo']\"\r\n            [selectOtherMonths]=\"true\"\r\n            placeholder=\"To\"\r\n            showButtonBar=\"true\"\r\n            [monthNavigator]=\"true\"\r\n            [yearNavigator]=\"true\"\r\n            inputId=\"navigators\"\r\n            class=\"calendarStyle\"\r\n            [minDate]=\"formData['releasedDateFrom']\"\r\n          ></p-calendar>\r\n        </div>\r\n      </div>\r\n      <div class=\"flex flex-column col-3\" *ngIf=\"!collapsed\">\r\n        <div class=\"color3D3D3D py-2\">Received Date</div>\r\n        <div class=\"flex align-items-center flex-1\">\r\n          <p-calendar\r\n            [showButtonBar]=\"true\"\r\n            [(ngModel)]=\"formData['receivedDateFrom']\"\r\n            [selectOtherMonths]=\"true\"\r\n            placeholder=\"From\"\r\n            [monthNavigator]=\"true\"\r\n            [yearNavigator]=\"true\"\r\n            inputId=\"navigators\"\r\n            class=\"calendarStyle\"\r\n            [maxDate]=\"formData['receivedDateTo']\"\r\n          ></p-calendar>\r\n          <div class=\"px-3\"> - </div>\r\n          <p-calendar\r\n            [(ngModel)]=\"formData['receivedDateTo']\"\r\n            [selectOtherMonths]=\"true\"\r\n            placeholder=\"To\"\r\n            showButtonBar=\"true\"\r\n            [monthNavigator]=\"true\"\r\n            [yearNavigator]=\"true\"\r\n            inputId=\"navigators\"\r\n            class=\"calendarStyle\"\r\n            [minDate]=\"formData['receivedDateFrom']\"\r\n          ></p-calendar>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-3\" *ngIf=\"!collapsed\">\r\n        <div class=\"color3D3D3D py-2\">Title Release Type</div>\r\n        <p-multiSelect\r\n          maxFileSize=\"********\"\r\n          [options]=\"titleReleaseTypeSelect\"\r\n          [(ngModel)]=\"formData['titleReleaseType']\"\r\n          placeholder=\"Select\"\r\n          optionLabel=\"text\"\r\n          optionValue=\"value\"\r\n          display=\"chip\"\r\n          class=\"multiSelectStyle\"\r\n          [showClear]=\"true\"/>\r\n      </div>\r\n      <div class=\"flex align-items-center justify-content-between w-12\">\r\n        <img ngSrc=\"assets/img/sortby_up_icon.png\" class=\"cursor-pointer\" alt=\"\" height=\"24\" width=\"24\"\r\n             (click)=\"collapsed = !collapsed\" *ngIf=\"!collapsed\">\r\n        <img ngSrc=\"assets/img/sortby_down_icon.png\" class=\"cursor-pointer\" alt=\"\" height=\"24\" width=\"24\"\r\n             (click)=\"collapsed = !collapsed\" *ngIf=\"collapsed\">\r\n        <div class=\"flex align-items-center gap-3\">\r\n          <button pButton pRipple type=\"button\" icon=\"pi pi-replay\" class=\"greyButton\" label=\"Reset\"\r\n                  (click)=\"reset()\"></button>\r\n          <button pButton pRipple type=\"button\" icon=\"pi pi-search\" class=\"greenButton\" label=\"Search\"\r\n                  (click)=\"searchData(0, pageSize, true)\"><!-- Call searchData with resetPage=true to reset to first page --></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"box flex flex-wrap w-12 p-3 border-round mt-4\">\r\n    <div class=\"w-full\">\r\n      <div class=\"flex align-items-center justify-content-between mb-3\">\r\n        <span class=\"font20 font-bold color3D3D3D\">Total Record: {{ paidTotal }}</span>\r\n        <div class=\"flex align-content-center gap-3\">\r\n          <div class=\"flex\">\r\n            <button pButton pRipple type=\"button\" class=\"greenButton\" label=\"Export\"\r\n                    [disabled]=\"titleList.length === 0\" (click)=\"export()\"\r\n            ><img src=\"./assets/img/Excel.png\" class=\"mr-2\"></button>\r\n          </div>\r\n          <div class=\"flex\">\r\n            <button pButton pRipple type=\"button\" class=\"greenButton\"  [label]=\"'Check In(' + loanIdList.length + ')'\"\r\n                    [disabled]=\"loanIdList.length === 0\"\r\n                    (click)=\"showCheckin(loanIdList)\"\r\n            ></button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <p-table\r\n        #dt\r\n        [resetPageOnSort]=\"true\"\r\n        [(rows)]=\"pageSize\"\r\n        [showCurrentPageReport]=\"true\"\r\n        [(first)]=\"first\"\r\n        (onPage)=\"searchData($event.first, $event.rows)\"\r\n        [paginator]=\"paidTotal > 10\"\r\n        [totalRecords]=\"paidTotal\"\r\n        [value]=\"titleList\"\r\n        [rowsPerPageOptions]=\"[10,25,50,100]\"\r\n        [scrollable]=\"true\"\r\n        [lazy]=\"true\"\r\n        [currentPageReportTemplate]=\"'Showing {first} to {last} of {totalRecords} entries'\"\r\n        styleClass=\"p-datatable-striped tableWidth\"\r\n        [tableStyle]=\"{'min-width': '60rem'}\">\r\n        <ng-template pTemplate=\"header\">\r\n          <tr>\r\n            <th >\r\n              Loan ID\r\n            </th>\r\n            <th >Loan Type</th>\r\n            <th >Dealer Code</th>\r\n            <th >DBA Name</th>\r\n            <th >Legal Name</th>\r\n            <th pSortableColumn=\"SN\" (click)=\"sort('SN')\">Source Name\r\n              <p-sortIcon field=\"SN\"></p-sortIcon>\r\n            </th>\r\n            <th >VIN</th>\r\n            <th  pSortableColumn=\"OP\" (click)=\"sort('OP')\">Original Principal\r\n              <p-sortIcon field=\"OP\"></p-sortIcon>\r\n            </th>\r\n            <th >Remaining Principal</th>\r\n            <th >Title Status</th>\r\n            <th >Title Release Type</th>\r\n            <th >Loan Status</th>\r\n            <th >Tracking No. from Source</th>\r\n            <th  pSortableColumn=\"RD\" (click)=\"sort('RD')\">\r\n              Received Date\r\n              <p-sortIcon field=\"RD\"></p-sortIcon>\r\n            </th>\r\n            <th >Sent Date</th>\r\n            <th >Tracking No. to Dealer</th>\r\n            <th >IsFunded</th>\r\n            <th >IsScheduled</th>\r\n            <th alignFrozen=\"right\" pFrozenColumn style=\"text-align: center\">Action</th>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"body\" let-title  >\r\n          <tr [ngClass]=\"{'dqRed': title.isInComplete === true}\">\r\n            <td [pTooltip]=\"accountNumber\" tooltipPosition=\"top\" [autoHide]=\"false\">\r\n              <span class=\"text-primary\">{{ title.accountNumber }}</span>\r\n            </td>\r\n            <ng-template #accountNumber>\r\n              <div class=\"flex flex-column gap-2 w-12\">\r\n                <div *ngFor=\"let detail of loanDetails\" class=\"flex align-items-center gap-1 text-xs\">\r\n                  <span>{{ detail.label }} :</span>\r\n                  <span class=\"font-bold\">{{ title.loanTib[detail.key] }}</span>\r\n                </div>\r\n              </div>\r\n            </ng-template>\r\n            <td>{{ title.loanTib.loanTypeDisp }}</td>\r\n            <td  [pTooltip]=\"reference\" tooltipPosition=\"top\" [autoHide]=\"false\">\r\n              <span class=\"text-primary preserve-space\">{{ title.reference }}</span>\r\n              <span class=\"label status-danger\" *ngIf=\"title.dealerTib.orgStatus != 'A'\">{{ title.dealerTib.orgStatus }}</span>\r\n              <ng-template #reference>\r\n                <div class=\"flex flex-column gap-2 w-12\">\r\n                  <div *ngFor=\"let detail of dealerDetails\" class=\"flex align-items-center gap-1 text-xs\">\r\n                    <span>{{ detail.label }} :</span>\r\n                    <ng-container *ngIf=\"detail.key === 'creditLineUtilization' || detail.key === 'creditLineInfo'; else normalDisplay\">\r\n                      <span class=\"font-bold\">{{ title.dealerTib[detail.key] | currency }}</span>\r\n                    </ng-container>\r\n                    <ng-template #normalDisplay>\r\n                      <span class=\"font-bold\">{{ title.dealerTib[detail.key] }}</span>\r\n                    </ng-template>\r\n                  </div>\r\n                </div>\r\n              </ng-template>\r\n            </td>\r\n            <td [pTooltip]=\"reference\" tooltipPosition=\"top\" [autoHide]=\"false\">\r\n              <span class=\"text-primary preserve-space\">{{ title.dba }}</span>\r\n              <span class=\"label status-danger\" *ngIf=\"title.dealerTib.orgStatus != 'A'\">{{ title.dealerTib.orgStatus }}</span>\r\n            </td>\r\n            <td [pTooltip]=\"reference\" tooltipPosition=\"top\" [autoHide]=\"false\">\r\n              <span class=\"text-primary preserve-space\">{{ title.legalName }}\r\n                <span class=\"label status-danger\" *ngIf=\"title.dealerTib.orgStatus != 'A'\">{{ title.dealerTib.orgStatus }}</span>\r\n              </span>\r\n            </td>\r\n            <td [pTooltip]=\"title.sourceName ? sourceName : ''\" tooltipPosition=\"top\" [autoHide]=\"false\">\r\n              <span class=\"text-primary preserve-space\">{{ title.sourceName }}</span>\r\n              <ng-template #sourceName >\r\n                <div class=\"flex flex-column gap-2 w-12\">\r\n                <li *ngFor=\"let dealer of title.contactTibsM\">\r\n                  {{dealer.contactTypeText}} :\r\n                  <span>\r\n                  {{dealer.firstName}} {{dealer.lastName}},\r\n                  <ng-container *ngIf=\"dealer.phone\">\r\n                    <span>tel:\r\n                      <a class=\"phone-link\" [href]=\"'tel:' + (dealer.phone | phone)\">{{dealer.phone | phone}}</a>,\r\n                    </span>\r\n                  </ng-container>\r\n                  <a class=\"email-link\" [href]=\"'mailto:' + dealer.email\">{{dealer.email}}</a>\r\n                </span>\r\n                </li>\r\n                </div>\r\n              </ng-template>\r\n            </td>\r\n            <td>{{ title.vin }}</td>\r\n            <td>{{ title.cost | currency }}</td>\r\n            <td>{{ title.currentCost | currency }}</td>\r\n            <td>{{ title.titleStatusText }}</td>\r\n            <td>{{ title.titleReleaseTypeText }}</td>\r\n            <td>{{ title.loanStatusText }}</td>\r\n\r\n            <td [class]=\"title.trackingNoFromSuplier ? 'cursor-pointer' : ''\"\r\n                (click)=\"title.trackingNoFromSuplier && openTrakingNumberFromDba(title.trackingNoFromSuplier)\">\r\n              <span class=\"text-primary\">{{ title.trackingNoFromSuplier }}</span>\r\n            </td>\r\n            <td>{{ title.strReceivedDate | date:'MM/dd/yyyy' }}</td>\r\n            <td>{{ title.strReleasedDate | date:'MM/dd/yyyy' }}</td>\r\n            <td [class]=\"title.trackingNumber ? 'cursor-pointer' : ''\"\r\n                (click)=\"title.trackingNumber && openTrakingNumberFromDba(title.carrierFullUrl)\">\r\n              <span class=\"text-primary\">{{ title.trackingNumber }}</span>\r\n            </td>\r\n            <td>\r\n              <input type=\"checkbox\" [disabled]=\"true\" [ngModel]=\"title.isFunded\">\r\n            </td>\r\n            <td>\r\n              <input type=\"checkbox\" [disabled]=\"true\" [ngModel]=\"title.isScheduled\">\r\n            </td>\r\n            <td alignFrozen=\"right\" pFrozenColumn>\r\n              <div class=\"flex align-items-center\">\r\n                <span pTooltip=\"View\" tooltipPosition=\"top\" class=\"link\">\r\n                <button class=\"action_button\" pButton [disabled]=\"title.isHasTitleFile == false\" [ngClass]=\"{'dqRed': title.isInComplete === true}\"\r\n                        pTooltip=\"View\" tooltipPosition=\"top\" (click)=\"viewFile(title.fileManagementUrl,title.accountNumber)\">\r\n                  <img src=\"./assets/img/title-view.png\">\r\n                </button>\r\n                </span>\r\n                <span class=\"mx-2\">|</span>\r\n                <span pTooltip=\"Details\" tooltipPosition=\"top\">\r\n                <button class=\"action_button\" pButton\r\n                        pTooltip=\"Details\" tooltipPosition=\"top\" (click)=\"toDetailOrEdit(title, 'details')\">\r\n                  <img src=\"./assets/img/details.png\">\r\n                </button>\r\n                </span>\r\n                <span class=\"mx-2\">|</span>\r\n                <button class=\"action_button\" pButton\r\n                        pTooltip=\"Edit\" tooltipPosition=\"top\"  (click)=\"toDetailOrEdit(title, 'edit')\" *ngIf=\"isBtnVisible('Title_Edit')\">\r\n                  <img src=\"./assets/img/edit.png\">\r\n                </button>\r\n                <span class=\"mx-2\">|</span>\r\n                <span pTooltip=\"{{ checkAddDeleteByLoanId(title.loanId) ? 'Add' : 'Remove' }}\" tooltipPosition=\"top\">\r\n                <button class=\"action_button\" pButton [disabled]=\"checkStatus(title.titleStatus)\" [ngClass]=\"{'dqRed': title.isInComplete === true}\"\r\n                        pTooltip=\"{{ checkAddDeleteByLoanId(title.loanId) ? 'Add' : 'Remove' }}\" tooltipPosition=\"top\"\r\n                        *ngIf=\"isBtnVisible('Title_Add')\"\r\n                        (click)=\"handleAddDelete(title, checkAddDeleteByLoanId(title.loanId) ? 'add' : 'delete')\">\r\n                  <img [src]=\"checkAddDeleteByLoanId(title.loanId) ? './assets/img/add.png' : './assets/img/loan-transaction-cancel.png'\">\r\n                </button>\r\n                </span>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"emptymessage\"  *ngIf=\"titleList === undefined || titleList === null || titleList?.length === 0\">\r\n          <tr *ngIf=\"isTitleList\">\r\n            <td colspan=\"20\" class=\"no_data\">No Data Available</td>\r\n          </tr>\r\n          <tr *ngIf=\"!isTitleList\">\r\n            <td colspan=\"12\"></td>\r\n          </tr>\r\n        </ng-template>\r\n      </p-table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<app-loan-pop-soldExt></app-loan-pop-soldExt>\r\n<app-check-in (refreshDataEvent)=\"handleAction($event,first,pageSize)\"></app-check-in>\r\n<app-title-detail-and-edit></app-title-detail-and-edit>\r\n<app-preview-component></app-preview-component>\r\n"], "mappings": "AAMA,SAAQA,cAAc,QAAyB,6BAA6B;AAC5E,OAAOC,CAAC,MAAM,QAAQ;AAEtB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAAQC,gBAAgB,QAAO,oCAAoC;AAEnE,SAAQC,gBAAgB,QAAO,iDAAiD;AAEhF,SAAQC,2BAA2B,QAAO,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICoChGC,EADF,CAAAC,cAAA,cAAsC,cACN;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,gBAC6C;IAAtCD,EAAA,CAAAI,gBAAA,2BAAAC,wEAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,YAAY,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAZ,YAAY,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAC7CN,EAFE,CAAAG,YAAA,EAC6C,EACzC;;;;IADGH,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,eAAoC;;;;;;IAG3CZ,EADF,CAAAC,cAAA,cAAsC,cACN;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,gBACsC;IAA/BD,EAAA,CAAAI,gBAAA,2BAAAY,wEAAAV,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,KAAK,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAL,KAAK,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IACtCN,EAFE,CAAAG,YAAA,EACsC,EAClC;;;;IADGH,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,QAA6B;;;;;;IAGpCZ,EADF,CAAAC,cAAA,cAAuD,cACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE3CH,EADF,CAAAC,cAAA,cAA4C,qBAWzC;IARCD,EAAA,CAAAI,gBAAA,2BAAAc,6EAAAZ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,kBAAkB,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAlB,kBAAkB,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAQ3CN,EAAA,CAAAG,YAAA,EAAa;IACdH,EAAA,CAAAC,cAAA,cAAkB;IAACD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3BH,EAAA,CAAAC,cAAA,qBAUC;IATCD,EAAA,CAAAI,gBAAA,2BAAAgB,6EAAAd,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,gBAAgB,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAhB,gBAAgB,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAW9CN,EAFK,CAAAG,YAAA,EAAa,EACV,EACF;;;;IAvBAH,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAqB,UAAA,uBAAsB;IACtBrB,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,qBAA0C;IAO1CZ,EANA,CAAAqB,UAAA,2BAA0B,wBAEH,uBACD,YAAAZ,MAAA,CAAAG,QAAA,mBAGgB;IAItCZ,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,mBAAwC;IAQxCZ,EAPA,CAAAqB,UAAA,2BAA0B,wBAGH,uBACD,YAAAZ,MAAA,CAAAG,QAAA,qBAGkB;;;;;;IAK5CZ,EADF,CAAAC,cAAA,cAAuD,cACvB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE/CH,EADF,CAAAC,cAAA,cAA4C,qBAWzC;IARCD,EAAA,CAAAI,gBAAA,2BAAAkB,6EAAAhB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,kBAAkB,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAlB,kBAAkB,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAQ3CN,EAAA,CAAAG,YAAA,EAAa;IACdH,EAAA,CAAAC,cAAA,cAAkB;IAACD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3BH,EAAA,CAAAC,cAAA,qBAUC;IATCD,EAAA,CAAAI,gBAAA,2BAAAoB,6EAAAlB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,gBAAgB,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAhB,gBAAgB,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAW9CN,EAFK,CAAAG,YAAA,EAAa,EACV,EACF;;;;IAvBAH,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAqB,UAAA,uBAAsB;IACtBrB,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,qBAA0C;IAO1CZ,EANA,CAAAqB,UAAA,2BAA0B,wBAEH,uBACD,YAAAZ,MAAA,CAAAG,QAAA,mBAGgB;IAItCZ,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,mBAAwC;IAQxCZ,EAPA,CAAAqB,UAAA,2BAA0B,wBAGH,uBACD,YAAAZ,MAAA,CAAAG,QAAA,qBAGkB;;;;;;IAK5CZ,EADF,CAAAC,cAAA,cAAsC,cACN;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,wBASsB;IANpBD,EAAA,CAAAI,gBAAA,2BAAAqB,gFAAAnB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,QAAA,CAAsB,kBAAkB,GAAAN,MAAA,MAAAG,MAAA,CAAAG,QAAA,CAAlB,kBAAkB,IAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAE;IAO9CN,EAVE,CAAAG,YAAA,EASsB,EAClB;;;;IARFH,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAkB,sBAAA,CAAkC;IAClC3B,EAAA,CAAAe,gBAAA,YAAAN,MAAA,CAAAG,QAAA,qBAA0C;IAM1CZ,EAAA,CAAAqB,UAAA,mBAAkB;;;;;;IAGpBrB,EAAA,CAAAC,cAAA,cACyD;IAApDD,EAAA,CAAA4B,UAAA,mBAAAC,8DAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAJ,MAAA,CAAAsB,SAAA,IAAAtB,MAAA,CAAAsB,SAAA;IAAA,EAAgC;IADrC/B,EAAA,CAAAG,YAAA,EACyD;;;;;;IACzDH,EAAA,CAAAC,cAAA,cACwD;IAAnDD,EAAA,CAAA4B,UAAA,mBAAAI,8DAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAJ,MAAA,CAAAsB,SAAA,IAAAtB,MAAA,CAAAsB,SAAA;IAAA,EAAgC;IADrC/B,EAAA,CAAAG,YAAA,EACwD;;;;;;IA8CpDH,EADF,CAAAC,cAAA,SAAI,SACG;IACHD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,cAA8C;IAArBD,EAAA,CAAA4B,UAAA,mBAAAM,sEAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAA4B,IAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA2B,IAAA,CAAK,IAAI,CAAC;IAAA,EAAC;IAACpC,EAAA,CAAAE,MAAA,oBAC5C;IAAAF,EAAA,CAAAqC,SAAA,sBAAoC;IACtCrC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,cAA+C;IAArBD,EAAA,CAAA4B,UAAA,mBAAAU,sEAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAA4B,IAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA2B,IAAA,CAAK,IAAI,CAAC;IAAA,EAAC;IAACpC,EAAA,CAAAE,MAAA,2BAC7C;IAAAF,EAAA,CAAAqC,SAAA,sBAAoC;IACtCrC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,cAA+C;IAArBD,EAAA,CAAA4B,UAAA,mBAAAW,sEAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAA4B,IAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA2B,IAAA,CAAK,IAAI,CAAC;IAAA,EAAC;IAC5CpC,EAAA,CAAAE,MAAA,uBACA;IAAAF,EAAA,CAAAqC,SAAA,sBAAoC;IACtCrC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,cAAiE;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACzEF,EADyE,CAAAG,YAAA,EAAK,EACzE;;;;;IAUGH,EADF,CAAAC,cAAA,cAAsF,WAC9E;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;;;;;IAFEH,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAwC,kBAAA,KAAAC,UAAA,CAAAC,KAAA,OAAoB;IACF1C,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAJ,UAAA,CAAAK,GAAA,EAA+B;;;;;IAH3D9C,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAA+C,UAAA,IAAAC,oEAAA,kBAAsF;IAIxFhD,EAAA,CAAAG,YAAA,EAAM;;;;IAJoBH,EAAA,CAAAc,SAAA,EAAc;IAAdd,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAwC,WAAA,CAAc;;;;;IASxCjD,EAAA,CAAAC,cAAA,eAA2E;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAM,SAAA,CAAAC,SAAA,CAA+B;;;;;IAKpGnD,EAAA,CAAAoD,uBAAA,GAAoH;IAClHpD,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAAnDH,EAAA,CAAAc,SAAA,GAA4C;IAA5Cd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAqD,WAAA,OAAAT,SAAA,CAAAM,SAAA,CAAAI,UAAA,CAAAR,GAAA,GAA4C;;;;;IAGpE9C,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAxCH,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAM,SAAA,CAAAI,UAAA,CAAAR,GAAA,EAAiC;;;;;IAL3D9C,EADF,CAAAC,cAAA,cAAwF,WAChF;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjCH,EAHA,CAAA+C,UAAA,IAAAQ,oFAAA,2BAAoH,IAAAC,mFAAA,gCAAAxD,EAAA,CAAAyD,sBAAA,CAGxF;IAG9BzD,EAAA,CAAAG,YAAA,EAAM;;;;;IAPEH,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAwC,kBAAA,KAAAc,UAAA,CAAAZ,KAAA,OAAoB;IACX1C,EAAA,CAAAc,SAAA,EAAiF;IAAAd,EAAjF,CAAAqB,UAAA,SAAAiC,UAAA,CAAAR,GAAA,gCAAAQ,UAAA,CAAAR,GAAA,sBAAiF,aAAAY,iBAAA,CAAkB;;;;;IAHtH1D,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAA+C,UAAA,IAAAY,qEAAA,kBAAwF;IAS1F3D,EAAA,CAAAG,YAAA,EAAM;;;;IAToBH,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAqB,UAAA,YAAAZ,MAAA,CAAAmD,aAAA,CAAgB;;;;;IAc5C5D,EAAA,CAAAC,cAAA,eAA2E;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAM,SAAA,CAAAC,SAAA,CAA+B;;;;;IAIxGnD,EAAA,CAAAC,cAAA,eAA2E;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAM,SAAA,CAAAC,SAAA,CAA+B;;;;;IAWxGnD,EAAA,CAAAoD,uBAAA,GAAmC;IACjCpD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,YACJ;IAAAF,EAAA,CAAAC,cAAA,YAA+D;;IAAAD,EAAA,CAAAE,MAAA,GAAwB;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAAAH,EAAA,CAAAE,MAAA,SAC7F;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADiBH,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAqB,UAAA,kBAAArB,EAAA,CAAAqD,WAAA,OAAAQ,UAAA,CAAAC,KAAA,GAAA9D,EAAA,CAAA+D,aAAA,CAAwC;IAAC/D,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAqD,WAAA,OAAAQ,UAAA,CAAAC,KAAA,EAAwB;;;;;IAN7F9D,EAAA,CAAAC,cAAA,SAA8C;IAC5CD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,WAAM;IACND,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA+C,UAAA,IAAAiB,mFAAA,2BAAmC;IAKnChE,EAAA,CAAAC,cAAA,YAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAE1EF,EAF0E,CAAAG,YAAA,EAAI,EACvE,EACF;;;;IAVHH,EAAA,CAAAc,SAAA,EACA;IADAd,EAAA,CAAAwC,kBAAA,MAAAqB,UAAA,CAAAI,eAAA,QACA;IACAjE,EAAA,CAAAc,SAAA,GACA;IADAd,EAAA,CAAAkE,kBAAA,MAAAL,UAAA,CAAAM,SAAA,OAAAN,UAAA,CAAAO,QAAA,OACA;IAAepE,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAqB,UAAA,SAAAwC,UAAA,CAAAC,KAAA,CAAkB;IAKX9D,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAqB,UAAA,qBAAAwC,UAAA,CAAAQ,KAAA,EAAArE,EAAA,CAAA+D,aAAA,CAAiC;IAAC/D,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAA2C,iBAAA,CAAAkB,UAAA,CAAAQ,KAAA,CAAgB;;;;;IAV1ErE,EAAA,CAAAC,cAAA,cAAyC;IACzCD,EAAA,CAAA+C,UAAA,IAAAuB,oEAAA,iBAA8C;IAY9CtE,EAAA,CAAAG,YAAA,EAAM;;;;IAZiBH,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAqB,UAAA,YAAAuB,SAAA,CAAA2B,YAAA,CAAqB;;;;;;IAsD5CvE,EAAA,CAAAC,cAAA,iBAC0H;IAA3ED,EAAA,CAAA4B,UAAA,mBAAA4C,mFAAA;MAAAxE,EAAA,CAAAO,aAAA,CAAAkE,IAAA;MAAA,MAAA7B,SAAA,GAAA5C,EAAA,CAAAU,aAAA,GAAAgE,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAkE,cAAA,CAAA/B,SAAA,EAAsB,MAAM,CAAC;IAAA,EAAC;IACpF5C,EAAA,CAAAqC,SAAA,cAAiC;IACnCrC,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAGkG;IAA1FD,EAAA,CAAA4B,UAAA,mBAAAgD,mFAAA;MAAA5E,EAAA,CAAAO,aAAA,CAAAsE,IAAA;MAAA,MAAAjC,SAAA,GAAA5C,EAAA,CAAAU,aAAA,GAAAgE,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqE,eAAA,CAAAlC,SAAA,EAAuBnC,MAAA,CAAAsE,sBAAA,CAAAnC,SAAA,CAAAoC,MAAA,CAAoC,GAAG,KAAK,GAAG,QAAQ,CAAC;IAAA,EAAC;IAC/FhF,EAAA,CAAAqC,SAAA,cAAwH;IAC1HrC,EAAA,CAAAG,YAAA,EAAS;;;;;IAJDH,EAAA,CAAAiF,qBAAA,aAAAxE,MAAA,CAAAsE,sBAAA,CAAAnC,SAAA,CAAAoC,MAAA,qBAAwE;IADEhF,EAA5C,CAAAqB,UAAA,aAAAZ,MAAA,CAAAyE,WAAA,CAAAtC,SAAA,CAAAuC,WAAA,EAA2C,YAAAnF,EAAA,CAAAoF,eAAA,IAAAC,GAAA,EAAAzC,SAAA,CAAA0C,YAAA,WAAmD;IAI7HtF,EAAA,CAAAc,SAAA,EAAkH;IAAlHd,EAAA,CAAAqB,UAAA,QAAAZ,MAAA,CAAAsE,sBAAA,CAAAnC,SAAA,CAAAoC,MAAA,yEAAAhF,EAAA,CAAA+D,aAAA,CAAkH;;;;;;IAzG3H/D,EAFJ,CAAAC,cAAA,aAAuD,aACmB,eAC3C;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACxD;IACLH,EAAA,CAAA+C,UAAA,IAAAwC,8DAAA,gCAAAvF,EAAA,CAAAyD,sBAAA,CAA4B;IAQ5BzD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvCH,EADF,CAAAC,cAAA,aAAqE,eACzB;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtEH,EADA,CAAA+C,UAAA,KAAAyC,wDAAA,mBAA2E,KAAAC,+DAAA,gCAAAzF,EAAA,CAAAyD,sBAAA,CACnD;IAa1BzD,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,cAAoE,gBACxB;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAA+C,UAAA,KAAA2C,wDAAA,mBAA2E;IAC7E1F,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,cAAoE,gBACxB;IAAAD,EAAA,CAAAE,MAAA,IACxC;IAAAF,EAAA,CAAA+C,UAAA,KAAA4C,wDAAA,mBAA2E;IAE/E3F,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,cAA6F,gBACjD;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAA+C,UAAA,KAAA6C,+DAAA,gCAAA5F,EAAA,CAAAyD,sBAAA,CAA0B;IAgB5BzD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA2B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EAAA,CAAAC,cAAA,cACmG;IAA/FD,EAAA,CAAA4B,UAAA,mBAAAiE,sEAAA;MAAA,MAAAjD,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAuF,IAAA,EAAApB,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAA+B,SAAA,CAAAmD,qBAAA,IAAwCtF,MAAA,CAAAuF,wBAAA,CAAApD,SAAA,CAAAmD,qBAAA,CAAqD;IAAA,EAAC;IAChG/F,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAChE;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,cACqF;IAAjFD,EAAA,CAAA4B,UAAA,mBAAAqE,sEAAA;MAAA,MAAArD,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAuF,IAAA,EAAApB,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAA+B,SAAA,CAAAsD,cAAA,IAAiCzF,MAAA,CAAAuF,wBAAA,CAAApD,SAAA,CAAAuD,cAAA,CAA8C;IAAA,EAAC;IAClFnG,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;IACLH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAqC,SAAA,iBAAoE;IACtErC,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAqC,SAAA,iBAAuE;IACzErC,EAAA,CAAAG,YAAA,EAAK;IAIDH,EAHJ,CAAAC,cAAA,cAAsC,eACC,gBACsB,kBAEqD;IAAhED,EAAA,CAAA4B,UAAA,mBAAAwE,0EAAA;MAAA,MAAAxD,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAuF,IAAA,EAAApB,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA4F,QAAA,CAAAzD,SAAA,CAAA0D,iBAAA,EAAA1D,SAAA,CAAA2D,aAAA,CAAqD;IAAA,EAAC;IAC3GvG,EAAA,CAAAqC,SAAA,eAAuC;IAEzCrC,EADA,CAAAG,YAAA,EAAS,EACF;IACPH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3BH,EADA,CAAAC,cAAA,gBAA+C,kBAE6C;IAA3CD,EAAA,CAAA4B,UAAA,mBAAA4E,0EAAA;MAAA,MAAA5D,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAuF,IAAA,EAAApB,SAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAkE,cAAA,CAAA/B,SAAA,EAAsB,SAAS,CAAC;IAAA,EAAC;IACzF5C,EAAA,CAAAqC,SAAA,eAAoC;IAEtCrC,EADA,CAAAG,YAAA,EAAS,EACF;IACPH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAA+C,UAAA,KAAA0D,0DAAA,qBAC0H;IAG1HzG,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,gBAAqG;IACrGD,EAAA,CAAA+C,UAAA,KAAA2D,0DAAA,qBAGkG;IAMxG1G,EAHM,CAAAG,YAAA,EAAO,EACH,EACH,EACF;;;;;;;;IAhHDH,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAoF,eAAA,KAAAC,GAAA,EAAAzC,SAAA,CAAA0C,YAAA,WAAkD;IAChDtF,EAAA,CAAAc,SAAA,EAA0B;IAAuBd,EAAjD,CAAAqB,UAAA,aAAAsF,iBAAA,CAA0B,mBAAyC;IAC1C3G,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAA2D,aAAA,CAAyB;IAUlDvG,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAC,OAAA,CAAA+D,YAAA,CAAgC;IAC/B5G,EAAA,CAAAc,SAAA,EAAsB;IAAuBd,EAA7C,CAAAqB,UAAA,aAAAwF,aAAA,CAAsB,mBAAyC;IACxB7G,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAkE,SAAA,CAAqB;IAC5B9G,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAAqB,UAAA,SAAAuB,SAAA,CAAAM,SAAA,CAAAC,SAAA,QAAsC;IAevEnD,EAAA,CAAAc,SAAA,GAAsB;IAAuBd,EAA7C,CAAAqB,UAAA,aAAAwF,aAAA,CAAsB,mBAAyC;IACvB7G,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAmE,GAAA,CAAe;IACtB/G,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAAqB,UAAA,SAAAuB,SAAA,CAAAM,SAAA,CAAAC,SAAA,QAAsC;IAEvEnD,EAAA,CAAAc,SAAA,EAAsB;IAAuBd,EAA7C,CAAAqB,UAAA,aAAAwF,aAAA,CAAsB,mBAAyC;IACvB7G,EAAA,CAAAc,SAAA,GACxC;IADwCd,EAAA,CAAAwC,kBAAA,KAAAI,SAAA,CAAAoE,SAAA,MACxC;IAAmChH,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAAqB,UAAA,SAAAuB,SAAA,CAAAM,SAAA,CAAAC,SAAA,QAAsC;IAGzEnD,EAAA,CAAAc,SAAA,EAA+C;IAAuBd,EAAtE,CAAAqB,UAAA,aAAAuB,SAAA,CAAAqE,UAAA,GAAAC,cAAA,MAA+C,mBAAyC;IAChDlH,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAqE,UAAA,CAAsB;IAkB9DjH,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAuE,GAAA,CAAe;IACfnH,EAAA,CAAAc,SAAA,GAA2B;IAA3Bd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAqD,WAAA,SAAAT,SAAA,CAAAwE,IAAA,EAA2B;IAC3BpH,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAqD,WAAA,SAAAT,SAAA,CAAAyE,WAAA,EAAkC;IAClCrH,EAAA,CAAAc,SAAA,GAA2B;IAA3Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAA0E,eAAA,CAA2B;IAC3BtH,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAA2E,oBAAA,CAAgC;IAChCvH,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAA4E,cAAA,CAA0B;IAE1BxH,EAAA,CAAAc,SAAA,EAA6D;IAA7Dd,EAAA,CAAAyH,UAAA,CAAA7E,SAAA,CAAAmD,qBAAA,yBAA6D;IAEpC/F,EAAA,CAAAc,SAAA,GAAiC;IAAjCd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAmD,qBAAA,CAAiC;IAE1D/F,EAAA,CAAAc,SAAA,GAA+C;IAA/Cd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAA0H,WAAA,SAAA9E,SAAA,CAAA+E,eAAA,gBAA+C;IAC/C3H,EAAA,CAAAc,SAAA,GAA+C;IAA/Cd,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAA0H,WAAA,SAAA9E,SAAA,CAAAgF,eAAA,gBAA+C;IAC/C5H,EAAA,CAAAc,SAAA,GAAsD;IAAtDd,EAAA,CAAAyH,UAAA,CAAA7E,SAAA,CAAAsD,cAAA,yBAAsD;IAE7BlG,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA2C,iBAAA,CAAAC,SAAA,CAAAsD,cAAA,CAA0B;IAG9BlG,EAAA,CAAAc,SAAA,GAAiB;IAACd,EAAlB,CAAAqB,UAAA,kBAAiB,YAAAuB,SAAA,CAAAiF,QAAA,CAA2B;IAG5C7H,EAAA,CAAAc,SAAA,GAAiB;IAACd,EAAlB,CAAAqB,UAAA,kBAAiB,YAAAuB,SAAA,CAAAkF,WAAA,CAA8B;IAK9B9H,EAAA,CAAAc,SAAA,GAA0C;IAACd,EAA3C,CAAAqB,UAAA,aAAAuB,SAAA,CAAAmF,cAAA,UAA0C,YAAA/H,EAAA,CAAAoF,eAAA,KAAAC,GAAA,EAAAzC,SAAA,CAAA0C,YAAA,WAAmD;IAc3CtF,EAAA,CAAAc,SAAA,GAAgC;IAAhCd,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAuH,YAAA,eAAgC;IAIlHhI,EAAA,CAAAc,SAAA,GAAwE;IAAxEd,EAAA,CAAAiF,qBAAA,aAAAxE,MAAA,CAAAsE,sBAAA,CAAAnC,SAAA,CAAAoC,MAAA,qBAAwE;IAGrEhF,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAAuH,YAAA,cAA+B;;;;;IAW5ChI,EADF,CAAAC,cAAA,SAAwB,aACW;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IACpDF,EADoD,CAAAG,YAAA,EAAK,EACpD;;;;;IACLH,EAAA,CAAAC,cAAA,SAAyB;IACvBD,EAAA,CAAAqC,SAAA,aAAsB;IACxBrC,EAAA,CAAAG,YAAA,EAAK;;;;;IAFLH,EAHA,CAAA+C,UAAA,IAAAkF,uDAAA,iBAAwB,IAAAC,uDAAA,iBAGC;;;;IAHpBlI,EAAA,CAAAqB,UAAA,SAAAZ,MAAA,CAAA0H,WAAA,CAAiB;IAGjBnI,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAqB,UAAA,UAAAZ,MAAA,CAAA0H,WAAA,CAAkB;;;;;IAJzBnI,EAAA,CAAA+C,UAAA,IAAAqF,kDAAA,0BAAwH;;;AD5ShI,WAAaC,wBAAwB;EAA/B,MAAOA,wBAAwB;IAqDnCC,YACmBC,iBAAiC,EACjCC,qBAA4C,EAC5CC,QAAkB,EAClBC,YAA0B,EAC1BC,qBAA4C,EAC5CC,cAA8B,EAC9BC,UAAsB,EACtBC,QAAyB,EACzBC,iBAAoC;MARpC,KAAAR,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,iBAAiB,GAAjBA,iBAAiB;MA3DpC,KAAAZ,WAAW,GAAY,KAAK;MAC5B,KAAAa,gBAAgB,GAAY,KAAK;MACjC,KAAAC,SAAS,GAAU,EAAE;MACrB,KAAAC,QAAQ,GAAG,IAAI;MAEf,KAAAC,UAAU,GAAY,KAAK;MAC3B,KAAAC,UAAU,GAAU,EAAE;MACtB,KAAAC,gBAAgB,GAAU,EAAE;MAC5B,KAAAC,iBAAiB,GAAU,EAAE;MAC7B,KAAAC,cAAc,GAAU,EAAE;MAC1B,KAAA5H,sBAAsB,GAAU,EAAE;MAClC,KAAA6H,kBAAkB,GAAU,EAAE;MAC9B,KAAAC,cAAc,GAAU,CAAC;QAACC,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAK,CAAC,EAAE;QAACD,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAI,CAAC,CAAC;MAChF,KAAA/I,QAAQ,GAAQ,EAAE;MAClB,KAAAgJ,WAAW,GAAQ,EAAE;MAErB,KAAAC,KAAK,GAAW,CAAC;MACjB,KAAAC,QAAQ,GAAW,EAAE;MACrB,KAAAC,SAAS,GAAW,CAAC;MACrB,KAAAhI,SAAS,GAAY,KAAK;MAC1B,KAAAiI,IAAI,GAAQ,EAAE;MACd,KAAAC,KAAK,GAAQ,EAAE;MACf,KAAAC,kBAAkB,GAAU,EAAE;MAE9B,KAAAC,WAAW,GAAQ,EAAE;MACrB,KAAAC,cAAc,GAAU,EAAE;MAE1B,KAAAnH,WAAW,GAAG,CACZ;QAACP,KAAK,EAAE,SAAS;QAAEI,GAAG,EAAE;MAAe,CAAC,EACxC;QAACJ,KAAK,EAAE,WAAW;QAAEI,GAAG,EAAE;MAAc,CAAC,EACzC;QAACJ,KAAK,EAAE,cAAc;QAAEI,GAAG,EAAE;MAAa,CAAC,EAC3C;QAACJ,KAAK,EAAE,KAAK;QAAEI,GAAG,EAAE;MAAK,CAAC,EAC1B;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAY,CAAC,EACzC;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAY,CAAC,EACzC;QAACJ,KAAK,EAAE,cAAc;QAAEI,GAAG,EAAE;MAAgB,CAAC,EAC9C;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAc,CAAC,EAC3C;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAgB,CAAC,CAC9C;MAED,KAAAc,aAAa,GAAG,CACd;QAAClB,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAW,CAAC,EACxC;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAK,CAAC,EAClC;QAACJ,KAAK,EAAE,OAAO;QAAEI,GAAG,EAAE;MAAgB,CAAC,EACvC;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAuB,CAAC,EACpD;QAACJ,KAAK,EAAE,YAAY;QAAEI,GAAG,EAAE;MAAW,CAAC,EACvC;QAACJ,KAAK,EAAE,SAAS;QAAEI,GAAG,EAAE;MAAU,CAAC,EACnC;QAACJ,KAAK,EAAE,aAAa;QAAEI,GAAG,EAAE;MAAc,CAAC,EAC3C;QAACJ,KAAK,EAAE,eAAe;QAAEI,GAAG,EAAE;MAAe,CAAC,CAC/C;IAaD;IAEAuH,QAAQA,CAAA;MACN,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACkB,mBAAmB,EAAE;MAC1B,IAAI,CAACC,eAAe,EAAE;IACxB;IAEA;IACAA,eAAeA,CAAA;MACb,IAAI,CAAC3B,cAAc,CAAC4B,IAAI,EAAE;MAC1B,MAAMC,MAAM,GAAO;QACjBC,SAAS,EAAE;UACTC,yBAAyB,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,kBAAkB,EAAE,WAAW;SACrG;QACDvI,IAAI,EAAE;OACP;MACD,IAAI,CAACoG,qBAAqB,CAACoC,sBAAsB,CAACH,MAAM,CAAC,CAACI,SAAS,CAAC;QAClEC,IAAI,EAAGC,QAAa,IAAU;UAC5B,IAAI,CAACnC,cAAc,CAACoC,IAAI,EAAE;UAC1B,MAAMC,IAAI,GAAOF,QAAQ,CAACE,IAAI,CAACC,OAAO;UACtC,IAAID,IAAI,EAAE;YACR,IAAI,CAACf,kBAAkB,GAAGe,IAAI;YAC9B,IAAI,CAAC5B,gBAAgB,GACnB4B,IAAI,CAACE,MAAM,CAAEC,IAAS,IAAcA,IAAI,CAAC,wBAAwB,CAAC,KAAK,YAAY,IACjF,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACrF,IAAI,CAAC9B,iBAAiB,GACpB2B,IAAI,CAACE,MAAM,CAAEC,IAAS,IAAcA,IAAI,CAAC,wBAAwB,CAAC,KAAK,aAAa,CAAC;YACvF,IAAI,CAAC7B,cAAc,GACjB0B,IAAI,CAACE,MAAM,CAAEC,IAAS,IAAcA,IAAI,CAAC,wBAAwB,CAAC,KAAK,UAAU,CAAC;YACpF,IAAI,CAACzJ,sBAAsB,GACzBsJ,IAAI,CAACE,MAAM,CAAEC,IAAS,IAAcA,IAAI,CAAC,wBAAwB,CAAC,KAAK,kBAAkB,CAAC;YAC5F,IAAI,CAAC5B,kBAAkB,GACrByB,IAAI,CAACE,MAAM,CAAEC,IAAS,IAAcA,IAAI,CAAC,wBAAwB,CAAC,KAAK,WAAW,CAAC;UACvF;QACF,CAAC;QACDE,KAAK,EAAEA,CAAA,KAAW;UAChB,IAAI,CAAC1C,cAAc,CAACoC,IAAI,EAAE;QAC5B;OACD,CAAC;IACJ;IAEA;IACAO,UAAUA,CAAC1B,KAAK,GAAG,CAAC,EAAE2B,IAAI,GAAG,IAAI,CAAC1B,QAAQ,EAAE2B,SAAA,GAAqB,KAAK;MACpE,IAAIA,SAAS,EAAE;QACb,IAAI,CAAC5B,KAAK,GAAG,CAAC;MAChB,CAAC,MAAM;QACL,IAAI,CAACA,KAAK,GAAGA,KAAK;MACpB;MACA,IAAI,CAACC,QAAQ,GAAG0B,IAAI;MACpB,IAAIf,MAAM,GAAQ;QAChBiB,OAAO,EAAE,IAAI,CAACC,gBAAgB,EAAE;QAChCC,SAAS,EAAE/B,KAAK;QAChBgC,QAAQ,EAAEL,IAAI;QACdM,OAAO,EAAE,IAAI,CAAClC,WAAW,CAAC,UAAU,CAAC,GAAG;UAAE,CAAC,IAAI,CAACA,WAAW,CAAC,UAAU,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,WAAW;QAAC,CAAE,GAAG;OAC7G;MACD,IAAI,CAACO,WAAW,GAAGM,MAAM;MACzB,IAAI,CAACsB,qBAAqB,CAACtB,MAAM,CAAC;MAClC,IAAI,CAACtC,WAAW,GAAG,IAAI;IACzB;IAEAnC,wBAAwBA,CAACD,qBAA0B;MACjDiG,MAAM,CAACC,IAAI,CAAC,qEAAqE,GAAGlG,qBAAqB,CAAC;IAC5G;IAEAmG,iBAAiBA,CAACC,GAAQ;MACxBH,MAAM,CAACC,IAAI,CAACE,GAAG,CAAC;IAClB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACpD,gBAAgB,GAAG,IAAI;IAC9B;IAEAqD,WAAWA,CAAA;MACT,IAAI,CAACrD,gBAAgB,GAAG,KAAK;IAC/B;IAEA3C,QAAQA,CAACC,iBAAyB,EAACC,aAAkB;MACnD,IAAI+F,IAAI,GAAQ;QACdC,QAAQ,EAAEjG,iBAAiB;QAC3BkG,QAAQ,EAAE;OACX;MACD,IAAI,CAAC7D,qBAAqB,CAAC8D,YAAY,CAACH,IAAI,CAAC,CAACzB,SAAS,CAAC;QACtDC,IAAI,EAAG4B,CAAM,IAAU;UACrB,IAAIA,CAAC,CAACzB,IAAI,EAAE;YACV;YACA,IAAIe,MAAM,CAACW,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;cACtD,MAAMC,aAAa,GAAG,OAAO,GAAGvG,aAAa,GAAG,MAAM;cACtD,IAAI,CAACuC,QAAQ,CAACiE,uBAAuB,CAACL,CAAC,CAACzB,IAAI,EAAE6B,aAAa,CAAC;YAC9D,CAAC,MAAM;cACL,IAAI,CAACE,gBAAgB,CAACC,UAAU,CAACP,CAAC,CAACzB,IAAI,EAAE,iBAAiB,CAAC;YAC7D;UACF,CAAC,MAAM;YACL,IAAIA,IAAI,GAAG;cACTiC,OAAO,EAAE,OAAO;cAChBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE,0BAA0B;cAClCC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE,EAAE;cACnBC,aAAa,EAAE,IAAI;cACnBC,gBAAgB,EAAE;aACnB;YACD,IAAI,CAAC3E,UAAU,CAAC4E,YAAY,CAACxC,IAAI,CAAC;UACpC;QACF;OACD,CAAC;IACJ;IAEA7I,IAAIA,CAACsL,KAAU;MAEb,IAAI,CAAC,IAAI,CAACvF,WAAW,EAAE;QACrB;MACF;MAEA,IAAI,IAAI,CAACyB,WAAW,CAAC,UAAU,CAAC,KAAK8D,KAAK,IAAI,IAAI,CAAC9D,WAAW,CAAC,UAAU,CAAC,IAAI+D,SAAS,EAAE;QACvF,IAAI,IAAI,CAAC/D,WAAW,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;UAC3C,IAAI,CAACA,WAAW,CAAC,WAAW,CAAC,GAAG,MAAM;QACxC,CAAC,MAAM;UACL,IAAI,CAACA,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK;QACvC;MACF,CAAC,MAAM;QACL,IAAI,CAACA,WAAW,CAAC,UAAU,CAAC,GAAG8D,KAAK;QACpC,IAAI,CAAC9D,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK;MACvC;MAEA,IAAIgE,OAAO,GAAQ,EAAE;MACrBA,OAAO,CAAC,IAAI,CAAChE,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,WAAW,CAAC;MACrE,MAAMa,MAAM,GAAG;QACbiB,OAAO,EAAE,IAAI,CAACC,gBAAgB,EAAE;QAChCC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE8B;OACV;MAED,IAAI,CAACzD,WAAW,GAAGM,MAAM;MACzB,IAAI,CAACsB,qBAAqB,CAACtB,MAAM,CAAC;IACpC;IAEAsB,qBAAqBA,CAACtB,MAAW;MAC/B,IAAI,CAAC7B,cAAc,CAAC4B,IAAI,EAAE;MAC1B,IAAI,CAAC9B,YAAY,CAACmF,mBAAmB,CAACpD,MAAM,CAAC,CAACI,SAAS,CAAC;QACtDC,IAAI,EAAG4B,CAAM,IAAU;UACrB,IAAI,CAAC9D,cAAc,CAACoC,IAAI,EAAE;UAC1B,IAAI0B,CAAC,CAACzB,IAAI,EAAE;YACV,IAAI,CAAChC,SAAS,GAAGyD,CAAC,CAACzB,IAAI,CAAC,SAAS,CAAC;YAClC,IAAI,CAAClB,SAAS,GAAG2C,CAAC,CAACzB,IAAI,CAAC,OAAO,CAAC;YAChC,IAAI,IAAI,CAAChC,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC6E,MAAM,GAAG,CAAC,EAAE;cAC/C,IAAI,CAAC7E,SAAS,CAAC8E,OAAO,CAAC3C,IAAI,IAAG;gBAC5B,IAAIA,IAAI,CAACvI,OAAO,IAAIuI,IAAI,CAACvI,OAAO,CAACmL,cAAc,EAAE;kBAC/C5C,IAAI,CAACvI,OAAO,CAACmL,cAAc,GAAG,IAAI,CAACC,gBAAgB,CAAC7C,IAAI,CAACvI,OAAO,CAACmL,cAAc,CAAC;kBAChF5C,IAAI,CAACvI,OAAO,CAACqL,YAAY,GAAG,IAAI,CAACD,gBAAgB,CAAC7C,IAAI,CAACvI,OAAO,CAACqL,YAAY,CAAC;gBAC9E;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAACnE,SAAS,GAAG,CAAC;cAClB,IAAI,CAACD,QAAQ,GAAG,EAAE;YACpB;UACF;QACF,CAAC;QACDwB,KAAK,EAAEA,CAAA,KAAW;UAChB,IAAI,CAAC1C,cAAc,CAACoC,IAAI,EAAE;QAC5B;OACD,CAAC;IACJ;IAEAiD,gBAAgBA,CAACE,UAAe;MAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMG,EAAE,GAAGC,MAAM,CAACH,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvD,MAAMC,EAAE,GAAGH,MAAM,CAACH,IAAI,CAACO,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAClD,MAAMG,IAAI,GAAGR,IAAI,CAACS,WAAW,EAAE;MAC/B,OAAO,GAAGP,EAAE,IAAII,EAAE,IAAIE,IAAI,EAAE;IAC9B;IAEAjK,cAAcA,CAACmK,KAAU,EAAEC,MAAc;MACvC,IAAI,CAACC,2BAA2B,CAAC/B,UAAU,CAAC,IAAI,EAAE6B,KAAK,EAAEC,MAAM,CAAC;IAClE;IAEAjK,eAAeA,CAACgK,KAAU,EAAEC,MAAW;MACrC,IAAIA,MAAM,KAAK,KAAK,EAAE;QACpB,IAAI,CAAC3F,UAAU,CAAC6F,IAAI,CAACH,KAAK,CAAC;MAC7B,CAAC,MAAM,IAAIC,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAMG,KAAK,GAAG,IAAI,CAAC9F,UAAU,CAAC+F,SAAS,CAAC/D,IAAI,IAAIA,IAAI,CAACpG,MAAM,KAAK8J,KAAK,CAAC9J,MAAM,CAAC;QAC7E,IAAIkK,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC9F,UAAU,CAACgG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAClC;QACA,IAAI,CAAChG,QAAQ,GAAG,IAAI;MACtB;IACF;IAEAnE,sBAAsBA,CAACC,MAAW;MAChC,OAAO,CAAC,IAAI,CAACoE,UAAU,CAACiG,IAAI,CAACjE,IAAI,IAAIA,IAAI,CAACpG,MAAM,KAAKA,MAAM,CAAC;IAC9D;IAGAE,WAAWA,CAACoK,MAAW;MACrB,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAI,MAAM,EAAE;QACtC,OAAO,KAAK;MACd,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IAEAC,MAAMA,CAAA;MACJ,IAAI,CAAC3G,cAAc,CAAC4B,IAAI,EAAE;MAC1B,IAAIC,MAAM,GAAQ;QAChBiB,OAAO,EAAE,IAAI,CAACC,gBAAgB,EAAE;QAChCC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,EAAE;QACX0D,QAAQ,EAAE;OACX;MACD,IAAI,CAAC9G,YAAY,CAAC+G,eAAe,CAAChF,MAAM,CAAC,CAACI,SAAS,CAAC;QAClDC,IAAI,EAAG4E,GAAS,IAAU;UACxB,IAAI,CAAC9G,cAAc,CAACoC,IAAI,EAAE;UAC1B,MAAMmB,GAAG,GAAGH,MAAM,CAAC2D,GAAG,CAACC,eAAe,CAACF,GAAG,CAAC;UAC3C,MAAMG,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;UACvCF,QAAQ,CAACxD,IAAI,CAAC2D,WAAW,CAACJ,CAAC,CAAC;UAC5BA,CAAC,CAACK,IAAI,GAAG/D,GAAG;UACZ0D,CAAC,CAACM,QAAQ,GAAG,IAAI,CAACC,gBAAgB,EAAE;UACpCP,CAAC,CAACQ,KAAK,EAAE;UACTrE,MAAM,CAAC2D,GAAG,CAACW,eAAe,CAACnE,GAAG,CAAC;UAC/B2D,QAAQ,CAACxD,IAAI,CAACiE,WAAW,CAACV,CAAC,CAAC;QAC9B,CAAC;QACDvE,KAAK,EAAEA,CAAA,KAAW;UAChB,IAAI,CAAC1C,cAAc,CAACoC,IAAI,EAAE;UAC1B,IAAIwF,UAAU,GAAG;YACftD,OAAO,EAAE,mBAAmB;YAC5BC,QAAQ,EAAEzN,cAAc;YACxB0N,MAAM,EAAE,qCAAqC;YAC7CC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE,EAAE;YACnBC,aAAa,EAAE,IAAI;YACnBC,gBAAgB,EAAE;WACnB;UACD,IAAI,CAAC3E,UAAU,CAAC4E,YAAY,CAAC+C,UAAU,CAAC,CAACC,OAAO,CAAC5F,SAAS,EAAE;QAC9D;OACD,CAAC;IACJ;IAEAuF,gBAAgBA,CAAA;MACd,MAAMM,SAAS,GAAG9Q,MAAM,CAAC,IAAIyO,IAAI,EAAE,CAAC,CAACsC,MAAM,CAAC,gBAAgB,CAAC;MAC7D,OAAO,SAASD,SAAS,MAAM;IACjC;IAGAE,WAAWA,CAAC9B,KAAU;MACpB,IAAI,CAAC+B,OAAO,CAAC5D,UAAU,CAAC,IAAI,EAAE6B,KAAK,CAAC;IACtC;IAEAgC,WAAWA,CAAA;MACT,IAAI,CAACvF,UAAU,EAAE;IACnB;IAEAwF,KAAKA,CAAA;MACH,IAAI,CAACnQ,QAAQ,GAAG,EAAE;IACpB;IAEAhB,MAAMA,CAACwO,IAAS,EAAEuC,MAAc;MAC9B,OAAOvC,IAAI,GAAG,IAAI,CAAC3F,QAAQ,CAACuI,SAAS,CAAC5C,IAAI,EAAEuC,MAAM,CAAC,GAAG,IAAI;IAC5D;IAEAM,MAAMA,CAAChI,SAAc;MACnB,IAAI,CAACG,UAAU,GAAGH,SAAS;IAC7B;IAEAiI,YAAYA,CAACC,KAAS,EAACtH,KAAS,EAACC,QAAY;MAC3C,QAAOqH,KAAK,CAACpC,MAAM;QACjB,KAAK,QAAQ;UACX,IAAI,CAACkC,MAAM,CAACE,KAAK,CAACrC,KAAK,CAAC;UACxB;QACF,KAAK,aAAa;UAChB,IAAI,CAAC1F,UAAU,GAAG,EAAE;UACpB,IAAI,CAACmC,UAAU,CAAC1B,KAAK,EAACC,QAAQ,CAAC;UAC/B;MACJ;IACF;IAGA6B,gBAAgBA,CAAA;MACd,MAAMyF,aAAa,GAAQzR,CAAC,CAAC0R,SAAS,CAAC,IAAI,CAACzQ,QAAQ,CAAC;MACrD,IAAI,CAAC,IAAI,CAAC0Q,OAAO,CAACF,aAAa,CAAC,kBAAkB,CAAC,CAAC,EAAE;QACpDA,aAAa,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACxR,MAAM,CAACwR,aAAa,CAAC,kBAAkB,CAAC,EAAE,YAAY,CAAC;MAClG;MACA,IAAI,CAAC,IAAI,CAACE,OAAO,CAACF,aAAa,CAAC,gBAAgB,CAAC,CAAC,EAAE;QAClDA,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACxR,MAAM,CAACwR,aAAa,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC;MAC9F;MACA,IAAI,CAAC,IAAI,CAACE,OAAO,CAACF,aAAa,CAAC,kBAAkB,CAAC,CAAC,EAAE;QACpDA,aAAa,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACxR,MAAM,CAACwR,aAAa,CAAC,kBAAkB,CAAC,EAAE,YAAY,CAAC;MAClG;MACA,IAAI,CAAC,IAAI,CAACE,OAAO,CAACF,aAAa,CAAC,gBAAgB,CAAC,CAAC,EAAE;QAClDA,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACxR,MAAM,CAACwR,aAAa,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC;MAC9F;MACAA,aAAa,CAAC,QAAQ,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACjJ,iBAAiB,CAACkJ,4BAA4B,CAAC,QAAQ,CAAC,CAAC;MACnGL,aAAa,CAAC,QAAQ,CAAC,GAAGA,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,GAAGA,aAAa,CAAC,QAAQ,CAAC;MACxF,IAAIA,aAAa,CAAC,YAAY,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,YAAY,CAAC,CAAC,EAAE;QAC7EA,aAAa,CAAC,YAAY,CAAC,GAAGA,aAAa,CAAC,YAAY,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;MACrE;MACA,IAAIR,aAAa,CAAC,aAAa,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,aAAa,CAAC,CAAC,EAAE;QAC/EA,aAAa,CAAC,aAAa,CAAC,GAAGA,aAAa,CAAC,aAAa,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;MACvE;MACA,IAAIR,aAAa,CAAC,cAAc,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,cAAc,CAAC,CAAC,EAAE;QACjFA,aAAa,CAAC,cAAc,CAAC,GAAGA,aAAa,CAAC,cAAc,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;MACzE;MACA,IAAIR,aAAa,CAAC,kBAAkB,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,kBAAkB,CAAC,CAAC,EAAE;QACzFA,aAAa,CAAC,kBAAkB,CAAC,GAAGA,aAAa,CAAC,kBAAkB,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;MACjF;MAEA,OAAOR,aAAa;IACtB;IAEAS,SAASA,CAACV,KAAoB;MAC5B,IAAIA,KAAK,CAACrO,GAAG,KAAK,OAAO,EAAE;QACzB,IAAI,CAACyI,UAAU,EAAE;MACnB;IACF;IAEAuG,OAAOA,CAACX,KAAU;MAChB,MAAMY,KAAK,GAAGZ,KAAK,CAACa,MAA0B;MAE9CD,KAAK,CAACrI,KAAK,GAAGqI,KAAK,CAACrI,KAAK,CAACuI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAEhD,IAAIF,KAAK,CAACrI,KAAK,CAACoE,MAAM,GAAG,EAAE,EAAE;QAC3BiE,KAAK,CAACrI,KAAK,GAAGqI,KAAK,CAACrI,KAAK,CAACwI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACxC;MAEA,IAAI,CAACtR,QAAQ,CAAC,QAAQ,CAAC,GAAGmR,KAAK,CAACrI,KAAK;IACvC;IAEA4H,OAAOA,CAAC5H,KAAU;MAChB,OAAOA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKiE,SAAS;IAC9D;IAEArD,mBAAmBA,CAAA;MACjB,IAAI,CAACvB,iBAAiB,CAACoJ,gBAAgB,CAAC,sBAAsB,CAAC,CAACtH,SAAS,CAAC;QACxEC,IAAI,EAAG4B,CAAM,IAAU;UACrB,IAAIA,CAAC,CAACzB,IAAI,EAAE;YACV,IAAI,CAACb,cAAc,GAAGsC,CAAC,CAACzB,IAAI;UAC9B;QACF;OACD,CAAC;IACJ;IAEAjD,YAAYA,CAACoK,UAAkB;MAC7B,OAAO,IAAI,CAACrJ,iBAAiB,CAACsJ,aAAa,CAAC,IAAI,CAACjI,cAAc,EAAEgI,UAAU,CAAC;IAC9E;IAAC,QAAAzS,CAAA,G;uBA1ZU0I,wBAAwB,EAAArI,EAAA,CAAAsS,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxS,EAAA,CAAAsS,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAA1S,EAAA,CAAAsS,iBAAA,CAAAK,EAAA,CAAAC,QAAA,GAAA5S,EAAA,CAAAsS,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA9S,EAAA,CAAAsS,iBAAA,CAAAS,EAAA,CAAAC,qBAAA,GAAAhT,EAAA,CAAAsS,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAlT,EAAA,CAAAsS,iBAAA,CAAAa,EAAA,CAAAC,UAAA,GAAApT,EAAA,CAAAsS,iBAAA,CAAAe,EAAA,CAAAC,eAAA,GAAAtT,EAAA,CAAAsS,iBAAA,CAAAiB,EAAA,CAAAC,iBAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAxBpL,wBAAwB;MAAAqL,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;yBAExB/T,gBAAgB;yBAKhBC,2BAA2B;yBAmB3BF,gBAAgB;;;;;;;;;;;;;;;;UC/CzBG,EAFJ,CAAAC,cAAA,aAA8B,aACwB,aACR;UAAAD,EAAA,CAAAE,MAAA,uBAAgB;UAC5DF,EAD4D,CAAAG,YAAA,EAAM,EAC5D;UACNH,EAAA,CAAAC,cAAA,aAAyE;UAA9BD,EAAA,CAAA4B,UAAA,qBAAAmS,yDAAAzT,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAAWiT,GAAA,CAAAjC,SAAA,CAAAvR,MAAA,CAAiB;UAAA,EAAC;UAGlEN,EAFJ,CAAAC,cAAA,aAAsD,cACjC,cACa;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAA,CAAAC,cAAA,wBASsB;UANpBD,EAAA,CAAAI,gBAAA,2BAAA6T,yEAAA3T,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAlT,QAAA,CAAsB,aAAa,GAAAN,MAAA,MAAAwT,GAAA,CAAAlT,QAAA,CAAb,aAAa,IAAAN,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAE;UAOzCN,EAVE,CAAAG,YAAA,EASsB,EAClB;UAEJH,EADF,CAAAC,cAAA,eAAmB,eACa;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAA,CAAAC,cAAA,yBASsB;UANpBD,EAAA,CAAAI,gBAAA,2BAAA8T,0EAAA5T,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAlT,QAAA,CAAsB,YAAY,GAAAN,MAAA,MAAAwT,GAAA,CAAAlT,QAAA,CAAZ,YAAY,IAAAN,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAE;UAOxCN,EAVE,CAAAG,YAAA,EASsB,EAClB;UAEJH,EADF,CAAAC,cAAA,eAAmB,eACa;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACjDH,EAAA,CAAAC,cAAA,yBASsB;UANpBD,EAAA,CAAAI,gBAAA,2BAAA+T,0EAAA7T,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAlT,QAAA,CAAsB,cAAc,GAAAN,MAAA,MAAAwT,GAAA,CAAAlT,QAAA,CAAd,cAAc,IAAAN,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAE;UAO1CN,EAVE,CAAAG,YAAA,EASsB,EAClB;UAEJH,EADF,CAAAC,cAAA,eAAmB,eACa;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3CH,EAAA,CAAAC,cAAA,iBAAmI;UAA5DD,EAAA,CAAA4B,UAAA,mBAAAwS,0DAAA9T,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAASiT,GAAA,CAAAhC,OAAA,CAAAxR,MAAA,CAAe;UAAA,EAAC;UAACN,EAAA,CAAAI,gBAAA,2BAAAiU,kEAAA/T,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAlT,QAAA,CAAsB,QAAQ,GAAAN,MAAA,MAAAwT,GAAA,CAAAlT,QAAA,CAAR,QAAQ,IAAAN,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAE;UACnIN,EADE,CAAAG,YAAA,EAAmI,EAC/H;UAmENH,EAlEA,CAAA+C,UAAA,KAAAuR,wCAAA,kBAAsC,KAAAC,wCAAA,kBAKA,KAAAC,wCAAA,mBAKiB,KAAAC,wCAAA,mBA4BA,KAAAC,wCAAA,kBA4BjB;UAatC1U,EAAA,CAAAC,cAAA,eAAkE;UAGhED,EAFA,CAAA+C,UAAA,KAAA4R,wCAAA,kBACyD,KAAAC,wCAAA,kBAED;UAEtD5U,EADF,CAAAC,cAAA,eAA2C,kBAEf;UAAlBD,EAAA,CAAA4B,UAAA,mBAAAiT,2DAAA;YAAA7U,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAASiT,GAAA,CAAA/C,KAAA,EAAO;UAAA,EAAC;UAAC/Q,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,kBACgD;UAAxCD,EAAA,CAAA4B,UAAA,mBAAAkT,2DAAA;YAAA9U,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAASiT,GAAA,CAAAvI,UAAA,CAAW,CAAC,EAAAuI,GAAA,CAAAhK,QAAA,EAAY,IAAI,CAAC;UAAA,EAAC;UAIvD9J,EAJ2H,CAAAG,YAAA,EAAS,EACxH,EACF,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,eAA2D,eACrC,eACgD,eACrB;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG3EH,EAFJ,CAAAC,cAAA,eAA6C,eACzB,kBAGf;UAD2CD,EAAA,CAAA4B,UAAA,mBAAAmT,2DAAA;YAAA/U,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAASiT,GAAA,CAAAvE,MAAA,EAAQ;UAAA,EAAC;UAC7DvP,EAAA,CAAAqC,SAAA,eAA+C;UAClDrC,EADkD,CAAAG,YAAA,EAAS,EACrD;UAEJH,EADF,CAAAC,cAAA,eAAkB,kBAIf;UADOD,EAAA,CAAA4B,UAAA,mBAAAoT,2DAAA;YAAAhV,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAASiT,GAAA,CAAAlD,WAAA,CAAAkD,GAAA,CAAA1K,UAAA,CAAuB;UAAA,EAAC;UAI/CpJ,EAHO,CAAAG,YAAA,EAAS,EACN,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,sBAewC;UAVtCD,EAFA,CAAAI,gBAAA,wBAAA6U,iEAAA3U,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAhK,QAAA,EAAAxJ,MAAA,MAAAwT,GAAA,CAAAhK,QAAA,GAAAxJ,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAAmB,yBAAA4U,kEAAA5U,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAAhU,EAAA,CAAAW,kBAAA,CAAAmT,GAAA,CAAAjK,KAAA,EAAAvJ,MAAA,MAAAwT,GAAA,CAAAjK,KAAA,GAAAvJ,MAAA;YAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;UAAA,EAEF;UACjBN,EAAA,CAAA4B,UAAA,oBAAAuT,6DAAA7U,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAAUiT,GAAA,CAAAvI,UAAA,CAAAjL,MAAA,CAAAuJ,KAAA,EAAAvJ,MAAA,CAAAkL,IAAA,CAAqC;UAAA,EAAC;UA6JhDxL,EAnJA,CAAA+C,UAAA,KAAAqS,gDAAA,2BAAgC,KAAAC,gDAAA,4BAgCU,KAAAC,oCAAA,iBAmH8E;UAWhItV,EAHM,CAAAG,YAAA,EAAU,EACN,EACF,EACF;UAENH,EAAA,CAAAqC,SAAA,4BAA6C;UAC7CrC,EAAA,CAAAC,cAAA,wBAAuE;UAAzDD,EAAA,CAAA4B,UAAA,8BAAA2T,4EAAAjV,MAAA;YAAAN,EAAA,CAAAO,aAAA,CAAAyT,GAAA;YAAA,OAAAhU,EAAA,CAAAa,WAAA,CAAoBiT,GAAA,CAAA5C,YAAA,CAAA5Q,MAAA,EAAAwT,GAAA,CAAAjK,KAAA,EAAAiK,GAAA,CAAAhK,QAAA,CAAmC;UAAA,EAAC;UAAC9J,EAAA,CAAAG,YAAA,EAAe;UAEtFH,EADA,CAAAqC,SAAA,iCAAuD,6BACR;;;UAzUrCrC,EAAA,CAAAc,SAAA,GAA6B;UAA7Bd,EAAA,CAAAqB,UAAA,YAAAyS,GAAA,CAAAxK,iBAAA,CAA6B;UAC7BtJ,EAAA,CAAAe,gBAAA,YAAA+S,GAAA,CAAAlT,QAAA,gBAAqC;UAMrCZ,EAAA,CAAAqB,UAAA,mBAAkB;UAMlBrB,EAAA,CAAAc,SAAA,GAA4B;UAA5Bd,EAAA,CAAAqB,UAAA,YAAAyS,GAAA,CAAAzK,gBAAA,CAA4B;UAC5BrJ,EAAA,CAAAe,gBAAA,YAAA+S,GAAA,CAAAlT,QAAA,eAAoC;UAMpCZ,EAAA,CAAAqB,UAAA,mBAAkB;UAMlBrB,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAqB,UAAA,YAAAyS,GAAA,CAAAtK,kBAAA,CAA8B;UAC9BxJ,EAAA,CAAAe,gBAAA,YAAA+S,GAAA,CAAAlT,QAAA,iBAAsC;UAMtCZ,EAAA,CAAAqB,UAAA,mBAAkB;UAI6ErB,EAAA,CAAAc,SAAA,GAAgC;UAAhCd,EAAA,CAAAe,gBAAA,YAAA+S,GAAA,CAAAlT,QAAA,WAAgC;UAE/GZ,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UAKhB/B,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UAKC/B,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UA4BhB/B,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UA4BjC/B,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UAeK/B,EAAA,CAAAc,SAAA,GAAgB;UAAhBd,EAAA,CAAAqB,UAAA,UAAAyS,GAAA,CAAA/R,SAAA,CAAgB;UAEhB/B,EAAA,CAAAc,SAAA,EAAe;UAAfd,EAAA,CAAAqB,UAAA,SAAAyS,GAAA,CAAA/R,SAAA,CAAe;UAaX/B,EAAA,CAAAc,SAAA,GAA6B;UAA7Bd,EAAA,CAAAwC,kBAAA,mBAAAsR,GAAA,CAAA/J,SAAA,KAA6B;UAI5D/J,EAAA,CAAAc,SAAA,GAAmC;UAAnCd,EAAA,CAAAqB,UAAA,aAAAyS,GAAA,CAAA7K,SAAA,CAAA6E,MAAA,OAAmC;UAIgB9N,EAAA,CAAAc,SAAA,GAA+C;UAClGd,EADmD,CAAAqB,UAAA,wBAAAyS,GAAA,CAAA1K,UAAA,CAAA0E,MAAA,OAA+C,aAAAgG,GAAA,CAAA1K,UAAA,CAAA0E,MAAA,OAC9D;UAQhD9N,EAAA,CAAAc,SAAA,EAAwB;UAAxBd,EAAA,CAAAqB,UAAA,yBAAwB;UACxBrB,EAAA,CAAAe,gBAAA,SAAA+S,GAAA,CAAAhK,QAAA,CAAmB;UACnB9J,EAAA,CAAAqB,UAAA,+BAA8B;UAC9BrB,EAAA,CAAAe,gBAAA,UAAA+S,GAAA,CAAAjK,KAAA,CAAiB;UAUjB7J,EARA,CAAAqB,UAAA,cAAAyS,GAAA,CAAA/J,SAAA,MAA4B,iBAAA+J,GAAA,CAAA/J,SAAA,CACF,UAAA+J,GAAA,CAAA7K,SAAA,CACP,uBAAAjJ,EAAA,CAAAwV,eAAA,KAAAC,GAAA,EACkB,oBAClB,cACN,oFACsE,eAAAzV,EAAA,CAAAwV,eAAA,KAAAE,GAAA,EAE9C;UAoJG1V,EAAA,CAAAc,SAAA,GAA8E;UAA9Ed,EAAA,CAAAqB,UAAA,SAAAyS,GAAA,CAAA7K,SAAA,KAAA0E,SAAA,IAAAmG,GAAA,CAAA7K,SAAA,cAAA6K,GAAA,CAAA7K,SAAA,kBAAA6K,GAAA,CAAA7K,SAAA,CAAA6E,MAAA,QAA8E;;;;;;;SD5SjHzF,wBAAwB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}