{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let LoanSchedulePaymentService = /*#__PURE__*/(() => {\n  class LoanSchedulePaymentService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = '/api/loan'; // 使用相对路径\n      this._allPaymentList = null;\n    }\n    get allPaymentList() {\n      return this._allPaymentList;\n    }\n    set allPaymentList(value) {\n      this._allPaymentList = value;\n    }\n    getMakePaymentInfo(data) {\n      return this.http.post(`${this.apiUrl}/schedule-payment/confirm/get-payment-info`, data);\n    }\n    static #_ = this.ɵfac = function LoanSchedulePaymentService_Factory(t) {\n      return new (t || LoanSchedulePaymentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoanSchedulePaymentService,\n      factory: LoanSchedulePaymentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LoanSchedulePaymentService;\n})();", "map": {"version": 3, "names": ["LoanSchedulePaymentService", "constructor", "http", "apiUrl", "_allPaymentList", "allPaymentList", "value", "getMakePaymentInfo", "data", "post", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-schedule-payment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\n\r\n// 定义支付数据接口\r\nexport interface PaymentData {\r\n  selectData: any[];\r\n  selectDealerFee: any[];\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class LoanSchedulePaymentService {\r\n  private apiUrl = '/api/loan';  // 使用相对路径\r\n  private _allPaymentList: PaymentData | null = null;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get allPaymentList(): PaymentData | null {\r\n    return this._allPaymentList;\r\n  }\r\n\r\n  set allPaymentList(value: PaymentData | null) {\r\n    this._allPaymentList = value;\r\n  }\r\n\r\n  getMakePaymentInfo(data: PaymentData): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/schedule-payment/confirm/get-payment-info`, data);\r\n  }\r\n\r\n  // ... existing code ...\r\n} "], "mappings": ";;AAaA,WAAaA,0BAA0B;EAAjC,MAAOA,0BAA0B;IAIrCC,YAAoBC,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;MAHhB,KAAAC,MAAM,GAAG,WAAW,CAAC,CAAE;MACvB,KAAAC,eAAe,GAAuB,IAAI;IAEX;IAEvC,IAAIC,cAAcA,CAAA;MAChB,OAAO,IAAI,CAACD,eAAe;IAC7B;IAEA,IAAIC,cAAcA,CAACC,KAAyB;MAC1C,IAAI,CAACF,eAAe,GAAGE,KAAK;IAC9B;IAEAC,kBAAkBA,CAACC,IAAiB;MAClC,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAG,IAAI,CAACN,MAAM,4CAA4C,EAAEK,IAAI,CAAC;IACzF;IAAC,QAAAE,CAAA,G;uBAhBUV,0BAA0B,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;IAAA,QAAAC,EAAA,G;aAA1Bf,0BAA0B;MAAAgB,OAAA,EAA1BhB,0BAA0B,CAAAiB,IAAA;MAAAC,UAAA,EAFzB;IAAM;;SAEPlB,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}