{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport moment from 'moment';\nimport { MessageService } from 'primeng/api';\nlet LoanProceedComponent = class LoanProceedComponent {\n  constructor(router, route, loanSchedulePaymentService, messageService) {\n    this.router = router;\n    this.route = route;\n    this.loanSchedulePaymentService = loanSchedulePaymentService;\n    this.messageService = messageService;\n    this.payoff = false;\n    this.formdata = [];\n    this.postageFee = [];\n    this.stateList = [];\n    this.holdTypeList = [];\n    this.uccProviderList = [];\n    this.holdSwitch = false;\n    this.isShowEstimation = false;\n    // Additional Payment dialog variables\n    this.showOtherAmountDialog = false;\n    this.selectedItem = null;\n    this.tempOtherAmount = 0;\n    // Calendar configuration\n    this.calendarConfig = {\n      showButtonBar: true,\n      monthNavigator: true,\n      yearNavigator: true,\n      dateFormat: 'mm/dd/yy',\n      showTime: false,\n      showIcon: false,\n      readonlyInput: true,\n      appendTo: 'body'\n    };\n    // Contact Dialog\n    this.showContactDialog = false;\n    this.selectedContactItem = null;\n    this.contactDialogMode = 'add';\n    this.editingContact = null;\n    this.tempContactNumber = 0;\n    // Contact Form\n    this.contactForm = {\n      contactReference: '',\n      firstName: '',\n      lastName: '',\n      addressLine1: '',\n      addressLine2: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      phone: '',\n      email: ''\n    };\n  }\n  ngOnInit() {\n    // 临时使用mock数据替代路由参数\n    const mockData = {\n      selectData: [{\n        \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\n        \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n        \"vDealerLevelDto\": null,\n        \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n        \"accountNumber\": 351764,\n        \"cost\": 2000,\n        \"currentCost\": 2000,\n        \"sold\": \"\",\n        \"soldOperationType\": null,\n        \"soldDisp\": \"\",\n        \"financeTag\": null,\n        \"assetId\": null,\n        \"titleStatus\": \"RE\",\n        \"titleStatusDisplay\": \"Trusted Title Received\",\n        \"interestDaily\": 0,\n        \"insuranceDaily\": 0,\n        \"interestPrice\": 0,\n        \"insurancePrice\": 0,\n        \"interestPriceTemp\": 0,\n        \"insurancePriceTemp\": 0,\n        \"isOnlyPrincial\": false,\n        \"onlyPrincialAmount\": 0,\n        \"isPastDue\": true,\n        \"year\": 2022,\n        \"fromDealerView\": null,\n        \"isScheduled\": false,\n        \"make\": \"BMW\",\n        \"model\": \"320Li\",\n        \"vin\": \"2GCEK19J245287165\",\n        \"titleReleaseDate\": null,\n        \"delayDays\": 0,\n        \"titleReleaseHoldDate\": null,\n        \"holdType\": null,\n        \"uccProviderDto\": null,\n        \"uccProviderId\": null,\n        \"titleNote\": null,\n        \"isHasTitleFile\": false,\n        \"fileManagementUrl\": null,\n        \"contactSwitch\": false,\n        \"contactDtoList\": null,\n        \"contactDto\": null,\n        \"newLocationDtoList\": null,\n        \"newLocationDto\": null,\n        \"newContactDtoList\": null,\n        \"newContactDto\": null,\n        \"vinLast6\": null,\n        \"maturituDate\": \"2023-02-28T00:00:00\",\n        \"nextDueDate\": \"2022-12-30T00:00:00\",\n        \"dueDate\": \"0001-01-01T00:00:00\",\n        \"isHold\": false,\n        \"isTrusted\": false,\n        \"payOff\": 90,\n        \"isPayOff\": true,\n        \"isPartialPayment\": false,\n        \"isCurtailment\": false,\n        \"isShowPrincipal\": false,\n        \"curtailment\": 0,\n        \"otherAmount\": 0,\n        \"otherAmountDisplay\": 0,\n        \"paidAmount\": 90,\n        \"scheduleDate\": \"2025-06-06T00:00:00\",\n        \"scheduleDateMessage\": null,\n        \"currentDate\": null,\n        \"scheduleDateEnd\": null,\n        \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\n        \"termNo\": 3,\n        \"dealerCode\": \"160625\",\n        \"dealerName\": null,\n        \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"dba\": \"MY160625\",\n        \"legalName\": \"MY160625\",\n        \"buttonName\": \"PayOff\",\n        \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n        \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n        \"principalInterest\": null,\n        \"effectiveDate\": \"2023-01-31T00:00:00\",\n        \"payOffPrincipal\": 0,\n        \"otherAmountLimit\": 0,\n        \"payOffTermFee\": 0,\n        \"payOffFeeItem\": 0,\n        \"payOffInterest\": 0,\n        \"payOffInsurance\": 0,\n        \"partPrincipal\": 0,\n        \"partTermFee\": 0,\n        \"partFeeItem\": 0,\n        \"partInterest\": 0,\n        \"partInsurance\": 0,\n        \"mailFee\": 0,\n        \"mailFeeName\": null,\n        \"principal\": 0,\n        \"titleName\": null,\n        \"titleContent\": null,\n        \"titleShoping\": null,\n        \"titlepostage\": null,\n        \"contactsId\": null,\n        \"newLocationId\": null,\n        \"newContactId\": null,\n        \"provincialMoney\": 0,\n        \"totalMoney\": 0,\n        \"tempTotalMoney\": 0,\n        \"subtotal\": 0,\n        \"chargeDay\": 0,\n        \"chargeDayMoneyInterest\": 0,\n        \"chargeDayMoneyInsurance\": 0,\n        \"stockId\": 214,\n        \"extraAmountMoney\": 0,\n        \"displayMail\": false,\n        \"vehicle\": \"287165-BMW 320Li 2022\",\n        \"feeAmountNoReserve\": 90,\n        \"reserveFee\": 0,\n        \"extraAmountList\": [{\n          \"feeId\": null,\n          \"feeName\": \"TERM_3\",\n          \"createDate\": null,\n          \"amount\": 50,\n          \"payFlag\": null\n        }, {\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 0,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }],\n        \"extraAmountListPayoff\": [{\n          \"feeId\": null,\n          \"feeName\": \"TERM_3\",\n          \"createDate\": null,\n          \"amount\": 50,\n          \"payFlag\": null\n        }, {\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 0,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }],\n        \"extraAmountListPartial\": [{\n          \"active\": true,\n          \"fixAmount\": 50,\n          \"tempAmount\": 50,\n          \"feeId\": null,\n          \"feeName\": \"TERM_3\",\n          \"createDate\": null,\n          \"amount\": 50,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 0,\n          \"tempAmount\": 0,\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 0,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 25,\n          \"tempAmount\": 25,\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 15,\n          \"tempAmount\": 15,\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }],\n        \"payInfoList\": null,\n        \"loanStatus\": \"L\",\n        \"isDuePayOff\": false,\n        \"isSoldPayOff\": false,\n        \"nsfCount\": 0,\n        \"waivedAmount\": 0,\n        \"extensionHide\": false,\n        \"paidPrinciple\": 0,\n        \"paidTermFee\": 0,\n        \"paidFee\": 0,\n        \"paidPcr\": 0,\n        \"paidInterest\": 0,\n        \"paidInsurance\": 0,\n        \"otherAmountDisable\": true\n      }, {\n        \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\n        \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n        \"vDealerLevelDto\": null,\n        \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n        \"accountNumber\": 352478,\n        \"cost\": 2000,\n        \"currentCost\": 2000,\n        \"sold\": \"\",\n        \"soldOperationType\": \"\",\n        \"soldDisp\": \"\",\n        \"financeTag\": \"\",\n        \"assetId\": null,\n        \"titleStatus\": \"PT\",\n        \"titleStatusDisplay\": \"Pending Release Trusted\",\n        \"interestDaily\": 0,\n        \"insuranceDaily\": 0,\n        \"interestPrice\": 0,\n        \"insurancePrice\": 0,\n        \"interestPriceTemp\": 0,\n        \"insurancePriceTemp\": 0,\n        \"isOnlyPrincial\": false,\n        \"onlyPrincialAmount\": 0,\n        \"isPastDue\": true,\n        \"year\": 2023,\n        \"fromDealerView\": null,\n        \"isScheduled\": false,\n        \"make\": \"BMW\",\n        \"model\": \"320Li\",\n        \"vin\": \"2GCEK19J245287243\",\n        \"titleReleaseDate\": null,\n        \"delayDays\": 0,\n        \"titleReleaseHoldDate\": null,\n        \"holdType\": null,\n        \"uccProviderDto\": null,\n        \"uccProviderId\": null,\n        \"titleNote\": null,\n        \"isHasTitleFile\": false,\n        \"fileManagementUrl\": null,\n        \"contactSwitch\": false,\n        \"contactDtoList\": null,\n        \"contactDto\": null,\n        \"newLocationDtoList\": null,\n        \"newLocationDto\": null,\n        \"newContactDtoList\": null,\n        \"newContactDto\": null,\n        \"vinLast6\": null,\n        \"maturituDate\": \"2023-02-28T00:00:00\",\n        \"nextDueDate\": \"2022-12-30T00:00:00\",\n        \"dueDate\": \"0001-01-01T00:00:00\",\n        \"isHold\": false,\n        \"isTrusted\": false,\n        \"payOff\": 2313,\n        \"isPayOff\": true,\n        \"isPartialPayment\": false,\n        \"isCurtailment\": false,\n        \"isShowPrincipal\": false,\n        \"curtailment\": 0,\n        \"otherAmount\": 0,\n        \"otherAmountDisplay\": 0,\n        \"paidAmount\": 2313,\n        \"scheduleDate\": \"2025-06-06T00:00:00\",\n        \"scheduleDateMessage\": null,\n        \"currentDate\": null,\n        \"scheduleDateEnd\": null,\n        \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\n        \"termNo\": 3,\n        \"dealerCode\": \"160625\",\n        \"dealerName\": null,\n        \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"dba\": \"MY160625\",\n        \"legalName\": \"MY160625\",\n        \"buttonName\": \"PayOff\",\n        \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n        \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n        \"principalInterest\": null,\n        \"effectiveDate\": \"2023-01-31T00:00:00\",\n        \"payOffPrincipal\": 2000,\n        \"otherAmountLimit\": 0,\n        \"payOffTermFee\": 0,\n        \"payOffFeeItem\": 0,\n        \"payOffInterest\": 0,\n        \"payOffInsurance\": 0,\n        \"partPrincipal\": 2000,\n        \"partTermFee\": 0,\n        \"partFeeItem\": 0,\n        \"partInterest\": 0,\n        \"partInsurance\": 0,\n        \"mailFee\": 0,\n        \"mailFeeName\": null,\n        \"principal\": 0,\n        \"titleName\": null,\n        \"titleContent\": null,\n        \"titleShoping\": null,\n        \"titlepostage\": null,\n        \"contactsId\": null,\n        \"newLocationId\": null,\n        \"newContactId\": null,\n        \"provincialMoney\": 0,\n        \"totalMoney\": 0,\n        \"tempTotalMoney\": 0,\n        \"subtotal\": 0,\n        \"chargeDay\": 0,\n        \"chargeDayMoneyInterest\": 0,\n        \"chargeDayMoneyInsurance\": 0,\n        \"stockId\": 312,\n        \"extraAmountMoney\": 0,\n        \"displayMail\": false,\n        \"vehicle\": \"287243-BMW 320Li 2023\",\n        \"feeAmountNoReserve\": 65,\n        \"reserveFee\": 248,\n        \"extraAmountList\": [{\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 2000,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }],\n        \"extraAmountListPayoff\": [{\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 2000,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"RESERVE\",\n          \"feeName\": \"Reserve\",\n          \"createDate\": null,\n          \"amount\": 248,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }, {\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }],\n        \"extraAmountListPartial\": [{\n          \"active\": true,\n          \"fixAmount\": 2000,\n          \"tempAmount\": 2000,\n          \"feeId\": null,\n          \"feeName\": \"Principal\",\n          \"createDate\": null,\n          \"amount\": 2000,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 25,\n          \"tempAmount\": 25,\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }, {\n          \"active\": false,\n          \"fixAmount\": 0,\n          \"tempAmount\": 0,\n          \"feeId\": \"RESERVE\",\n          \"feeName\": \"Reserve\",\n          \"createDate\": null,\n          \"amount\": 248,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 15,\n          \"tempAmount\": 15,\n          \"feeId\": \"ADMIN_FEE\",\n          \"feeName\": \"Admin Fee\",\n          \"createDate\": null,\n          \"amount\": 15,\n          \"payFlag\": null\n        }, {\n          \"active\": true,\n          \"fixAmount\": 25,\n          \"tempAmount\": 25,\n          \"feeId\": \"TRUSTED_TITLE_FEE\",\n          \"feeName\": \"Trusted Title Fee\",\n          \"createDate\": null,\n          \"amount\": 25,\n          \"payFlag\": null\n        }],\n        \"payInfoList\": null,\n        \"loanStatus\": \"L\",\n        \"isDuePayOff\": false,\n        \"isSoldPayOff\": false,\n        \"nsfCount\": 0,\n        \"waivedAmount\": 0,\n        \"extensionHide\": false,\n        \"paidPrinciple\": 0,\n        \"paidTermFee\": 0,\n        \"paidFee\": 0,\n        \"paidPcr\": 0,\n        \"paidInterest\": 0,\n        \"paidInsurance\": 0,\n        \"otherAmountDisable\": true\n      }],\n      \"selectDealerFee\": [{\n        \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\n        \"feeName\": \"Audit Fee\",\n        \"description\": \"Audit Fee\",\n        \"remainingAmount\": 100,\n        \"chargedOffRemainingAmount\": 0,\n        \"paidStatus\": \"UP\",\n        \"dba\": \"MY160625\",\n        \"name\": \"MY160625\",\n        \"dealerCode\": \"160625\",\n        \"scheduleDate\": \"0001-01-01T00:00:00\",\n        \"dueDate\": null,\n        \"createDate\": \"2023-06-20T09:29:05\",\n        \"postpaymentDealerFeeAmount\": 100,\n        \"removeDay\": true,\n        \"feeType\": \"P\"\n      }, {\n        \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n        \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\n        \"feeName\": \"OC Defense Fee_CO\",\n        \"description\": \"OC Defense Fee_CO\",\n        \"remainingAmount\": 123,\n        \"chargedOffRemainingAmount\": 0,\n        \"paidStatus\": \"UP\",\n        \"dba\": \"MY160625\",\n        \"name\": \"MY160625\",\n        \"dealerCode\": \"160625\",\n        \"scheduleDate\": \"0001-01-01T00:00:00\",\n        \"dueDate\": null,\n        \"createDate\": \"2025-05-29T02:44:35\",\n        \"postpaymentDealerFeeAmount\": 123,\n        \"removeDay\": true,\n        \"feeType\": \"P\"\n      }]\n    };\n    const data = JSON.stringify(mockData);\n    if (!data) {\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'No payment data found'\n      });\n      this.router.navigate(['/loan/schedule-payment']);\n      return;\n    }\n    const paymentData = {\n      selectData: JSON.parse(data).selectData,\n      selectDealerFee: JSON.parse(data).selectDealerFee\n    };\n    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\n      next: response => {\n        if (response.code === 200) {\n          this.formdata = response.data.results || [];\n          this.postageFee = response.data.postageFee || [];\n          this.stateList = response.data.stateList || [];\n          this.holdTypeList = response.data.releaseHoldTypeList || [];\n          this.uccProviderList = response.data.uccProviderList || [];\n          this.holdSwitch = response.data.releaseHoldSwitch || false;\n          this.formdata.forEach(dealer => {\n            if (dealer.dtoList) {\n              dealer.dtoList.forEach(dto => {\n                if (dto.isPayOff && !dto.isPartialPayment) {\n                  if (dto.contactSwitch) {\n                    dto.newLocationInfo = dto.newLocationDto;\n                    dto.newContactInfo = dto.newContactDto;\n                  } else {\n                    dto.contactInfo = dto.contactDtoList?.[0];\n                  }\n                  dto.mailFeeInfo = this.postageFee[0];\n                  const releaseDate = moment(new Date(dto.scheduleDate)).add(dto.delayDays, 'days').format('MM/DD/YYYY');\n                  dto.titleReleaseDate = releaseDate;\n                  if (this.holdSwitch && dto.isHold) {\n                    const holdTypes = this.holdTypeList.filter(c => c.value === 'H');\n                    if (holdTypes.length > 0) {\n                      dto.holdType = holdTypes[0];\n                    }\n                  }\n                }\n                // 转换日期格式\n                if (dto.scheduleDate) {\n                  dto.scheduleDate = moment(dto.scheduleDate).toDate();\n                }\n                if (dto.scheduleDateEnd) {\n                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\n                }\n                if (dto.currentDate) {\n                  dto.currentDate = moment(dto.currentDate).toDate();\n                }\n                if (dealer.paymentSource && dealer.bankAccountList?.length) {\n                  const matchingAccount = dealer.bankAccountList.find(account => account.bankAccountDtoId === dealer.paymentSource.bankAccountId);\n                  if (matchingAccount) {\n                    dealer.bankAccount = matchingAccount.bankAccountDtoId;\n                  }\n                }\n              });\n            }\n          });\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: response.message\n          });\n          this.router.navigate(['/loan/schedule-payment']);\n        }\n      },\n      error: error => {\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to get payment info'\n        });\n        console.error('Error getting payment info:', error);\n      }\n    });\n  }\n  showPayoff() {\n    this.payoff = !this.payoff;\n  }\n  getTotal() {\n    return this.formdata.reduce((total, dealer) => {\n      const dealerTotal = this.getSubTotal(dealer);\n      return total + dealerTotal;\n    }, 0);\n  }\n  getSubTotal(dealerInfo) {\n    let total = 0;\n    if (dealerInfo.dtoList) {\n      dealerInfo.dtoList.forEach(item => {\n        total += item.totalMoney || 0;\n        if (item.extraAmountList) {\n          item.extraAmountList.forEach(fee => {\n            total += fee.amount || 0;\n          });\n        }\n      });\n    }\n    if (dealerInfo.dealerLevelFeeList) {\n      dealerInfo.dealerLevelFeeList.forEach(fee => {\n        total += fee.remainingAmount || 0;\n      });\n    }\n    return total;\n  }\n  // 获取日期选择器配置\n  getDatePickerConfig(scheduleDate, scheduleDateEnd) {\n    const config = {\n      ...this.calendarConfig\n    };\n    if (scheduleDate) {\n      config.minDate = new Date(scheduleDate);\n      config.defaultDate = new Date(scheduleDate);\n    }\n    if (scheduleDateEnd) {\n      config.maxDate = new Date(scheduleDateEnd);\n    }\n    return config;\n  }\n  // 日期变更处理\n  scheduleDateChange(item) {\n    console.log('scheduleDateChange', item);\n    if (!item.scheduleDate) {\n      return;\n    }\n    // 计算发布日期\n    const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');\n    item.titleReleaseDate = releaseDate;\n    // 计算日期差\n    const now = moment(item.currentDate).format('MM/DD/YYYY');\n    const diffDays = moment(item.scheduleDate).diff(moment(item.currentDate), 'days');\n    // 显示预估标记\n    this.isShowEstimation = diffDays > 0;\n    // 计算利息和保险费用\n    const diffInterest = diffDays * item.interestDaily;\n    const diffInsurance = diffDays * item.insuranceDaily;\n    // 更新金额\n    item.interestPrice = item.interestPriceTemp + diffInterest;\n    item.insurancePrice = item.insurancePriceTemp + diffInsurance;\n    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\n  }\n  pay() {\n    let isContinue = true;\n    let isOnlyPrincipalSave = true;\n    for (const dealer of this.formdata) {\n      if (!dealer.bankAccount) {\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: `${dealer.dba}'s Bank Account is required`\n        });\n        isContinue = false;\n        break;\n      }\n      if (dealer.dtoList) {\n        for (const dto of dealer.dtoList) {\n          if (dto.onlyPrincialAmount === 0 && dto.isOnlyPrincial) {\n            this.messageService.add({\n              severity: 'warning',\n              summary: 'Warning',\n              detail: 'Only Principal amount should be greater than 0!'\n            });\n            isOnlyPrincipalSave = false;\n            break;\n          }\n          if (dto.isPayOff && !dto.isPartialPayment && dto.displayMail && !dto.isTrusted) {\n            if (this.holdSwitch && dto.isHold) {\n              if (!dto.holdType) {\n                this.messageService.add({\n                  severity: 'error',\n                  summary: 'Error',\n                  detail: 'Special Title Type is required'\n                });\n                isContinue = false;\n                break;\n              }\n              if (dto.holdType.value === 'T' && !dto.holdContactInfo) {\n                this.messageService.add({\n                  severity: 'error',\n                  summary: 'Error',\n                  detail: 'Shipping contact is required'\n                });\n                isContinue = false;\n                break;\n              }\n              if (dto.holdType.value === 'D' || dto.holdType.value === 'H') {\n                if (!dto.newContactInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping contact is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n                if (!dto.newLocationInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping location is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n              }\n            } else {\n              if (dto.contactSwitch) {\n                if (!dto.newContactInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping contact is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n                if (!dto.newLocationInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping location is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n              } else {\n                if (!dto.contactInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping contact is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    if (!isOnlyPrincipalSave || !isContinue) {\n      return;\n    }\n    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.messageService.add({\n            severity: 'success',\n            summary: 'Success',\n            detail: response.results\n          });\n          this.cancel();\n        } else if (response.status === 'warning') {\n          this.messageService.add({\n            severity: 'warn',\n            summary: 'Warning',\n            detail: response.results\n          });\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: response.results\n          });\n        }\n      },\n      error: error => {\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to process payment'\n        });\n      }\n    });\n  }\n  cancel() {\n    this.router.navigate(['/loan/schedule-payment']);\n  }\n  getPaymentLength() {\n    return this.formdata.reduce((total, dealer) => {\n      return total + (dealer.dtoList?.length || 0);\n    }, 0);\n  }\n  removeFee(dealer, feeItem) {\n    if (dealer && dealer.dealerLevelFeeList) {\n      const index = dealer.dealerLevelFeeList.findIndex(item => item.description === feeItem.description && item.remainingAmount === feeItem.remainingAmount);\n      if (index > -1) {\n        dealer.dealerLevelFeeList.splice(index, 1);\n        this.recalculateTotal(dealer);\n      }\n    }\n  }\n  recalculateTotal(dealer) {\n    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total, fee) => {\n      return total + (fee.remainingAmount || 0);\n    }, 0);\n    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\n  }\n  // 处理仅本金支付的金额变更\n  onlyPrincipalAmountChange(item) {\n    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\n      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n        this.messageService.add({\n          severity: 'warning',\n          summary: 'Warning',\n          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\n        });\n        item.onlyPrincialAmount = 0;\n      }\n    } else {\n      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n        this.messageService.add({\n          severity: 'warning',\n          summary: 'Warning',\n          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\n        });\n        item.onlyPrincialAmount = 0;\n      }\n    }\n  }\n  // 打开Other Amount输入对话框\n  inputOtherAmount(item) {\n    this.selectedItem = item;\n    this.tempOtherAmount = item.otherAmount || 0;\n    this.showOtherAmountDialog = true;\n  }\n  // 取消Other Amount输入\n  cancelOtherAmount() {\n    this.showOtherAmountDialog = false;\n    this.selectedItem = null;\n    this.tempOtherAmount = 0;\n  }\n  // 确认Other Amount输入\n  confirmOtherAmount() {\n    if (this.selectedItem) {\n      this.selectedItem.otherAmount = this.tempOtherAmount;\n      if (this.tempOtherAmount !== 0) {\n        this.selectedItem.buttonName = 'OtherAmount';\n      } else {\n        this.selectedItem.buttonName = 'Curtailment';\n      }\n    }\n    this.showOtherAmountDialog = false;\n    this.selectedItem = null;\n    this.tempOtherAmount = 0;\n  }\n  // 获取最小日期\n  getMinDate(item) {\n    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\n  }\n  // 获取最大日期\n  getMaxDate(item) {\n    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\n  }\n  // Add Contact Dialog\n  addContactDialog(item) {\n    this.selectedContactItem = item;\n    this.contactDialogMode = 'add';\n    this.contactForm = {\n      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\n      firstName: '',\n      lastName: '',\n      addressLine1: '',\n      addressLine2: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      phone: '',\n      email: ''\n    };\n    this.showContactDialog = true;\n  }\n  // Edit Contact Dialog\n  editContactDialog(contactInfo, item) {\n    this.selectedContactItem = item;\n    this.contactDialogMode = 'edit';\n    this.editingContact = contactInfo;\n    this.contactForm = {\n      contactReference: contactInfo.contactReference,\n      firstName: contactInfo.firstName,\n      lastName: contactInfo.lastName,\n      addressLine1: contactInfo.addressLine1,\n      addressLine2: contactInfo.addressLine2 || '',\n      city: contactInfo.city,\n      state: contactInfo.state,\n      zipCode: contactInfo.zipCode,\n      phone: contactInfo.phone,\n      email: contactInfo.email || ''\n    };\n    this.showContactDialog = true;\n  }\n  // Save Contact\n  saveContact() {\n    if (this.contactDialogMode === 'add') {\n      const newContact = {\n        ...this.contactForm\n      };\n      // Add to contact list\n      if (this.selectedContactItem.contactDtoList) {\n        this.selectedContactItem.contactDtoList.push(newContact);\n      } else {\n        this.selectedContactItem.contactDtoList = [newContact];\n      }\n      // Set as current contact\n      this.selectedContactItem.contactInfo = newContact;\n      // Update all loans for same dealer\n      this.formdata.forEach(dealer => {\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\n          dealer.dtoList?.forEach(loan => {\n            if (loan.contactDtoList) {\n              loan.contactDtoList.push({\n                ...newContact\n              });\n              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n                loan.isDisabledEdit = true;\n              }\n            }\n          });\n        }\n      });\n    } else {\n      // Edit mode\n      const updatedContact = {\n        ...this.contactForm\n      };\n      // Update in all places\n      this.formdata.forEach(dealer => {\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\n          dealer.dtoList?.forEach(loan => {\n            if (loan.contactDtoList) {\n              // Update in contact list\n              const index = loan.contactDtoList.findIndex(c => c.contactReference === this.editingContact.contactReference);\n              if (index > -1) {\n                loan.contactDtoList[index] = updatedContact;\n              }\n              // Update current selection if matches\n              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\n                loan.contactInfo = updatedContact;\n              }\n            }\n          });\n        }\n      });\n    }\n    this.showContactDialog = false;\n    this.selectedContactItem = null;\n    this.editingContact = null;\n  }\n  // Cancel Contact Dialog\n  cancelContactDialog() {\n    if (this.contactDialogMode === 'add') {\n      this.tempContactNumber--;\n    }\n    this.showContactDialog = false;\n    this.selectedContactItem = null;\n    this.editingContact = null;\n  }\n  // Handle shipping contact change\n  shippingContactChange(item) {\n    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n      item.isDisabledEdit = true;\n    } else {\n      item.isDisabledEdit = false;\n    }\n  }\n  // Remove payoff item\n  removePayoff(dealer, item) {\n    if (dealer.dtoList) {\n      const index = dealer.dtoList.findIndex(dto => dto === item);\n      if (index > -1) {\n        dealer.dtoList.splice(index, 1);\n      }\n    }\n  }\n  // View Title\n  viewTitle(item) {\n    if (item.fileManagementUrl) {\n      window.open(item.fileManagementUrl, '_blank');\n    }\n  }\n};\nLoanProceedComponent = __decorate([Component({\n  selector: 'app-loan-proceed',\n  templateUrl: './loan-proceed.component.html',\n  styleUrl: './loan-proceed.component.scss',\n  providers: [MessageService]\n})], LoanProceedComponent);\nexport { LoanProceedComponent };", "map": {"version": 3, "names": ["Component", "moment", "MessageService", "LoanProceedComponent", "constructor", "router", "route", "loanSchedulePaymentService", "messageService", "payoff", "formdata", "postageFee", "stateList", "holdTypeList", "uccProviderList", "holdSwitch", "isShowEstimation", "showOtherAmountDialog", "selectedItem", "tempOtherAmount", "calendarConfig", "showButtonBar", "monthNavigator", "yearNavigator", "dateFormat", "showTime", "showIcon", "readonlyInput", "appendTo", "showContactDialog", "selectedContactItem", "contactDialogMode", "editingContact", "tempContactNumber", "contactForm", "contactReference", "firstName", "lastName", "addressLine1", "addressLine2", "city", "state", "zipCode", "phone", "email", "ngOnInit", "mockData", "selectData", "data", "JSON", "stringify", "add", "severity", "summary", "detail", "navigate", "paymentData", "parse", "selectDealer<PERSON>ee", "getMakePaymentInfo", "subscribe", "next", "response", "code", "results", "releaseHoldTypeList", "releaseHoldSwitch", "for<PERSON>ach", "dealer", "dtoList", "dto", "is<PERSON>ayOff", "isPartialPayment", "contactSwitch", "newLocationInfo", "newLocationDto", "newContactInfo", "newContactDto", "contactInfo", "contactDtoList", "mailFeeInfo", "releaseDate", "Date", "scheduleDate", "delayDays", "format", "titleReleaseDate", "isHold", "holdTypes", "filter", "c", "value", "length", "holdType", "toDate", "scheduleDateEnd", "currentDate", "paymentSource", "bankAccountList", "matchingAccount", "find", "account", "bankAccountDtoId", "bankAccountId", "bankAccount", "message", "error", "console", "show<PERSON><PERSON>off", "getTotal", "reduce", "total", "dealerTotal", "getSubTotal", "dealerInfo", "item", "totalMoney", "extraAmountList", "fee", "amount", "dealerLevelFeeList", "remainingAmount", "getDatePickerConfig", "config", "minDate", "defaultDate", "maxDate", "scheduleDateChange", "log", "now", "diffDays", "diff", "diffInterest", "interestDaily", "diffInsurance", "insuranceDaily", "interestPrice", "interestPriceTemp", "insurancePrice", "insurancePriceTemp", "tempTotalMoney", "pay", "isContinue", "isOnlyPrincipalSave", "dba", "onlyPrincialAmount", "isOnlyPrincial", "displayMail", "isTrusted", "holdContactInfo", "editMakePayment", "status", "cancel", "getPaymentLength", "removeFee", "feeItem", "index", "findIndex", "description", "splice", "recalculateTotal", "dealerLevelFeeTotal", "onlyPrincipalAmountChange", "parseFloat", "payOffPrincipal", "inputOtherAmount", "otherAmount", "cancelOtherAmount", "confirmOtherAmount", "buttonName", "getMinDate", "getMaxDate", "addContactDialog", "editContactDialog", "saveContact", "newContact", "push", "dealerId", "loan", "includes", "isDisabledEdit", "updatedContact", "cancelContactDialog", "shippingContactChange", "<PERSON><PERSON><PERSON><PERSON>", "viewTitle", "fileManagementUrl", "window", "open", "__decorate", "selector", "templateUrl", "styleUrl", "providers"], "sources": ["D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-proceed\\loan-proceed.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport moment from 'moment';\r\nimport { LoanSchedulePaymentService } from '../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-loan-proceed',\r\n  templateUrl: './loan-proceed.component.html',\r\n  styleUrl: './loan-proceed.component.scss',\r\n  providers: [MessageService]\r\n})\r\nexport class LoanProceedComponent implements OnInit {\r\n  payoff: boolean = false;\r\n  formdata: any[] = [];\r\n  postageFee: any[] = [];\r\n  stateList: any[] = [];\r\n  holdTypeList: any[] = [];\r\n  uccProviderList: any[] = [];\r\n  holdSwitch: boolean = false;\r\n  isShowEstimation: boolean = false;\r\n\r\n  // Additional Payment dialog variables\r\n  showOtherAmountDialog: boolean = false;\r\n  selectedItem: any = null;\r\n  tempOtherAmount: number = 0;\r\n\r\n  // Calendar configuration\r\n  calendarConfig: any = {\r\n    showButtonBar: true,\r\n    monthNavigator: true,\r\n    yearNavigator: true,\r\n    dateFormat: 'mm/dd/yy',\r\n    showTime: false,\r\n    showIcon: false,\r\n    readonlyInput: true,\r\n    appendTo: 'body'\r\n  };\r\n\r\n  // Contact Dialog\r\n  showContactDialog: boolean = false;\r\n  selectedContactItem: any = null;\r\n  contactDialogMode: 'add' | 'edit' = 'add';\r\n  editingContact: any = null;\r\n  tempContactNumber: number = 0;\r\n\r\n  // Contact Form\r\n  contactForm = {\r\n    contactReference: '',\r\n    firstName: '',\r\n    lastName: '',\r\n    addressLine1: '',\r\n    addressLine2: '',\r\n    city: '',\r\n    state: '',\r\n    zipCode: '',\r\n    phone: '',\r\n    email: ''\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private loanSchedulePaymentService: LoanSchedulePaymentService,\r\n    private messageService: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // 临时使用mock数据替代路由参数\r\n    const mockData = {\r\n      selectData: [\r\n        {\r\n          \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\r\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\r\n          \"vDealerLevelDto\": null,\r\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\r\n          \"accountNumber\": 351764,\r\n          \"cost\": 2000,\r\n          \"currentCost\": 2000,\r\n          \"sold\": \"\",\r\n          \"soldOperationType\": null,\r\n          \"soldDisp\": \"\",\r\n          \"financeTag\": null,\r\n          \"assetId\": null,\r\n          \"titleStatus\": \"RE\",\r\n          \"titleStatusDisplay\": \"Trusted Title Received\",\r\n          \"interestDaily\": 0,\r\n          \"insuranceDaily\": 0,\r\n          \"interestPrice\": 0,\r\n          \"insurancePrice\": 0,\r\n          \"interestPriceTemp\": 0,\r\n          \"insurancePriceTemp\": 0,\r\n          \"isOnlyPrincial\": false,\r\n          \"onlyPrincialAmount\": 0,\r\n          \"isPastDue\": true,\r\n          \"year\": 2022,\r\n          \"fromDealerView\": null,\r\n          \"isScheduled\": false,\r\n          \"make\": \"BMW\",\r\n          \"model\": \"320Li\",\r\n          \"vin\": \"2GCEK19J245287165\",\r\n          \"titleReleaseDate\": null,\r\n          \"delayDays\": 0,\r\n          \"titleReleaseHoldDate\": null,\r\n          \"holdType\": null,\r\n          \"uccProviderDto\": null,\r\n          \"uccProviderId\": null,\r\n          \"titleNote\": null,\r\n          \"isHasTitleFile\": false,\r\n          \"fileManagementUrl\": null,\r\n          \"contactSwitch\": false,\r\n          \"contactDtoList\": null,\r\n          \"contactDto\": null,\r\n          \"newLocationDtoList\": null,\r\n          \"newLocationDto\": null,\r\n          \"newContactDtoList\": null,\r\n          \"newContactDto\": null,\r\n          \"vinLast6\": null,\r\n          \"maturituDate\": \"2023-02-28T00:00:00\",\r\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\r\n          \"dueDate\": \"0001-01-01T00:00:00\",\r\n          \"isHold\": false,\r\n          \"isTrusted\": false,\r\n          \"payOff\": 90,\r\n          \"isPayOff\": true,\r\n          \"isPartialPayment\": false,\r\n          \"isCurtailment\": false,\r\n          \"isShowPrincipal\": false,\r\n          \"curtailment\": 0,\r\n          \"otherAmount\": 0,\r\n          \"otherAmountDisplay\": 0,\r\n          \"paidAmount\": 90,\r\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\r\n          \"scheduleDateMessage\": null,\r\n          \"currentDate\": null,\r\n          \"scheduleDateEnd\": null,\r\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\r\n          \"termNo\": 3,\r\n          \"dealerCode\": \"160625\",\r\n          \"dealerName\": null,\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"dba\": \"MY160625\",\r\n          \"legalName\": \"MY160625\",\r\n          \"buttonName\": \"PayOff\",\r\n          \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\r\n          \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\r\n          \"principalInterest\": null,\r\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\r\n          \"payOffPrincipal\": 0,\r\n          \"otherAmountLimit\": 0,\r\n          \"payOffTermFee\": 0,\r\n          \"payOffFeeItem\": 0,\r\n          \"payOffInterest\": 0,\r\n          \"payOffInsurance\": 0,\r\n          \"partPrincipal\": 0,\r\n          \"partTermFee\": 0,\r\n          \"partFeeItem\": 0,\r\n          \"partInterest\": 0,\r\n          \"partInsurance\": 0,\r\n          \"mailFee\": 0,\r\n          \"mailFeeName\": null,\r\n          \"principal\": 0,\r\n          \"titleName\": null,\r\n          \"titleContent\": null,\r\n          \"titleShoping\": null,\r\n          \"titlepostage\": null,\r\n          \"contactsId\": null,\r\n          \"newLocationId\": null,\r\n          \"newContactId\": null,\r\n          \"provincialMoney\": 0,\r\n          \"totalMoney\": 0,\r\n          \"tempTotalMoney\": 0,\r\n          \"subtotal\": 0,\r\n          \"chargeDay\": 0,\r\n          \"chargeDayMoneyInterest\": 0,\r\n          \"chargeDayMoneyInsurance\": 0,\r\n          \"stockId\": 214,\r\n          \"extraAmountMoney\": 0,\r\n          \"displayMail\": false,\r\n          \"vehicle\": \"287165-BMW 320Li 2022\",\r\n          \"feeAmountNoReserve\": 90,\r\n          \"reserveFee\": 0,\r\n          \"extraAmountList\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPayoff\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPartial\": [\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 50,\r\n              \"tempAmount\": 50,\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 0,\r\n              \"tempAmount\": 0,\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 15,\r\n              \"tempAmount\": 15,\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"payInfoList\": null,\r\n          \"loanStatus\": \"L\",\r\n          \"isDuePayOff\": false,\r\n          \"isSoldPayOff\": false,\r\n          \"nsfCount\": 0,\r\n          \"waivedAmount\": 0,\r\n          \"extensionHide\": false,\r\n          \"paidPrinciple\": 0,\r\n          \"paidTermFee\": 0,\r\n          \"paidFee\": 0,\r\n          \"paidPcr\": 0,\r\n          \"paidInterest\": 0,\r\n          \"paidInsurance\": 0,\r\n          \"otherAmountDisable\": true\r\n        },\r\n        {\r\n          \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\r\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\r\n          \"vDealerLevelDto\": null,\r\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\r\n          \"accountNumber\": 352478,\r\n          \"cost\": 2000,\r\n          \"currentCost\": 2000,\r\n          \"sold\": \"\",\r\n          \"soldOperationType\": \"\",\r\n          \"soldDisp\": \"\",\r\n          \"financeTag\": \"\",\r\n          \"assetId\": null,\r\n          \"titleStatus\": \"PT\",\r\n          \"titleStatusDisplay\": \"Pending Release Trusted\",\r\n          \"interestDaily\": 0,\r\n          \"insuranceDaily\": 0,\r\n          \"interestPrice\": 0,\r\n          \"insurancePrice\": 0,\r\n          \"interestPriceTemp\": 0,\r\n          \"insurancePriceTemp\": 0,\r\n          \"isOnlyPrincial\": false,\r\n          \"onlyPrincialAmount\": 0,\r\n          \"isPastDue\": true,\r\n          \"year\": 2023,\r\n          \"fromDealerView\": null,\r\n          \"isScheduled\": false,\r\n          \"make\": \"BMW\",\r\n          \"model\": \"320Li\",\r\n          \"vin\": \"2GCEK19J245287243\",\r\n          \"titleReleaseDate\": null,\r\n          \"delayDays\": 0,\r\n          \"titleReleaseHoldDate\": null,\r\n          \"holdType\": null,\r\n          \"uccProviderDto\": null,\r\n          \"uccProviderId\": null,\r\n          \"titleNote\": null,\r\n          \"isHasTitleFile\": false,\r\n          \"fileManagementUrl\": null,\r\n          \"contactSwitch\": false,\r\n          \"contactDtoList\": null,\r\n          \"contactDto\": null,\r\n          \"newLocationDtoList\": null,\r\n          \"newLocationDto\": null,\r\n          \"newContactDtoList\": null,\r\n          \"newContactDto\": null,\r\n          \"vinLast6\": null,\r\n          \"maturituDate\": \"2023-02-28T00:00:00\",\r\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\r\n          \"dueDate\": \"0001-01-01T00:00:00\",\r\n          \"isHold\": false,\r\n          \"isTrusted\": false,\r\n          \"payOff\": 2313,\r\n          \"isPayOff\": true,\r\n          \"isPartialPayment\": false,\r\n          \"isCurtailment\": false,\r\n          \"isShowPrincipal\": false,\r\n          \"curtailment\": 0,\r\n          \"otherAmount\": 0,\r\n          \"otherAmountDisplay\": 0,\r\n          \"paidAmount\": 2313,\r\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\r\n          \"scheduleDateMessage\": null,\r\n          \"currentDate\": null,\r\n          \"scheduleDateEnd\": null,\r\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\r\n          \"termNo\": 3,\r\n          \"dealerCode\": \"160625\",\r\n          \"dealerName\": null,\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"dba\": \"MY160625\",\r\n          \"legalName\": \"MY160625\",\r\n          \"buttonName\": \"PayOff\",\r\n          \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\r\n          \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\r\n          \"principalInterest\": null,\r\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\r\n          \"payOffPrincipal\": 2000,\r\n          \"otherAmountLimit\": 0,\r\n          \"payOffTermFee\": 0,\r\n          \"payOffFeeItem\": 0,\r\n          \"payOffInterest\": 0,\r\n          \"payOffInsurance\": 0,\r\n          \"partPrincipal\": 2000,\r\n          \"partTermFee\": 0,\r\n          \"partFeeItem\": 0,\r\n          \"partInterest\": 0,\r\n          \"partInsurance\": 0,\r\n          \"mailFee\": 0,\r\n          \"mailFeeName\": null,\r\n          \"principal\": 0,\r\n          \"titleName\": null,\r\n          \"titleContent\": null,\r\n          \"titleShoping\": null,\r\n          \"titlepostage\": null,\r\n          \"contactsId\": null,\r\n          \"newLocationId\": null,\r\n          \"newContactId\": null,\r\n          \"provincialMoney\": 0,\r\n          \"totalMoney\": 0,\r\n          \"tempTotalMoney\": 0,\r\n          \"subtotal\": 0,\r\n          \"chargeDay\": 0,\r\n          \"chargeDayMoneyInterest\": 0,\r\n          \"chargeDayMoneyInsurance\": 0,\r\n          \"stockId\": 312,\r\n          \"extraAmountMoney\": 0,\r\n          \"displayMail\": false,\r\n          \"vehicle\": \"287243-BMW 320Li 2023\",\r\n          \"feeAmountNoReserve\": 65,\r\n          \"reserveFee\": 248,\r\n          \"extraAmountList\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPayoff\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"RESERVE\",\r\n              \"feeName\": \"Reserve\",\r\n              \"createDate\": null,\r\n              \"amount\": 248,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPartial\": [\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 2000,\r\n              \"tempAmount\": 2000,\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": false,\r\n              \"fixAmount\": 0,\r\n              \"tempAmount\": 0,\r\n              \"feeId\": \"RESERVE\",\r\n              \"feeName\": \"Reserve\",\r\n              \"createDate\": null,\r\n              \"amount\": 248,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 15,\r\n              \"tempAmount\": 15,\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"payInfoList\": null,\r\n          \"loanStatus\": \"L\",\r\n          \"isDuePayOff\": false,\r\n          \"isSoldPayOff\": false,\r\n          \"nsfCount\": 0,\r\n          \"waivedAmount\": 0,\r\n          \"extensionHide\": false,\r\n          \"paidPrinciple\": 0,\r\n          \"paidTermFee\": 0,\r\n          \"paidFee\": 0,\r\n          \"paidPcr\": 0,\r\n          \"paidInterest\": 0,\r\n          \"paidInsurance\": 0,\r\n          \"otherAmountDisable\": true\r\n        }\r\n      ],\r\n      \"selectDealerFee\": [\r\n        {\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\r\n          \"feeName\": \"Audit Fee\",\r\n          \"description\": \"Audit Fee\",\r\n          \"remainingAmount\": 100,\r\n          \"chargedOffRemainingAmount\": 0,\r\n          \"paidStatus\": \"UP\",\r\n          \"dba\": \"MY160625\",\r\n          \"name\": \"MY160625\",\r\n          \"dealerCode\": \"160625\",\r\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\r\n          \"dueDate\": null,\r\n          \"createDate\": \"2023-06-20T09:29:05\",\r\n          \"postpaymentDealerFeeAmount\": 100,\r\n          \"removeDay\": true,\r\n          \"feeType\": \"P\"\r\n        },\r\n        {\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\r\n          \"feeName\": \"OC Defense Fee_CO\",\r\n          \"description\": \"OC Defense Fee_CO\",\r\n          \"remainingAmount\": 123,\r\n          \"chargedOffRemainingAmount\": 0,\r\n          \"paidStatus\": \"UP\",\r\n          \"dba\": \"MY160625\",\r\n          \"name\": \"MY160625\",\r\n          \"dealerCode\": \"160625\",\r\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\r\n          \"dueDate\": null,\r\n          \"createDate\": \"2025-05-29T02:44:35\",\r\n          \"postpaymentDealerFeeAmount\": 123,\r\n          \"removeDay\": true,\r\n          \"feeType\": \"P\"\r\n        }\r\n      ]\r\n    };\r\n\r\n    const data = JSON.stringify(mockData);\r\n\r\n    if (!data) {\r\n      this.messageService.add({severity:'error', summary: 'Error', detail: 'No payment data found'});\r\n      this.router.navigate(['/loan/schedule-payment']);\r\n      return;\r\n    }\r\n\r\n    const paymentData = {\r\n      selectData: JSON.parse(data).selectData,\r\n      selectDealerFee: JSON.parse(data).selectDealerFee\r\n    };\r\n\r\n    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\r\n      next: (response: any) => {\r\n        if (response.code === 200) {\r\n          this.formdata = response.data.results || [];\r\n          this.postageFee = response.data.postageFee || [];\r\n          this.stateList = response.data.stateList || [];\r\n          this.holdTypeList = response.data.releaseHoldTypeList || [];\r\n          this.uccProviderList = response.data.uccProviderList || [];\r\n          this.holdSwitch = response.data.releaseHoldSwitch || false;\r\n\r\n          this.formdata.forEach((dealer: any) => {\r\n            if (dealer.dtoList) {\r\n              dealer.dtoList.forEach((dto: any) => {\r\n                if (dto.isPayOff && !dto.isPartialPayment) {\r\n                  if (dto.contactSwitch) {\r\n                    dto.newLocationInfo = dto.newLocationDto;\r\n                    dto.newContactInfo = dto.newContactDto;\r\n                  } else {\r\n                    dto.contactInfo = dto.contactDtoList?.[0];\r\n                  }\r\n                  dto.mailFeeInfo = this.postageFee[0];\r\n\r\n                  const releaseDate = moment(new Date(dto.scheduleDate))\r\n                    .add(dto.delayDays, 'days')\r\n                    .format('MM/DD/YYYY');\r\n                  dto.titleReleaseDate = releaseDate;\r\n\r\n                  if (this.holdSwitch && dto.isHold) {\r\n                    const holdTypes = this.holdTypeList.filter((c: any) => c.value === 'H');\r\n                    if (holdTypes.length > 0) {\r\n                      dto.holdType = holdTypes[0];\r\n                    }\r\n                  }\r\n                }\r\n\r\n                // 转换日期格式\r\n                if(dto.scheduleDate) {\r\n                  dto.scheduleDate = moment(dto.scheduleDate).toDate();\r\n                }\r\n                if(dto.scheduleDateEnd) {\r\n                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\r\n                }\r\n                if(dto.currentDate) {\r\n                  dto.currentDate = moment(dto.currentDate).toDate();\r\n                }\r\n\r\n                if (dealer.paymentSource && dealer.bankAccountList?.length) {\r\n                  const matchingAccount = dealer.bankAccountList.find((account: any) =>\r\n                    account.bankAccountDtoId === dealer.paymentSource.bankAccountId\r\n                  );\r\n                  if (matchingAccount) {\r\n                    dealer.bankAccount = matchingAccount.bankAccountDtoId;\r\n                  }\r\n                }\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          this.messageService.add({severity:'error', summary: 'Error', detail: response.message});\r\n          this.router.navigate(['/loan/schedule-payment']);\r\n        }\r\n      },\r\n      error: (error: Error) => {\r\n        this.messageService.add({severity:'error', summary: 'Error', detail: 'Failed to get payment info'});\r\n        console.error('Error getting payment info:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  showPayoff() {\r\n    this.payoff = !this.payoff;\r\n  }\r\n\r\n  getTotal(): number {\r\n    return this.formdata.reduce((total, dealer) => {\r\n      const dealerTotal = this.getSubTotal(dealer);\r\n      return total + dealerTotal;\r\n    }, 0);\r\n  }\r\n\r\n  getSubTotal(dealerInfo: any): number {\r\n    let total = 0;\r\n    if (dealerInfo.dtoList) {\r\n      dealerInfo.dtoList.forEach((item: any) => {\r\n        total += item.totalMoney || 0;\r\n        if (item.extraAmountList) {\r\n          item.extraAmountList.forEach((fee: any) => {\r\n            total += fee.amount || 0;\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    if (dealerInfo.dealerLevelFeeList) {\r\n      dealerInfo.dealerLevelFeeList.forEach((fee: any) => {\r\n        total += fee.remainingAmount || 0;\r\n      });\r\n    }\r\n\r\n    return total;\r\n  }\r\n\r\n  // 获取日期选择器配置\r\n  getDatePickerConfig(scheduleDate: string, scheduleDateEnd: string): any {\r\n    const config = { ...this.calendarConfig };\r\n    if (scheduleDate) {\r\n      config.minDate = new Date(scheduleDate);\r\n      config.defaultDate = new Date(scheduleDate);\r\n    }\r\n    if (scheduleDateEnd) {\r\n      config.maxDate = new Date(scheduleDateEnd);\r\n    }\r\n    return config;\r\n  }\r\n\r\n  // 日期变更处理\r\n  scheduleDateChange(item: any) {\r\n    console.log('scheduleDateChange', item);\r\n    if (!item.scheduleDate) {\r\n      return;\r\n    }\r\n\r\n    // 计算发布日期\r\n    const releaseDate = moment(item.scheduleDate)\r\n      .add(item.delayDays, 'days')\r\n      .format('MM/DD/YYYY');\r\n    item.titleReleaseDate = releaseDate;\r\n\r\n    // 计算日期差\r\n    const now = moment(item.currentDate).format('MM/DD/YYYY');\r\n    const diffDays = moment(item.scheduleDate)\r\n      .diff(moment(item.currentDate), 'days');\r\n\r\n    // 显示预估标记\r\n    this.isShowEstimation = diffDays > 0;\r\n\r\n    // 计算利息和保险费用\r\n    const diffInterest = diffDays * item.interestDaily;\r\n    const diffInsurance = diffDays * item.insuranceDaily;\r\n\r\n    // 更新金额\r\n    item.interestPrice = item.interestPriceTemp + diffInterest;\r\n    item.insurancePrice = item.insurancePriceTemp + diffInsurance;\r\n    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\r\n  }\r\n\r\n  pay() {\r\n    let isContinue = true;\r\n    let isOnlyPrincipalSave = true;\r\n\r\n    for (const dealer of this.formdata) {\r\n      if (!dealer.bankAccount) {\r\n        this.messageService.add({\r\n          severity:'error',\r\n          summary: 'Error',\r\n          detail: `${dealer.dba}'s Bank Account is required`\r\n        });\r\n        isContinue = false;\r\n        break;\r\n      }\r\n\r\n      if (dealer.dtoList) {\r\n        for (const dto of dealer.dtoList) {\r\n          if (dto.onlyPrincialAmount === 0 && dto.isOnlyPrincial) {\r\n            this.messageService.add({\r\n              severity:'warning',\r\n              summary: 'Warning',\r\n              detail: 'Only Principal amount should be greater than 0!'\r\n            });\r\n            isOnlyPrincipalSave = false;\r\n            break;\r\n          }\r\n\r\n          if (dto.isPayOff && !dto.isPartialPayment && dto.displayMail && !dto.isTrusted) {\r\n            if (this.holdSwitch && dto.isHold) {\r\n              if (!dto.holdType) {\r\n                this.messageService.add({\r\n                  severity:'error',\r\n                  summary: 'Error',\r\n                  detail: 'Special Title Type is required'\r\n                });\r\n                isContinue = false;\r\n                break;\r\n              }\r\n\r\n              if (dto.holdType.value === 'T' && !dto.holdContactInfo) {\r\n                this.messageService.add({\r\n                  severity:'error',\r\n                  summary: 'Error',\r\n                  detail: 'Shipping contact is required'\r\n                });\r\n                isContinue = false;\r\n                break;\r\n              }\r\n\r\n              if ((dto.holdType.value === 'D' || dto.holdType.value === 'H')) {\r\n                if (!dto.newContactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n                if (!dto.newLocationInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping location is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              }\r\n            } else {\r\n              if (dto.contactSwitch) {\r\n                if (!dto.newContactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n                if (!dto.newLocationInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping location is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              } else {\r\n                if (!dto.contactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (!isOnlyPrincipalSave || !isContinue) {\r\n      return;\r\n    }\r\n\r\n    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\r\n      next: (response: any) => {\r\n        if (response.status === 'success') {\r\n          this.messageService.add({\r\n            severity:'success',\r\n            summary: 'Success',\r\n            detail: response.results\r\n          });\r\n          this.cancel();\r\n        } else if (response.status === 'warning') {\r\n          this.messageService.add({\r\n            severity:'warn',\r\n            summary: 'Warning',\r\n            detail: response.results\r\n          });\r\n        } else {\r\n          this.messageService.add({\r\n            severity:'error',\r\n            summary: 'Error',\r\n            detail: response.results\r\n          });\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        this.messageService.add({\r\n          severity:'error',\r\n          summary: 'Error',\r\n          detail: 'Failed to process payment'\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate(['/loan/schedule-payment']);\r\n  }\r\n\r\n  getPaymentLength(): number {\r\n    return this.formdata.reduce((total, dealer) => {\r\n      return total + (dealer.dtoList?.length || 0);\r\n    }, 0);\r\n  }\r\n\r\n  removeFee(dealer: any, feeItem: any) {\r\n    if (dealer && dealer.dealerLevelFeeList) {\r\n      const index = dealer.dealerLevelFeeList.findIndex((item: any) =>\r\n        item.description === feeItem.description &&\r\n        item.remainingAmount === feeItem.remainingAmount\r\n      );\r\n      if (index > -1) {\r\n        dealer.dealerLevelFeeList.splice(index, 1);\r\n        this.recalculateTotal(dealer);\r\n      }\r\n    }\r\n  }\r\n\r\n  private recalculateTotal(dealer: any) {\r\n    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total: number, fee: any) => {\r\n      return total + (fee.remainingAmount || 0);\r\n    }, 0);\r\n\r\n    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\r\n  }\r\n\r\n  // 处理仅本金支付的金额变更\r\n  onlyPrincipalAmountChange(item: any) {\r\n    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\r\n      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\r\n        this.messageService.add({\r\n          severity:'warning',\r\n          summary: 'Warning',\r\n          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\r\n        });\r\n        item.onlyPrincialAmount = 0;\r\n      }\r\n    } else {\r\n      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\r\n        this.messageService.add({\r\n          severity:'warning',\r\n          summary: 'Warning',\r\n          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\r\n        });\r\n        item.onlyPrincialAmount = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 打开Other Amount输入对话框\r\n  inputOtherAmount(item: any) {\r\n    this.selectedItem = item;\r\n    this.tempOtherAmount = item.otherAmount || 0;\r\n    this.showOtherAmountDialog = true;\r\n  }\r\n\r\n  // 取消Other Amount输入\r\n  cancelOtherAmount() {\r\n    this.showOtherAmountDialog = false;\r\n    this.selectedItem = null;\r\n    this.tempOtherAmount = 0;\r\n  }\r\n\r\n  // 确认Other Amount输入\r\n  confirmOtherAmount() {\r\n    if (this.selectedItem) {\r\n      this.selectedItem.otherAmount = this.tempOtherAmount;\r\n      if (this.tempOtherAmount !== 0) {\r\n        this.selectedItem.buttonName = 'OtherAmount';\r\n      } else {\r\n        this.selectedItem.buttonName = 'Curtailment';\r\n      }\r\n    }\r\n    this.showOtherAmountDialog = false;\r\n    this.selectedItem = null;\r\n    this.tempOtherAmount = 0;\r\n  }\r\n\r\n  // 获取最小日期\r\n  getMinDate(item: any): Date | null {\r\n    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\r\n  }\r\n\r\n  // 获取最大日期\r\n  getMaxDate(item: any): Date | null {\r\n    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\r\n  }\r\n\r\n  // Add Contact Dialog\r\n  addContactDialog(item: any) {\r\n    this.selectedContactItem = item;\r\n    this.contactDialogMode = 'add';\r\n    this.contactForm = {\r\n      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\r\n      firstName: '',\r\n      lastName: '',\r\n      addressLine1: '',\r\n      addressLine2: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      phone: '',\r\n      email: ''\r\n    };\r\n    this.showContactDialog = true;\r\n  }\r\n\r\n  // Edit Contact Dialog\r\n  editContactDialog(contactInfo: any, item: any) {\r\n    this.selectedContactItem = item;\r\n    this.contactDialogMode = 'edit';\r\n    this.editingContact = contactInfo;\r\n    this.contactForm = {\r\n      contactReference: contactInfo.contactReference,\r\n      firstName: contactInfo.firstName,\r\n      lastName: contactInfo.lastName,\r\n      addressLine1: contactInfo.addressLine1,\r\n      addressLine2: contactInfo.addressLine2 || '',\r\n      city: contactInfo.city,\r\n      state: contactInfo.state,\r\n      zipCode: contactInfo.zipCode,\r\n      phone: contactInfo.phone,\r\n      email: contactInfo.email || ''\r\n    };\r\n    this.showContactDialog = true;\r\n  }\r\n\r\n  // Save Contact\r\n  saveContact() {\r\n    if (this.contactDialogMode === 'add') {\r\n      const newContact = { ...this.contactForm };\r\n      \r\n      // Add to contact list\r\n      if (this.selectedContactItem.contactDtoList) {\r\n        this.selectedContactItem.contactDtoList.push(newContact);\r\n      } else {\r\n        this.selectedContactItem.contactDtoList = [newContact];\r\n      }\r\n\r\n      // Set as current contact\r\n      this.selectedContactItem.contactInfo = newContact;\r\n\r\n      // Update all loans for same dealer\r\n      this.formdata.forEach((dealer: any) => {\r\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\r\n          dealer.dtoList?.forEach((loan: any) => {\r\n            if (loan.contactDtoList) {\r\n              loan.contactDtoList.push({ ...newContact });\r\n              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\r\n                loan.isDisabledEdit = true;\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // Edit mode\r\n      const updatedContact = { ...this.contactForm };\r\n      \r\n      // Update in all places\r\n      this.formdata.forEach((dealer: any) => {\r\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\r\n          dealer.dtoList?.forEach((loan: any) => {\r\n            if (loan.contactDtoList) {\r\n              // Update in contact list\r\n              const index = loan.contactDtoList.findIndex(\r\n                (c: any) => c.contactReference === this.editingContact.contactReference\r\n              );\r\n              if (index > -1) {\r\n                loan.contactDtoList[index] = updatedContact;\r\n              }\r\n\r\n              // Update current selection if matches\r\n              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\r\n                loan.contactInfo = updatedContact;\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    this.showContactDialog = false;\r\n    this.selectedContactItem = null;\r\n    this.editingContact = null;\r\n  }\r\n\r\n  // Cancel Contact Dialog\r\n  cancelContactDialog() {\r\n    if (this.contactDialogMode === 'add') {\r\n      this.tempContactNumber--;\r\n    }\r\n    this.showContactDialog = false;\r\n    this.selectedContactItem = null;\r\n    this.editingContact = null;\r\n  }\r\n\r\n  // Handle shipping contact change\r\n  shippingContactChange(item: any) {\r\n    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\r\n      item.isDisabledEdit = true;\r\n    } else {\r\n      item.isDisabledEdit = false;\r\n    }\r\n  }\r\n\r\n  // Remove payoff item\r\n  removePayoff(dealer: any, item: any) {\r\n    if (dealer.dtoList) {\r\n      const index = dealer.dtoList.findIndex((dto: any) => dto === item);\r\n      if (index > -1) {\r\n        dealer.dtoList.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  // View Title\r\n  viewTitle(item: any) {\r\n    if (item.fileManagementUrl) {\r\n      window.open(item.fileManagementUrl, '_blank');\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAEjD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAASC,cAAc,QAAQ,aAAa;AAQrC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAgD/BC,YACUC,MAAc,EACdC,KAAqB,EACrBC,0BAAsD,EACtDC,cAA8B;IAH9B,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAC,cAAc,GAAdA,cAAc;IAnDxB,KAAAC,MAAM,GAAY,KAAK;IACvB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAY,KAAK;IAEjC;IACA,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,eAAe,GAAW,CAAC;IAE3B;IACA,KAAAC,cAAc,GAAQ;MACpBC,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE;KACX;IAED;IACA,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,iBAAiB,GAAmB,KAAK;IACzC,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,iBAAiB,GAAW,CAAC;IAE7B;IACA,KAAAC,WAAW,GAAG;MACZC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;KACR;EAOE;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,QAAQ,GAAG;MACfC,UAAU,EAAE,CACV;QACE,QAAQ,EAAE,sCAAsC;QAChD,cAAc,EAAE,sCAAsC;QACtD,iBAAiB,EAAE,IAAI;QACvB,oBAAoB,EAAE,sCAAsC;QAC5D,eAAe,EAAE,MAAM;QACvB,MAAM,EAAE,IAAI;QACZ,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,EAAE;QACV,mBAAmB,EAAE,IAAI;QACzB,UAAU,EAAE,EAAE;QACd,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,IAAI;QACnB,oBAAoB,EAAE,wBAAwB;QAC9C,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,mBAAmB,EAAE,CAAC;QACtB,oBAAoB,EAAE,CAAC;QACvB,gBAAgB,EAAE,KAAK;QACvB,oBAAoB,EAAE,CAAC;QACvB,WAAW,EAAE,IAAI;QACjB,MAAM,EAAE,IAAI;QACZ,gBAAgB,EAAE,IAAI;QACtB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,mBAAmB;QAC1B,kBAAkB,EAAE,IAAI;QACxB,WAAW,EAAE,CAAC;QACd,sBAAsB,EAAE,IAAI;QAC5B,UAAU,EAAE,IAAI;QAChB,gBAAgB,EAAE,IAAI;QACtB,eAAe,EAAE,IAAI;QACrB,WAAW,EAAE,IAAI;QACjB,gBAAgB,EAAE,KAAK;QACvB,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,IAAI;QACtB,YAAY,EAAE,IAAI;QAClB,oBAAoB,EAAE,IAAI;QAC1B,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,IAAI;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,qBAAqB;QACrC,aAAa,EAAE,qBAAqB;QACpC,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,IAAI;QAChB,kBAAkB,EAAE,KAAK;QACzB,eAAe,EAAE,KAAK;QACtB,iBAAiB,EAAE,KAAK;QACxB,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,CAAC;QAChB,oBAAoB,EAAE,CAAC;QACvB,YAAY,EAAE,EAAE;QAChB,cAAc,EAAE,qBAAqB;QACrC,qBAAqB,EAAE,IAAI;QAC3B,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,IAAI;QACvB,UAAU,EAAE,sCAAsC;QAClD,QAAQ,EAAE,sCAAsC;QAChD,QAAQ,EAAE,CAAC;QACX,YAAY,EAAE,QAAQ;QACtB,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,sCAAsC;QAClD,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,UAAU;QACvB,YAAY,EAAE,QAAQ;QACtB,eAAe,EAAE,uCAAuC;QACxD,kBAAkB,EAAE,uCAAuC;QAC3D,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,qBAAqB;QACtC,iBAAiB,EAAE,CAAC;QACpB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,iBAAiB,EAAE,CAAC;QACpB,eAAe,EAAE,CAAC;QAClB,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,CAAC;QAChB,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,CAAC;QAClB,SAAS,EAAE,CAAC;QACZ,aAAa,EAAE,IAAI;QACnB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,IAAI;QACjB,cAAc,EAAE,IAAI;QACpB,cAAc,EAAE,IAAI;QACpB,cAAc,EAAE,IAAI;QACpB,YAAY,EAAE,IAAI;QAClB,eAAe,EAAE,IAAI;QACrB,cAAc,EAAE,IAAI;QACpB,iBAAiB,EAAE,CAAC;QACpB,YAAY,EAAE,CAAC;QACf,gBAAgB,EAAE,CAAC;QACnB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,wBAAwB,EAAE,CAAC;QAC3B,yBAAyB,EAAE,CAAC;QAC5B,SAAS,EAAE,GAAG;QACd,kBAAkB,EAAE,CAAC;QACrB,aAAa,EAAE,KAAK;QACpB,SAAS,EAAE,uBAAuB;QAClC,oBAAoB,EAAE,EAAE;QACxB,YAAY,EAAE,CAAC;QACf,iBAAiB,EAAE,CACjB;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,QAAQ;UACnB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,CAAC;UACX,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,uBAAuB,EAAE,CACvB;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,QAAQ;UACnB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,CAAC;UACX,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,wBAAwB,EAAE,CACxB;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,QAAQ;UACnB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,CAAC;UACd,YAAY,EAAE,CAAC;UACf,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,CAAC;UACX,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,GAAG;QACjB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,UAAU,EAAE,CAAC;QACb,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,KAAK;QACtB,eAAe,EAAE,CAAC;QAClB,aAAa,EAAE,CAAC;QAChB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,CAAC;QACZ,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,CAAC;QAClB,oBAAoB,EAAE;OACvB,EACD;QACE,QAAQ,EAAE,sCAAsC;QAChD,cAAc,EAAE,sCAAsC;QACtD,iBAAiB,EAAE,IAAI;QACvB,oBAAoB,EAAE,sCAAsC;QAC5D,eAAe,EAAE,MAAM;QACvB,MAAM,EAAE,IAAI;QACZ,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,EAAE;QACV,mBAAmB,EAAE,EAAE;QACvB,UAAU,EAAE,EAAE;QACd,YAAY,EAAE,EAAE;QAChB,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,IAAI;QACnB,oBAAoB,EAAE,yBAAyB;QAC/C,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,mBAAmB,EAAE,CAAC;QACtB,oBAAoB,EAAE,CAAC;QACvB,gBAAgB,EAAE,KAAK;QACvB,oBAAoB,EAAE,CAAC;QACvB,WAAW,EAAE,IAAI;QACjB,MAAM,EAAE,IAAI;QACZ,gBAAgB,EAAE,IAAI;QACtB,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,mBAAmB;QAC1B,kBAAkB,EAAE,IAAI;QACxB,WAAW,EAAE,CAAC;QACd,sBAAsB,EAAE,IAAI;QAC5B,UAAU,EAAE,IAAI;QAChB,gBAAgB,EAAE,IAAI;QACtB,eAAe,EAAE,IAAI;QACrB,WAAW,EAAE,IAAI;QACjB,gBAAgB,EAAE,KAAK;QACvB,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,IAAI;QACtB,YAAY,EAAE,IAAI;QAClB,oBAAoB,EAAE,IAAI;QAC1B,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,IAAI;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,qBAAqB;QACrC,aAAa,EAAE,qBAAqB;QACpC,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,kBAAkB,EAAE,KAAK;QACzB,eAAe,EAAE,KAAK;QACtB,iBAAiB,EAAE,KAAK;QACxB,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,CAAC;QAChB,oBAAoB,EAAE,CAAC;QACvB,YAAY,EAAE,IAAI;QAClB,cAAc,EAAE,qBAAqB;QACrC,qBAAqB,EAAE,IAAI;QAC3B,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,IAAI;QACvB,UAAU,EAAE,sCAAsC;QAClD,QAAQ,EAAE,sCAAsC;QAChD,QAAQ,EAAE,CAAC;QACX,YAAY,EAAE,QAAQ;QACtB,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,sCAAsC;QAClD,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,UAAU;QACvB,YAAY,EAAE,QAAQ;QACtB,eAAe,EAAE,iHAAiH;QAClI,kBAAkB,EAAE,iHAAiH;QACrI,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,qBAAqB;QACtC,iBAAiB,EAAE,IAAI;QACvB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,gBAAgB,EAAE,CAAC;QACnB,iBAAiB,EAAE,CAAC;QACpB,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,CAAC;QAChB,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,CAAC;QAClB,SAAS,EAAE,CAAC;QACZ,aAAa,EAAE,IAAI;QACnB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,IAAI;QACjB,cAAc,EAAE,IAAI;QACpB,cAAc,EAAE,IAAI;QACpB,cAAc,EAAE,IAAI;QACpB,YAAY,EAAE,IAAI;QAClB,eAAe,EAAE,IAAI;QACrB,cAAc,EAAE,IAAI;QACpB,iBAAiB,EAAE,CAAC;QACpB,YAAY,EAAE,CAAC;QACf,gBAAgB,EAAE,CAAC;QACnB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,wBAAwB,EAAE,CAAC;QAC3B,yBAAyB,EAAE,CAAC;QAC5B,SAAS,EAAE,GAAG;QACd,kBAAkB,EAAE,CAAC;QACrB,aAAa,EAAE,KAAK;QACpB,SAAS,EAAE,uBAAuB;QAClC,oBAAoB,EAAE,EAAE;QACxB,YAAY,EAAE,GAAG;QACjB,iBAAiB,EAAE,CACjB;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,IAAI;UACd,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,uBAAuB,EAAE,CACvB;UACE,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,IAAI;UACd,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,SAAS;UAClB,SAAS,EAAE,SAAS;UACpB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,GAAG;UACb,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,wBAAwB,EAAE,CACxB;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,IAAI;UACjB,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,IAAI;UACb,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,IAAI;UACd,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,KAAK;UACf,WAAW,EAAE,CAAC;UACd,YAAY,EAAE,CAAC;UACf,OAAO,EAAE,SAAS;UAClB,SAAS,EAAE,SAAS;UACpB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,GAAG;UACb,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,WAAW;UACpB,SAAS,EAAE,WAAW;UACtB,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,EACD;UACE,QAAQ,EAAE,IAAI;UACd,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,OAAO,EAAE,mBAAmB;UAC5B,SAAS,EAAE,mBAAmB;UAC9B,YAAY,EAAE,IAAI;UAClB,QAAQ,EAAE,EAAE;UACZ,SAAS,EAAE;SACZ,CACF;QACD,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,GAAG;QACjB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,UAAU,EAAE,CAAC;QACb,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,KAAK;QACtB,eAAe,EAAE,CAAC;QAClB,aAAa,EAAE,CAAC;QAChB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,CAAC;QACZ,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,CAAC;QAClB,oBAAoB,EAAE;OACvB,CACF;MACD,iBAAiB,EAAE,CACjB;QACE,UAAU,EAAE,sCAAsC;QAClD,0BAA0B,EAAE,sCAAsC;QAClE,SAAS,EAAE,WAAW;QACtB,aAAa,EAAE,WAAW;QAC1B,iBAAiB,EAAE,GAAG;QACtB,2BAA2B,EAAE,CAAC;QAC9B,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,UAAU;QAClB,YAAY,EAAE,QAAQ;QACtB,cAAc,EAAE,qBAAqB;QACrC,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,qBAAqB;QACnC,4BAA4B,EAAE,GAAG;QACjC,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE;OACZ,EACD;QACE,UAAU,EAAE,sCAAsC;QAClD,0BAA0B,EAAE,sCAAsC;QAClE,SAAS,EAAE,mBAAmB;QAC9B,aAAa,EAAE,mBAAmB;QAClC,iBAAiB,EAAE,GAAG;QACtB,2BAA2B,EAAE,CAAC;QAC9B,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,UAAU;QAClB,YAAY,EAAE,QAAQ;QACtB,cAAc,EAAE,qBAAqB;QACrC,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,qBAAqB;QACnC,4BAA4B,EAAE,GAAG;QACjC,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE;OACZ;KAEJ;IAED,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC;IAErC,IAAI,CAACE,IAAI,EAAE;MACT,IAAI,CAACxC,cAAc,CAAC2C,GAAG,CAAC;QAACC,QAAQ,EAAC,OAAO;QAAEC,OAAO,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAuB,CAAC,CAAC;MAC9F,IAAI,CAACjD,MAAM,CAACkD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAChD;IACF;IAEA,MAAMC,WAAW,GAAG;MAClBT,UAAU,EAAEE,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACD,UAAU;MACvCW,eAAe,EAAET,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACU;KACnC;IAED,IAAI,CAACnD,0BAA0B,CAACoD,kBAAkB,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;MACxEC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,IAAI,KAAK,GAAG,EAAE;UACzB,IAAI,CAACrD,QAAQ,GAAGoD,QAAQ,CAACd,IAAI,CAACgB,OAAO,IAAI,EAAE;UAC3C,IAAI,CAACrD,UAAU,GAAGmD,QAAQ,CAACd,IAAI,CAACrC,UAAU,IAAI,EAAE;UAChD,IAAI,CAACC,SAAS,GAAGkD,QAAQ,CAACd,IAAI,CAACpC,SAAS,IAAI,EAAE;UAC9C,IAAI,CAACC,YAAY,GAAGiD,QAAQ,CAACd,IAAI,CAACiB,mBAAmB,IAAI,EAAE;UAC3D,IAAI,CAACnD,eAAe,GAAGgD,QAAQ,CAACd,IAAI,CAAClC,eAAe,IAAI,EAAE;UAC1D,IAAI,CAACC,UAAU,GAAG+C,QAAQ,CAACd,IAAI,CAACkB,iBAAiB,IAAI,KAAK;UAE1D,IAAI,CAACxD,QAAQ,CAACyD,OAAO,CAAEC,MAAW,IAAI;YACpC,IAAIA,MAAM,CAACC,OAAO,EAAE;cAClBD,MAAM,CAACC,OAAO,CAACF,OAAO,CAAEG,GAAQ,IAAI;gBAClC,IAAIA,GAAG,CAACC,QAAQ,IAAI,CAACD,GAAG,CAACE,gBAAgB,EAAE;kBACzC,IAAIF,GAAG,CAACG,aAAa,EAAE;oBACrBH,GAAG,CAACI,eAAe,GAAGJ,GAAG,CAACK,cAAc;oBACxCL,GAAG,CAACM,cAAc,GAAGN,GAAG,CAACO,aAAa;kBACxC,CAAC,MAAM;oBACLP,GAAG,CAACQ,WAAW,GAAGR,GAAG,CAACS,cAAc,GAAG,CAAC,CAAC;kBAC3C;kBACAT,GAAG,CAACU,WAAW,GAAG,IAAI,CAACrE,UAAU,CAAC,CAAC,CAAC;kBAEpC,MAAMsE,WAAW,GAAGhF,MAAM,CAAC,IAAIiF,IAAI,CAACZ,GAAG,CAACa,YAAY,CAAC,CAAC,CACnDhC,GAAG,CAACmB,GAAG,CAACc,SAAS,EAAE,MAAM,CAAC,CAC1BC,MAAM,CAAC,YAAY,CAAC;kBACvBf,GAAG,CAACgB,gBAAgB,GAAGL,WAAW;kBAElC,IAAI,IAAI,CAAClE,UAAU,IAAIuD,GAAG,CAACiB,MAAM,EAAE;oBACjC,MAAMC,SAAS,GAAG,IAAI,CAAC3E,YAAY,CAAC4E,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,KAAK,KAAK,GAAG,CAAC;oBACvE,IAAIH,SAAS,CAACI,MAAM,GAAG,CAAC,EAAE;sBACxBtB,GAAG,CAACuB,QAAQ,GAAGL,SAAS,CAAC,CAAC,CAAC;oBAC7B;kBACF;gBACF;gBAEA;gBACA,IAAGlB,GAAG,CAACa,YAAY,EAAE;kBACnBb,GAAG,CAACa,YAAY,GAAGlF,MAAM,CAACqE,GAAG,CAACa,YAAY,CAAC,CAACW,MAAM,EAAE;gBACtD;gBACA,IAAGxB,GAAG,CAACyB,eAAe,EAAE;kBACtBzB,GAAG,CAACyB,eAAe,GAAG9F,MAAM,CAACqE,GAAG,CAACyB,eAAe,CAAC,CAACD,MAAM,EAAE;gBAC5D;gBACA,IAAGxB,GAAG,CAAC0B,WAAW,EAAE;kBAClB1B,GAAG,CAAC0B,WAAW,GAAG/F,MAAM,CAACqE,GAAG,CAAC0B,WAAW,CAAC,CAACF,MAAM,EAAE;gBACpD;gBAEA,IAAI1B,MAAM,CAAC6B,aAAa,IAAI7B,MAAM,CAAC8B,eAAe,EAAEN,MAAM,EAAE;kBAC1D,MAAMO,eAAe,GAAG/B,MAAM,CAAC8B,eAAe,CAACE,IAAI,CAAEC,OAAY,IAC/DA,OAAO,CAACC,gBAAgB,KAAKlC,MAAM,CAAC6B,aAAa,CAACM,aAAa,CAChE;kBACD,IAAIJ,eAAe,EAAE;oBACnB/B,MAAM,CAACoC,WAAW,GAAGL,eAAe,CAACG,gBAAgB;kBACvD;gBACF;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC9F,cAAc,CAAC2C,GAAG,CAAC;YAACC,QAAQ,EAAC,OAAO;YAAEC,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAEQ,QAAQ,CAAC2C;UAAO,CAAC,CAAC;UACvF,IAAI,CAACpG,MAAM,CAACkD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAClD;MACF,CAAC;MACDmD,KAAK,EAAGA,KAAY,IAAI;QACtB,IAAI,CAAClG,cAAc,CAAC2C,GAAG,CAAC;UAACC,QAAQ,EAAC,OAAO;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAA4B,CAAC,CAAC;QACnGqD,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEAE,UAAUA,CAAA;IACR,IAAI,CAACnG,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EAC5B;EAEAoG,QAAQA,CAAA;IACN,OAAO,IAAI,CAACnG,QAAQ,CAACoG,MAAM,CAAC,CAACC,KAAK,EAAE3C,MAAM,KAAI;MAC5C,MAAM4C,WAAW,GAAG,IAAI,CAACC,WAAW,CAAC7C,MAAM,CAAC;MAC5C,OAAO2C,KAAK,GAAGC,WAAW;IAC5B,CAAC,EAAE,CAAC,CAAC;EACP;EAEAC,WAAWA,CAACC,UAAe;IACzB,IAAIH,KAAK,GAAG,CAAC;IACb,IAAIG,UAAU,CAAC7C,OAAO,EAAE;MACtB6C,UAAU,CAAC7C,OAAO,CAACF,OAAO,CAAEgD,IAAS,IAAI;QACvCJ,KAAK,IAAII,IAAI,CAACC,UAAU,IAAI,CAAC;QAC7B,IAAID,IAAI,CAACE,eAAe,EAAE;UACxBF,IAAI,CAACE,eAAe,CAAClD,OAAO,CAAEmD,GAAQ,IAAI;YACxCP,KAAK,IAAIO,GAAG,CAACC,MAAM,IAAI,CAAC;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,IAAIL,UAAU,CAACM,kBAAkB,EAAE;MACjCN,UAAU,CAACM,kBAAkB,CAACrD,OAAO,CAAEmD,GAAQ,IAAI;QACjDP,KAAK,IAAIO,GAAG,CAACG,eAAe,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ;IAEA,OAAOV,KAAK;EACd;EAEA;EACAW,mBAAmBA,CAACvC,YAAoB,EAAEY,eAAuB;IAC/D,MAAM4B,MAAM,GAAG;MAAE,GAAG,IAAI,CAACvG;IAAc,CAAE;IACzC,IAAI+D,YAAY,EAAE;MAChBwC,MAAM,CAACC,OAAO,GAAG,IAAI1C,IAAI,CAACC,YAAY,CAAC;MACvCwC,MAAM,CAACE,WAAW,GAAG,IAAI3C,IAAI,CAACC,YAAY,CAAC;IAC7C;IACA,IAAIY,eAAe,EAAE;MACnB4B,MAAM,CAACG,OAAO,GAAG,IAAI5C,IAAI,CAACa,eAAe,CAAC;IAC5C;IACA,OAAO4B,MAAM;EACf;EAEA;EACAI,kBAAkBA,CAACZ,IAAS;IAC1BR,OAAO,CAACqB,GAAG,CAAC,oBAAoB,EAAEb,IAAI,CAAC;IACvC,IAAI,CAACA,IAAI,CAAChC,YAAY,EAAE;MACtB;IACF;IAEA;IACA,MAAMF,WAAW,GAAGhF,MAAM,CAACkH,IAAI,CAAChC,YAAY,CAAC,CAC1ChC,GAAG,CAACgE,IAAI,CAAC/B,SAAS,EAAE,MAAM,CAAC,CAC3BC,MAAM,CAAC,YAAY,CAAC;IACvB8B,IAAI,CAAC7B,gBAAgB,GAAGL,WAAW;IAEnC;IACA,MAAMgD,GAAG,GAAGhI,MAAM,CAACkH,IAAI,CAACnB,WAAW,CAAC,CAACX,MAAM,CAAC,YAAY,CAAC;IACzD,MAAM6C,QAAQ,GAAGjI,MAAM,CAACkH,IAAI,CAAChC,YAAY,CAAC,CACvCgD,IAAI,CAAClI,MAAM,CAACkH,IAAI,CAACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IAEzC;IACA,IAAI,CAAChF,gBAAgB,GAAGkH,QAAQ,GAAG,CAAC;IAEpC;IACA,MAAME,YAAY,GAAGF,QAAQ,GAAGf,IAAI,CAACkB,aAAa;IAClD,MAAMC,aAAa,GAAGJ,QAAQ,GAAGf,IAAI,CAACoB,cAAc;IAEpD;IACApB,IAAI,CAACqB,aAAa,GAAGrB,IAAI,CAACsB,iBAAiB,GAAGL,YAAY;IAC1DjB,IAAI,CAACuB,cAAc,GAAGvB,IAAI,CAACwB,kBAAkB,GAAGL,aAAa;IAC7DnB,IAAI,CAACC,UAAU,GAAGD,IAAI,CAACyB,cAAc,GAAGR,YAAY,GAAGE,aAAa;EACtE;EAEAO,GAAGA,CAAA;IACD,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAIC,mBAAmB,GAAG,IAAI;IAE9B,KAAK,MAAM3E,MAAM,IAAI,IAAI,CAAC1D,QAAQ,EAAE;MAClC,IAAI,CAAC0D,MAAM,CAACoC,WAAW,EAAE;QACvB,IAAI,CAAChG,cAAc,CAAC2C,GAAG,CAAC;UACtBC,QAAQ,EAAC,OAAO;UAChBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAE,GAAGc,MAAM,CAAC4E,GAAG;SACtB,CAAC;QACFF,UAAU,GAAG,KAAK;QAClB;MACF;MAEA,IAAI1E,MAAM,CAACC,OAAO,EAAE;QAClB,KAAK,MAAMC,GAAG,IAAIF,MAAM,CAACC,OAAO,EAAE;UAChC,IAAIC,GAAG,CAAC2E,kBAAkB,KAAK,CAAC,IAAI3E,GAAG,CAAC4E,cAAc,EAAE;YACtD,IAAI,CAAC1I,cAAc,CAAC2C,GAAG,CAAC;cACtBC,QAAQ,EAAC,SAAS;cAClBC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE;aACT,CAAC;YACFyF,mBAAmB,GAAG,KAAK;YAC3B;UACF;UAEA,IAAIzE,GAAG,CAACC,QAAQ,IAAI,CAACD,GAAG,CAACE,gBAAgB,IAAIF,GAAG,CAAC6E,WAAW,IAAI,CAAC7E,GAAG,CAAC8E,SAAS,EAAE;YAC9E,IAAI,IAAI,CAACrI,UAAU,IAAIuD,GAAG,CAACiB,MAAM,EAAE;cACjC,IAAI,CAACjB,GAAG,CAACuB,QAAQ,EAAE;gBACjB,IAAI,CAACrF,cAAc,CAAC2C,GAAG,CAAC;kBACtBC,QAAQ,EAAC,OAAO;kBAChBC,OAAO,EAAE,OAAO;kBAChBC,MAAM,EAAE;iBACT,CAAC;gBACFwF,UAAU,GAAG,KAAK;gBAClB;cACF;cAEA,IAAIxE,GAAG,CAACuB,QAAQ,CAACF,KAAK,KAAK,GAAG,IAAI,CAACrB,GAAG,CAAC+E,eAAe,EAAE;gBACtD,IAAI,CAAC7I,cAAc,CAAC2C,GAAG,CAAC;kBACtBC,QAAQ,EAAC,OAAO;kBAChBC,OAAO,EAAE,OAAO;kBAChBC,MAAM,EAAE;iBACT,CAAC;gBACFwF,UAAU,GAAG,KAAK;gBAClB;cACF;cAEA,IAAKxE,GAAG,CAACuB,QAAQ,CAACF,KAAK,KAAK,GAAG,IAAIrB,GAAG,CAACuB,QAAQ,CAACF,KAAK,KAAK,GAAG,EAAG;gBAC9D,IAAI,CAACrB,GAAG,CAACM,cAAc,EAAE;kBACvB,IAAI,CAACpE,cAAc,CAAC2C,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACFwF,UAAU,GAAG,KAAK;kBAClB;gBACF;gBACA,IAAI,CAACxE,GAAG,CAACI,eAAe,EAAE;kBACxB,IAAI,CAAClE,cAAc,CAAC2C,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACFwF,UAAU,GAAG,KAAK;kBAClB;gBACF;cACF;YACF,CAAC,MAAM;cACL,IAAIxE,GAAG,CAACG,aAAa,EAAE;gBACrB,IAAI,CAACH,GAAG,CAACM,cAAc,EAAE;kBACvB,IAAI,CAACpE,cAAc,CAAC2C,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACFwF,UAAU,GAAG,KAAK;kBAClB;gBACF;gBACA,IAAI,CAACxE,GAAG,CAACI,eAAe,EAAE;kBACxB,IAAI,CAAClE,cAAc,CAAC2C,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACFwF,UAAU,GAAG,KAAK;kBAClB;gBACF;cACF,CAAC,MAAM;gBACL,IAAI,CAACxE,GAAG,CAACQ,WAAW,EAAE;kBACpB,IAAI,CAACtE,cAAc,CAAC2C,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACFwF,UAAU,GAAG,KAAK;kBAClB;gBACF;cACF;YACF;UACF;QACF;MACF;IACF;IAEA,IAAI,CAACC,mBAAmB,IAAI,CAACD,UAAU,EAAE;MACvC;IACF;IAEA,IAAI,CAACvI,0BAA0B,CAAC+I,eAAe,CAAC,IAAI,CAAC5I,QAAQ,CAAC,CAACkD,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACyF,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC/I,cAAc,CAAC2C,GAAG,CAAC;YACtBC,QAAQ,EAAC,SAAS;YAClBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAEQ,QAAQ,CAACE;WAClB,CAAC;UACF,IAAI,CAACwF,MAAM,EAAE;QACf,CAAC,MAAM,IAAI1F,QAAQ,CAACyF,MAAM,KAAK,SAAS,EAAE;UACxC,IAAI,CAAC/I,cAAc,CAAC2C,GAAG,CAAC;YACtBC,QAAQ,EAAC,MAAM;YACfC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAEQ,QAAQ,CAACE;WAClB,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACxD,cAAc,CAAC2C,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAEQ,QAAQ,CAACE;WAClB,CAAC;QACJ;MACF,CAAC;MACD0C,KAAK,EAAGA,KAAc,IAAI;QACxB,IAAI,CAAClG,cAAc,CAAC2C,GAAG,CAAC;UACtBC,QAAQ,EAAC,OAAO;UAChBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAEAkG,MAAMA,CAAA;IACJ,IAAI,CAACnJ,MAAM,CAACkD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAkG,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC/I,QAAQ,CAACoG,MAAM,CAAC,CAACC,KAAK,EAAE3C,MAAM,KAAI;MAC5C,OAAO2C,KAAK,IAAI3C,MAAM,CAACC,OAAO,EAAEuB,MAAM,IAAI,CAAC,CAAC;IAC9C,CAAC,EAAE,CAAC,CAAC;EACP;EAEA8D,SAASA,CAACtF,MAAW,EAAEuF,OAAY;IACjC,IAAIvF,MAAM,IAAIA,MAAM,CAACoD,kBAAkB,EAAE;MACvC,MAAMoC,KAAK,GAAGxF,MAAM,CAACoD,kBAAkB,CAACqC,SAAS,CAAE1C,IAAS,IAC1DA,IAAI,CAAC2C,WAAW,KAAKH,OAAO,CAACG,WAAW,IACxC3C,IAAI,CAACM,eAAe,KAAKkC,OAAO,CAAClC,eAAe,CACjD;MACD,IAAImC,KAAK,GAAG,CAAC,CAAC,EAAE;QACdxF,MAAM,CAACoD,kBAAkB,CAACuC,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QAC1C,IAAI,CAACI,gBAAgB,CAAC5F,MAAM,CAAC;MAC/B;IACF;EACF;EAEQ4F,gBAAgBA,CAAC5F,MAAW;IAClC,MAAM6F,mBAAmB,GAAG,CAAC7F,MAAM,CAACoD,kBAAkB,IAAI,EAAE,EAAEV,MAAM,CAAC,CAACC,KAAa,EAAEO,GAAQ,KAAI;MAC/F,OAAOP,KAAK,IAAIO,GAAG,CAACG,eAAe,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,CAAC,CAAC;IAELrD,MAAM,CAACgD,UAAU,GAAG6C,mBAAmB,GAAG,IAAI,CAAChD,WAAW,CAAC7C,MAAM,CAAC;EACpE;EAEA;EACA8F,yBAAyBA,CAAC/C,IAAS;IACjC,IAAIgD,UAAU,CAAChD,IAAI,CAACC,UAAU,CAAC,GAAG+C,UAAU,CAAChD,IAAI,CAACiD,eAAe,CAAC,EAAE;MAClE,IAAID,UAAU,CAAChD,IAAI,CAAC8B,kBAAkB,CAAC,GAAGkB,UAAU,CAAChD,IAAI,CAACiD,eAAe,CAAC,IAAIjD,IAAI,CAAC8B,kBAAkB,IAAI,CAAC,EAAE;QAC1G,IAAI,CAACzI,cAAc,CAAC2C,GAAG,CAAC;UACtBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE;SACT,CAAC;QACF6D,IAAI,CAAC8B,kBAAkB,GAAG,CAAC;MAC7B;IACF,CAAC,MAAM;MACL,IAAIkB,UAAU,CAAChD,IAAI,CAAC8B,kBAAkB,CAAC,IAAIkB,UAAU,CAAChD,IAAI,CAACiD,eAAe,CAAC,IAAIjD,IAAI,CAAC8B,kBAAkB,IAAI,CAAC,EAAE;QAC3G,IAAI,CAACzI,cAAc,CAAC2C,GAAG,CAAC;UACtBC,QAAQ,EAAC,SAAS;UAClBC,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE;SACT,CAAC;QACF6D,IAAI,CAAC8B,kBAAkB,GAAG,CAAC;MAC7B;IACF;EACF;EAEA;EACAoB,gBAAgBA,CAAClD,IAAS;IACxB,IAAI,CAACjG,YAAY,GAAGiG,IAAI;IACxB,IAAI,CAAChG,eAAe,GAAGgG,IAAI,CAACmD,WAAW,IAAI,CAAC;IAC5C,IAAI,CAACrJ,qBAAqB,GAAG,IAAI;EACnC;EAEA;EACAsJ,iBAAiBA,CAAA;IACf,IAAI,CAACtJ,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC1B;EAEA;EACAqJ,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACtJ,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACoJ,WAAW,GAAG,IAAI,CAACnJ,eAAe;MACpD,IAAI,IAAI,CAACA,eAAe,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACD,YAAY,CAACuJ,UAAU,GAAG,aAAa;MAC9C,CAAC,MAAM;QACL,IAAI,CAACvJ,YAAY,CAACuJ,UAAU,GAAG,aAAa;MAC9C;IACF;IACA,IAAI,CAACxJ,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC1B;EAEA;EACAuJ,UAAUA,CAACvD,IAAS;IAClB,OAAOA,IAAI,EAAEhC,YAAY,GAAGlF,MAAM,CAACkH,IAAI,CAAChC,YAAY,CAAC,CAACW,MAAM,EAAE,GAAG,IAAI;EACvE;EAEA;EACA6E,UAAUA,CAACxD,IAAS;IAClB,OAAOA,IAAI,EAAEpB,eAAe,GAAG9F,MAAM,CAACkH,IAAI,CAACpB,eAAe,CAAC,CAACD,MAAM,EAAE,GAAG,IAAI;EAC7E;EAEA;EACA8E,gBAAgBA,CAACzD,IAAS;IACxB,IAAI,CAACrF,mBAAmB,GAAGqF,IAAI;IAC/B,IAAI,CAACpF,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,WAAW,GAAG;MACjBC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAACF,iBAAiB,EAAE;MACjEG,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;KACR;IACD,IAAI,CAACf,iBAAiB,GAAG,IAAI;EAC/B;EAEA;EACAgJ,iBAAiBA,CAAC/F,WAAgB,EAAEqC,IAAS;IAC3C,IAAI,CAACrF,mBAAmB,GAAGqF,IAAI;IAC/B,IAAI,CAACpF,iBAAiB,GAAG,MAAM;IAC/B,IAAI,CAACC,cAAc,GAAG8C,WAAW;IACjC,IAAI,CAAC5C,WAAW,GAAG;MACjBC,gBAAgB,EAAE2C,WAAW,CAAC3C,gBAAgB;MAC9CC,SAAS,EAAE0C,WAAW,CAAC1C,SAAS;MAChCC,QAAQ,EAAEyC,WAAW,CAACzC,QAAQ;MAC9BC,YAAY,EAAEwC,WAAW,CAACxC,YAAY;MACtCC,YAAY,EAAEuC,WAAW,CAACvC,YAAY,IAAI,EAAE;MAC5CC,IAAI,EAAEsC,WAAW,CAACtC,IAAI;MACtBC,KAAK,EAAEqC,WAAW,CAACrC,KAAK;MACxBC,OAAO,EAAEoC,WAAW,CAACpC,OAAO;MAC5BC,KAAK,EAAEmC,WAAW,CAACnC,KAAK;MACxBC,KAAK,EAAEkC,WAAW,CAAClC,KAAK,IAAI;KAC7B;IACD,IAAI,CAACf,iBAAiB,GAAG,IAAI;EAC/B;EAEA;EACAiJ,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/I,iBAAiB,KAAK,KAAK,EAAE;MACpC,MAAMgJ,UAAU,GAAG;QAAE,GAAG,IAAI,CAAC7I;MAAW,CAAE;MAE1C;MACA,IAAI,IAAI,CAACJ,mBAAmB,CAACiD,cAAc,EAAE;QAC3C,IAAI,CAACjD,mBAAmB,CAACiD,cAAc,CAACiG,IAAI,CAACD,UAAU,CAAC;MAC1D,CAAC,MAAM;QACL,IAAI,CAACjJ,mBAAmB,CAACiD,cAAc,GAAG,CAACgG,UAAU,CAAC;MACxD;MAEA;MACA,IAAI,CAACjJ,mBAAmB,CAACgD,WAAW,GAAGiG,UAAU;MAEjD;MACA,IAAI,CAACrK,QAAQ,CAACyD,OAAO,CAAEC,MAAW,IAAI;QACpC,IAAIA,MAAM,CAAC6G,QAAQ,KAAK,IAAI,CAACnJ,mBAAmB,CAACmJ,QAAQ,EAAE;UACzD7G,MAAM,CAACC,OAAO,EAAEF,OAAO,CAAE+G,IAAS,IAAI;YACpC,IAAIA,IAAI,CAACnG,cAAc,EAAE;cACvBmG,IAAI,CAACnG,cAAc,CAACiG,IAAI,CAAC;gBAAE,GAAGD;cAAU,CAAE,CAAC;cAC3C,IAAIG,IAAI,CAACpG,WAAW,EAAE3C,gBAAgB,EAAEgJ,QAAQ,CAAC,oBAAoB,CAAC,EAAE;gBACtED,IAAI,CAACE,cAAc,GAAG,IAAI;cAC5B;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMC,cAAc,GAAG;QAAE,GAAG,IAAI,CAACnJ;MAAW,CAAE;MAE9C;MACA,IAAI,CAACxB,QAAQ,CAACyD,OAAO,CAAEC,MAAW,IAAI;QACpC,IAAIA,MAAM,CAAC6G,QAAQ,KAAK,IAAI,CAACnJ,mBAAmB,CAACmJ,QAAQ,EAAE;UACzD7G,MAAM,CAACC,OAAO,EAAEF,OAAO,CAAE+G,IAAS,IAAI;YACpC,IAAIA,IAAI,CAACnG,cAAc,EAAE;cACvB;cACA,MAAM6E,KAAK,GAAGsB,IAAI,CAACnG,cAAc,CAAC8E,SAAS,CACxCnE,CAAM,IAAKA,CAAC,CAACvD,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,CACxE;cACD,IAAIyH,KAAK,GAAG,CAAC,CAAC,EAAE;gBACdsB,IAAI,CAACnG,cAAc,CAAC6E,KAAK,CAAC,GAAGyB,cAAc;cAC7C;cAEA;cACA,IAAIH,IAAI,CAACpG,WAAW,EAAE3C,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,EAAE;gBAC/E+I,IAAI,CAACpG,WAAW,GAAGuG,cAAc;cACnC;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,CAACxJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAsJ,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACvJ,iBAAiB,KAAK,KAAK,EAAE;MACpC,IAAI,CAACE,iBAAiB,EAAE;IAC1B;IACA,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAuJ,qBAAqBA,CAACpE,IAAS;IAC7B,IAAIA,IAAI,CAACrC,WAAW,EAAE3C,gBAAgB,EAAEgJ,QAAQ,CAAC,oBAAoB,CAAC,EAAE;MACtEhE,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC5B,CAAC,MAAM;MACLjE,IAAI,CAACiE,cAAc,GAAG,KAAK;IAC7B;EACF;EAEA;EACAI,YAAYA,CAACpH,MAAW,EAAE+C,IAAS;IACjC,IAAI/C,MAAM,CAACC,OAAO,EAAE;MAClB,MAAMuF,KAAK,GAAGxF,MAAM,CAACC,OAAO,CAACwF,SAAS,CAAEvF,GAAQ,IAAKA,GAAG,KAAK6C,IAAI,CAAC;MAClE,IAAIyC,KAAK,GAAG,CAAC,CAAC,EAAE;QACdxF,MAAM,CAACC,OAAO,CAAC0F,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACjC;IACF;EACF;EAEA;EACA6B,SAASA,CAACtE,IAAS;IACjB,IAAIA,IAAI,CAACuE,iBAAiB,EAAE;MAC1BC,MAAM,CAACC,IAAI,CAACzE,IAAI,CAACuE,iBAAiB,EAAE,QAAQ,CAAC;IAC/C;EACF;CACD;AAhlCYvL,oBAAoB,GAAA0L,UAAA,EANhC7L,SAAS,CAAC;EACT8L,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,QAAQ,EAAE,+BAA+B;EACzCC,SAAS,EAAE,CAAC/L,cAAc;CAC3B,CAAC,C,EACWC,oBAAoB,CAglChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}