{"ast": null, "code": "import moment from 'moment';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/checkbox\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/inputnumber\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"primeng/inputgroup\";\nimport * as i15 from \"primeng/inputgroupaddon\";\nimport * as i16 from \"primeng/ripple\";\nconst _c0 = () => ({\n  width: \"30vw\"\n});\nconst _c1 = () => ({\n  width: \"45vw\"\n});\nfunction LoanProceedComponent_div_10_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵtextInterpolate2(\" \", item_r3.bankAccountRanking, \".\", item_r3.reference, \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", item_r4.bankAccountRanking, \".\", item_r4.reference, \"\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"i\", 49);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_26_Template_i_click_2_listener() {\n      const feeItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const dealer_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.removeFee(dealer_r2, feeItem_r6));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"div\", 51)(5, \"div\", 37);\n    i0.ɵɵtext(6, \"Fee Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 53)(10, \"div\", 37);\n    i0.ɵɵtext(11, \"Pay Amount:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 54);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const feeItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(feeItem_r6.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 2, feeItem_r6.remainingAmount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_i_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 73);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_i_23_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.showPayoff());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_i_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 74);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_i_24_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.showPayoff());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fee_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(fee_r12.feeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, fee_r12.amount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_29_i_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 80);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_29_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 80);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 10)(3, \"div\");\n    i0.ɵɵtext(4, \"Principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, LoanProceedComponent_div_10_div_27_div_29_div_8_Template, 6, 4, \"div\", 77);\n    i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\");\n    i0.ɵɵtext(11, \"Interest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 78);\n    i0.ɵɵtemplate(13, LoanProceedComponent_div_10_div_27_div_29_i_13_Template, 1, 0, \"i\", 79);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 10)(17, \"div\");\n    i0.ɵɵtext(18, \"WIP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 78);\n    i0.ɵɵtemplate(20, LoanProceedComponent_div_10_div_27_div_29_i_20_Template, 1, 0, \"i\", 79);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 6, item_r9.principal));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r9.extraAmountList);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isShowEstimation);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 8, item_r9.interestPrice), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isShowEstimation);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 10, item_r9.insurancePrice), \" \");\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 62);\n    i0.ɵɵtext(2, \"Additional Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 81);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_div_30_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.inputOtherAmount(item_r9));\n    });\n    i0.ɵɵtext(8, \"Change\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 1, item_r9.otherAmount));\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 82)(2, \"div\", 68);\n    i0.ɵɵtext(3, \"Principal Only\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-checkbox\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_36_Template_p_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.isOnlyPrincial, $event) || (item_r9.isOnlyPrincial = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.isOnlyPrincial);\n    i0.ɵɵproperty(\"binary\", true);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 10)(2, \"div\", 68);\n    i0.ɵɵtext(3, \"Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"p-inputGroup\", 84)(6, \"p-inputGroupAddon\");\n    i0.ɵɵtext(7, \"$\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-inputNumber\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_37_Template_p_inputNumber_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.onlyPrincialAmount, $event) || (item_r9.onlyPrincialAmount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_37_Template_p_inputNumber_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onlyPrincipalAmountChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\", 68);\n    i0.ɵɵtext(11, \"Schedule Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 68)(13, \"p-calendar\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_37_Template_p_calendar_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.scheduleDate, $event) || (item_r9.scheduleDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"minFractionDigits\", 2)(\"maxFractionDigits\", 2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.onlyPrincialAmount);\n    i0.ɵɵproperty(\"maxlength\", 14);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.scheduleDate);\n    i0.ɵɵproperty(\"disabled\", true)(\"selectOtherMonths\", true)(\"showButtonBar\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"dateFormat\", \"mm/dd/yy\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90);\n    i0.ɵɵtext(2, \"Special Title Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 92)(4, \"p-dropdown\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_div_14_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.holdType, $event) || (item_r9.holdType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.holdType);\n    i0.ɵɵproperty(\"options\", ctx_r6.holdTypeList)(\"showClear\", true);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90);\n    i0.ɵɵtext(2, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 100)(4, \"p-dropdown\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_div_20_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.contactInfo, $event) || (item_r9.contactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LoanProceedComponent_div_10_div_27_div_38_div_20_Template_p_dropdown_onChange_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.shippingContactChange(item_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_div_38_div_20_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.addContactDialog(item_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_div_38_div_20_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.editContactDialog(item_r9.contactInfo, item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.contactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.contactDtoList)(\"showClear\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", item_r9.isDisabledEdit);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90);\n    i0.ɵɵtext(2, \"Shipping Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 92)(4, \"p-dropdown\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_div_21_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newContactInfo, $event) || (item_r9.newContactInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newContactInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newContactDtoList)(\"showClear\", true);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90);\n    i0.ɵɵtext(2, \"Shipping Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 92)(4, \"p-dropdown\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_div_22_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const item_r9 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.newLocationInfo, $event) || (item_r9.newLocationInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.newLocationInfo);\n    i0.ɵɵproperty(\"options\", item_r9.newLocationDtoList)(\"showClear\", true);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_27_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.contactInfo.addressLine2);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LoanProceedComponent_div_10_div_27_div_38_div_27_p_5_Template, 2, 1, \"p\", 107);\n    i0.ɵɵelementStart(6, \"p\", 106);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.contactInfo.firstName, \" \", item_r9.contactInfo.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.contactInfo.addressLine1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.contactInfo.addressLine2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r9.contactInfo.city, \", \", item_r9.contactInfo.state, \", \", item_r9.contactInfo.zipCode, \"\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_28_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r9.newLocationInfo.address2);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LoanProceedComponent_div_10_div_27_div_38_div_28_p_5_Template, 2, 1, \"p\", 107);\n    i0.ɵɵelementStart(6, \"p\", 106);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r9.newContactInfo.firstName, \" \", item_r9.newContactInfo.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.newLocationInfo.address1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.newLocationInfo.address2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r9.newLocationInfo.city, \", \", item_r9.newLocationInfo.state, \", \", item_r9.newLocationInfo.zipCode, \"\");\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 88);\n    i0.ɵɵtext(3, \"Title Shipping Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 89)(5, \"div\", 90);\n    i0.ɵɵtext(6, \"Release Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 91)(8, \"div\", 92)(9, \"p-calendar\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_Template_p_calendar_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.titleReleaseDate, $event) || (item_r9.titleReleaseDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 94)(11, \"div\");\n    i0.ɵɵtext(12, \"Hold\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-checkbox\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_Template_p_checkbox_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.isHold, $event) || (item_r9.isHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(14, LoanProceedComponent_div_10_div_27_div_38_div_14_Template, 5, 3, \"div\", 96);\n    i0.ɵɵelementStart(15, \"div\", 89)(16, \"div\", 90);\n    i0.ɵɵtext(17, \"Shipping Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 92)(19, \"p-dropdown\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_div_38_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.mailFeeInfo, $event) || (item_r9.mailFeeInfo = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(20, LoanProceedComponent_div_10_div_27_div_38_div_20_Template, 7, 4, \"div\", 96)(21, LoanProceedComponent_div_10_div_27_div_38_div_21_Template, 5, 3, \"div\", 96)(22, LoanProceedComponent_div_10_div_27_div_38_div_22_Template, 5, 3, \"div\", 96);\n    i0.ɵɵelementStart(23, \"div\", 98)(24, \"div\", 90);\n    i0.ɵɵtext(25, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 92);\n    i0.ɵɵtemplate(27, LoanProceedComponent_div_10_div_27_div_38_div_27_Template, 8, 7, \"div\", 99)(28, LoanProceedComponent_div_10_div_27_div_38_div_28_Template, 8, 7, \"div\", 99);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.titleReleaseDate);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"showButtonBar\", true)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"readonlyInput\", true)(\"dateFormat\", \"mm/dd/yy\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.isHold);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.holdSwitch && item_r9.isHold);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.mailFeeInfo);\n    i0.ɵɵproperty(\"options\", ctx_r6.postageFee)(\"showClear\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.contactSwitch);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.contactSwitch);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.contactSwitch);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r9.contactSwitch && item_r9.contactInfo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.contactSwitch && item_r9.newContactInfo && item_r9.newLocationInfo);\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 108);\n    i0.ɵɵtext(2, \"Title Released\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoanProceedComponent_div_10_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"i\", 49);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_div_10_div_27_Template_i_click_2_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const dealer_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.removePayoff(dealer_r2, item_r9));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 55)(4, \"div\", 56);\n    i0.ɵɵelement(5, \"i\", 57);\n    i0.ɵɵtext(6, \" Vehicle Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 2);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 2);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 4)(12, \"span\", 58);\n    i0.ɵɵtext(13, \"Due Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 58);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 59)(18, \"div\", 56);\n    i0.ɵɵelement(19, \"i\", 60);\n    i0.ɵɵtext(20, \" Payment Detail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 61)(22, \"div\", 62);\n    i0.ɵɵtemplate(23, LoanProceedComponent_div_10_div_27_i_23_Template, 1, 0, \"i\", 63)(24, LoanProceedComponent_div_10_div_27_i_24_Template, 1, 0, \"i\", 64);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, LoanProceedComponent_div_10_div_27_div_29_Template, 23, 12, \"div\", 65)(30, LoanProceedComponent_div_10_div_27_div_30_Template, 9, 3, \"div\", 66);\n    i0.ɵɵelementStart(31, \"div\", 10)(32, \"div\", 67);\n    i0.ɵɵtext(33, \"Schedule Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 68)(35, \"p-calendar\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_div_27_Template_p_calendar_ngModelChange_35_listener($event) {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r9.scheduleDate, $event) || (item_r9.scheduleDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onSelect\", function LoanProceedComponent_div_10_div_27_Template_p_calendar_onSelect_35_listener() {\n      const item_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.scheduleDateChange(item_r9));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(36, LoanProceedComponent_div_10_div_27_div_36_Template, 5, 2, \"div\", 70)(37, LoanProceedComponent_div_10_div_27_div_37_Template, 14, 11, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, LoanProceedComponent_div_10_div_27_div_38_Template, 29, 18, \"div\", 72)(39, LoanProceedComponent_div_10_div_27_div_39_Template, 3, 0, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(item_r9.vin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r9.year, \" \", item_r9.make, \" \", item_r9.model, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 27, item_r9.nextDueDate, \"MM/dd/yy\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r9.buttonName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 30, item_r9.totalMoney));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.payoff);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.isPayOff && !item_r9.isPartialPayment);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r9.scheduleDate);\n    i0.ɵɵproperty(\"selectOtherMonths\", true)(\"showButtonBar\", false)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"dateFormat\", \"mm/dd/yy\")(\"showTime\", false)(\"showIcon\", false)(\"readonlyInput\", true)(\"minDate\", ctx_r6.getMinDate(item_r9))(\"maxDate\", ctx_r6.getMaxDate(item_r9))(\"disabled\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.buttonName === \"PayOff\" && item_r9.isShowPrincipal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.isOnlyPrincial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.displayMail && !item_r9.isTrusted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.displayMail && item_r9.isTrusted);\n  }\n}\nfunction LoanProceedComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 10)(2, \"div\", 35)(3, \"div\", 36)(4, \"div\", 37);\n    i0.ɵɵtext(5, \"Dealer Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"div\", 37);\n    i0.ɵɵtext(10, \"Dealer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"div\", 37);\n    i0.ɵɵtext(15, \"Subtotal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 39);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 40)(20, \"div\", 37);\n    i0.ɵɵtext(21, \"Bank Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p-dropdown\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_div_10_Template_p_dropdown_ngModelChange_22_listener($event) {\n      const dealer_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(dealer_r2.bankAccount, $event) || (dealer_r2.bankAccount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(23, LoanProceedComponent_div_10_ng_template_23_Template, 1, 2, \"ng-template\", 42)(24, LoanProceedComponent_div_10_ng_template_24_Template, 2, 2, \"ng-template\", 43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(25, \"img\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, LoanProceedComponent_div_10_div_26_Template, 15, 4, \"div\", 45)(27, LoanProceedComponent_div_10_div_27_Template, 40, 32, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dealer_r2 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(dealer_r2.dealerCode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dealer_r2.dba);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 11, ctx_r6.getSubTotal(dealer_r2)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", dealer_r2.bankAccount);\n    i0.ɵɵproperty(\"options\", dealer_r2.bankAccountList)(\"showClear\", true)(\"optionLabel\", \"reference\")(\"optionValue\", \"bankAccountDtoId\")(\"filter\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", dealer_r2.dealerLevelFeeList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", dealer_r2.dtoList);\n  }\n}\nfunction LoanProceedComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancelOtherAmount());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_17_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.confirmOtherAmount());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoanProceedComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_57_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancelContactDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function LoanProceedComponent_ng_template_57_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.saveContact());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let LoanProceedComponent = /*#__PURE__*/(() => {\n  class LoanProceedComponent {\n    constructor(router, route, loanSchedulePaymentService, messageService) {\n      this.router = router;\n      this.route = route;\n      this.loanSchedulePaymentService = loanSchedulePaymentService;\n      this.messageService = messageService;\n      this.payoff = false;\n      this.formdata = [];\n      this.postageFee = [];\n      this.stateList = [];\n      this.holdTypeList = [];\n      this.uccProviderList = [];\n      this.holdSwitch = false;\n      this.isShowEstimation = false;\n      // Additional Payment dialog variables\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n      // Calendar configuration\n      this.calendarConfig = {\n        showButtonBar: true,\n        monthNavigator: true,\n        yearNavigator: true,\n        dateFormat: 'mm/dd/yy',\n        showTime: false,\n        showIcon: false,\n        readonlyInput: true,\n        appendTo: 'body'\n      };\n      // Contact Dialog\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.contactDialogMode = 'add';\n      this.editingContact = null;\n      this.tempContactNumber = 0;\n      // Contact Form\n      this.contactForm = {\n        contactReference: '',\n        firstName: '',\n        lastName: '',\n        addressLine1: '',\n        addressLine2: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        phone: '',\n        email: ''\n      };\n    }\n    ngOnInit() {\n      // 临时使用mock数据替代路由参数\n      const mockData = {\n        selectData: [{\n          \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 351764,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": null,\n          \"soldDisp\": \"\",\n          \"financeTag\": null,\n          \"assetId\": null,\n          \"titleStatus\": \"RE\",\n          \"titleStatusDisplay\": \"Trusted Title Received\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2022,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287165\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 90,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 90,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 0,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 0,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 214,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287165-BMW 320Li 2022\",\n          \"feeAmountNoReserve\": 90,\n          \"reserveFee\": 0,\n          \"extraAmountList\": [{\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPayoff\": [{\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPartial\": [{\n            \"active\": true,\n            \"fixAmount\": 50,\n            \"tempAmount\": 50,\n            \"feeId\": null,\n            \"feeName\": \"TERM_3\",\n            \"createDate\": null,\n            \"amount\": 50,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 0,\n            \"tempAmount\": 0,\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 0,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 15,\n            \"tempAmount\": 15,\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        }, {\n          \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\n          \"vDealerLevelDto\": null,\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\n          \"accountNumber\": 352478,\n          \"cost\": 2000,\n          \"currentCost\": 2000,\n          \"sold\": \"\",\n          \"soldOperationType\": \"\",\n          \"soldDisp\": \"\",\n          \"financeTag\": \"\",\n          \"assetId\": null,\n          \"titleStatus\": \"PT\",\n          \"titleStatusDisplay\": \"Pending Release Trusted\",\n          \"interestDaily\": 0,\n          \"insuranceDaily\": 0,\n          \"interestPrice\": 0,\n          \"insurancePrice\": 0,\n          \"interestPriceTemp\": 0,\n          \"insurancePriceTemp\": 0,\n          \"isOnlyPrincial\": false,\n          \"onlyPrincialAmount\": 0,\n          \"isPastDue\": true,\n          \"year\": 2023,\n          \"fromDealerView\": null,\n          \"isScheduled\": false,\n          \"make\": \"BMW\",\n          \"model\": \"320Li\",\n          \"vin\": \"2GCEK19J245287243\",\n          \"titleReleaseDate\": null,\n          \"delayDays\": 0,\n          \"titleReleaseHoldDate\": null,\n          \"holdType\": null,\n          \"uccProviderDto\": null,\n          \"uccProviderId\": null,\n          \"titleNote\": null,\n          \"isHasTitleFile\": false,\n          \"fileManagementUrl\": null,\n          \"contactSwitch\": false,\n          \"contactDtoList\": null,\n          \"contactDto\": null,\n          \"newLocationDtoList\": null,\n          \"newLocationDto\": null,\n          \"newContactDtoList\": null,\n          \"newContactDto\": null,\n          \"vinLast6\": null,\n          \"maturituDate\": \"2023-02-28T00:00:00\",\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\n          \"dueDate\": \"0001-01-01T00:00:00\",\n          \"isHold\": false,\n          \"isTrusted\": false,\n          \"payOff\": 2313,\n          \"isPayOff\": true,\n          \"isPartialPayment\": false,\n          \"isCurtailment\": false,\n          \"isShowPrincipal\": false,\n          \"curtailment\": 0,\n          \"otherAmount\": 0,\n          \"otherAmountDisplay\": 0,\n          \"paidAmount\": 2313,\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\n          \"scheduleDateMessage\": null,\n          \"currentDate\": null,\n          \"scheduleDateEnd\": null,\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\n          \"termNo\": 3,\n          \"dealerCode\": \"160625\",\n          \"dealerName\": null,\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"dba\": \"MY160625\",\n          \"legalName\": \"MY160625\",\n          \"buttonName\": \"PayOff\",\n          \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\n          \"principalInterest\": null,\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\n          \"payOffPrincipal\": 2000,\n          \"otherAmountLimit\": 0,\n          \"payOffTermFee\": 0,\n          \"payOffFeeItem\": 0,\n          \"payOffInterest\": 0,\n          \"payOffInsurance\": 0,\n          \"partPrincipal\": 2000,\n          \"partTermFee\": 0,\n          \"partFeeItem\": 0,\n          \"partInterest\": 0,\n          \"partInsurance\": 0,\n          \"mailFee\": 0,\n          \"mailFeeName\": null,\n          \"principal\": 0,\n          \"titleName\": null,\n          \"titleContent\": null,\n          \"titleShoping\": null,\n          \"titlepostage\": null,\n          \"contactsId\": null,\n          \"newLocationId\": null,\n          \"newContactId\": null,\n          \"provincialMoney\": 0,\n          \"totalMoney\": 0,\n          \"tempTotalMoney\": 0,\n          \"subtotal\": 0,\n          \"chargeDay\": 0,\n          \"chargeDayMoneyInterest\": 0,\n          \"chargeDayMoneyInsurance\": 0,\n          \"stockId\": 312,\n          \"extraAmountMoney\": 0,\n          \"displayMail\": false,\n          \"vehicle\": \"287243-BMW 320Li 2023\",\n          \"feeAmountNoReserve\": 65,\n          \"reserveFee\": 248,\n          \"extraAmountList\": [{\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPayoff\": [{\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"RESERVE\",\n            \"feeName\": \"Reserve\",\n            \"createDate\": null,\n            \"amount\": 248,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"extraAmountListPartial\": [{\n            \"active\": true,\n            \"fixAmount\": 2000,\n            \"tempAmount\": 2000,\n            \"feeId\": null,\n            \"feeName\": \"Principal\",\n            \"createDate\": null,\n            \"amount\": 2000,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }, {\n            \"active\": false,\n            \"fixAmount\": 0,\n            \"tempAmount\": 0,\n            \"feeId\": \"RESERVE\",\n            \"feeName\": \"Reserve\",\n            \"createDate\": null,\n            \"amount\": 248,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 15,\n            \"tempAmount\": 15,\n            \"feeId\": \"ADMIN_FEE\",\n            \"feeName\": \"Admin Fee\",\n            \"createDate\": null,\n            \"amount\": 15,\n            \"payFlag\": null\n          }, {\n            \"active\": true,\n            \"fixAmount\": 25,\n            \"tempAmount\": 25,\n            \"feeId\": \"TRUSTED_TITLE_FEE\",\n            \"feeName\": \"Trusted Title Fee\",\n            \"createDate\": null,\n            \"amount\": 25,\n            \"payFlag\": null\n          }],\n          \"payInfoList\": null,\n          \"loanStatus\": \"L\",\n          \"isDuePayOff\": false,\n          \"isSoldPayOff\": false,\n          \"nsfCount\": 0,\n          \"waivedAmount\": 0,\n          \"extensionHide\": false,\n          \"paidPrinciple\": 0,\n          \"paidTermFee\": 0,\n          \"paidFee\": 0,\n          \"paidPcr\": 0,\n          \"paidInterest\": 0,\n          \"paidInsurance\": 0,\n          \"otherAmountDisable\": true\n        }],\n        \"selectDealerFee\": [{\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\n          \"feeName\": \"Audit Fee\",\n          \"description\": \"Audit Fee\",\n          \"remainingAmount\": 100,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2023-06-20T09:29:05\",\n          \"postpaymentDealerFeeAmount\": 100,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        }, {\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\n          \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\n          \"feeName\": \"OC Defense Fee_CO\",\n          \"description\": \"OC Defense Fee_CO\",\n          \"remainingAmount\": 123,\n          \"chargedOffRemainingAmount\": 0,\n          \"paidStatus\": \"UP\",\n          \"dba\": \"MY160625\",\n          \"name\": \"MY160625\",\n          \"dealerCode\": \"160625\",\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\n          \"dueDate\": null,\n          \"createDate\": \"2025-05-29T02:44:35\",\n          \"postpaymentDealerFeeAmount\": 123,\n          \"removeDay\": true,\n          \"feeType\": \"P\"\n        }]\n      };\n      const data = JSON.stringify(mockData);\n      if (!data) {\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'No payment data found'\n        });\n        this.router.navigate(['/loan/schedule-payment']);\n        return;\n      }\n      const paymentData = {\n        selectData: JSON.parse(data).selectData,\n        selectDealerFee: JSON.parse(data).selectDealerFee\n      };\n      this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\n        next: response => {\n          if (response.code === 200) {\n            this.formdata = response.data.results || [];\n            this.postageFee = response.data.postageFee || [];\n            this.stateList = response.data.stateList || [];\n            this.holdTypeList = response.data.releaseHoldTypeList || [];\n            this.uccProviderList = response.data.uccProviderList || [];\n            this.holdSwitch = response.data.releaseHoldSwitch || false;\n            this.formdata.forEach(dealer => {\n              if (dealer.dtoList) {\n                dealer.dtoList.forEach(dto => {\n                  if (dto.isPayOff && !dto.isPartialPayment) {\n                    if (dto.contactSwitch) {\n                      dto.newLocationInfo = dto.newLocationDto;\n                      dto.newContactInfo = dto.newContactDto;\n                    } else {\n                      dto.contactInfo = dto.contactDtoList?.[0];\n                    }\n                    dto.mailFeeInfo = this.postageFee[0];\n                    const releaseDate = moment(new Date(dto.scheduleDate)).add(dto.delayDays, 'days').format('MM/DD/YYYY');\n                    dto.titleReleaseDate = releaseDate;\n                    if (this.holdSwitch && dto.isHold) {\n                      const holdTypes = this.holdTypeList.filter(c => c.value === 'H');\n                      if (holdTypes.length > 0) {\n                        dto.holdType = holdTypes[0];\n                      }\n                    }\n                  }\n                  // 转换日期格式\n                  if (dto.scheduleDate) {\n                    dto.scheduleDate = moment(dto.scheduleDate).toDate();\n                  }\n                  if (dto.scheduleDateEnd) {\n                    dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\n                  }\n                  if (dto.currentDate) {\n                    dto.currentDate = moment(dto.currentDate).toDate();\n                  }\n                  if (dealer.paymentSource && dealer.bankAccountList?.length) {\n                    const matchingAccount = dealer.bankAccountList.find(account => account.bankAccountDtoId === dealer.paymentSource.bankAccountId);\n                    if (matchingAccount) {\n                      dealer.bankAccount = matchingAccount.bankAccountDtoId;\n                    }\n                  }\n                });\n              }\n            });\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.message\n            });\n            this.router.navigate(['/loan/schedule-payment']);\n          }\n        },\n        error: error => {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to get payment info'\n          });\n          console.error('Error getting payment info:', error);\n        }\n      });\n    }\n    showPayoff() {\n      this.payoff = !this.payoff;\n    }\n    getTotal() {\n      return this.formdata.reduce((total, dealer) => {\n        const dealerTotal = this.getSubTotal(dealer);\n        return total + dealerTotal;\n      }, 0);\n    }\n    getSubTotal(dealerInfo) {\n      let total = 0;\n      if (dealerInfo.dtoList) {\n        dealerInfo.dtoList.forEach(item => {\n          total += item.totalMoney || 0;\n          if (item.extraAmountList) {\n            item.extraAmountList.forEach(fee => {\n              total += fee.amount || 0;\n            });\n          }\n        });\n      }\n      if (dealerInfo.dealerLevelFeeList) {\n        dealerInfo.dealerLevelFeeList.forEach(fee => {\n          total += fee.remainingAmount || 0;\n        });\n      }\n      return total;\n    }\n    // 获取日期选择器配置\n    getDatePickerConfig(scheduleDate, scheduleDateEnd) {\n      const config = {\n        ...this.calendarConfig\n      };\n      if (scheduleDate) {\n        config.minDate = new Date(scheduleDate);\n        config.defaultDate = new Date(scheduleDate);\n      }\n      if (scheduleDateEnd) {\n        config.maxDate = new Date(scheduleDateEnd);\n      }\n      return config;\n    }\n    // 日期变更处理\n    scheduleDateChange(item) {\n      console.log('scheduleDateChange', item);\n      if (!item.scheduleDate) {\n        return;\n      }\n      // 计算发布日期\n      const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');\n      item.titleReleaseDate = releaseDate;\n      // 计算日期差\n      const now = moment(item.currentDate).format('MM/DD/YYYY');\n      const diffDays = moment(item.scheduleDate).diff(moment(item.currentDate), 'days');\n      // 显示预估标记\n      this.isShowEstimation = diffDays > 0;\n      // 计算利息和保险费用\n      const diffInterest = diffDays * item.interestDaily;\n      const diffInsurance = diffDays * item.insuranceDaily;\n      // 更新金额\n      item.interestPrice = item.interestPriceTemp + diffInterest;\n      item.insurancePrice = item.insurancePriceTemp + diffInsurance;\n      item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\n    }\n    pay() {\n      let isContinue = true;\n      let isOnlyPrincipalSave = true;\n      for (const dealer of this.formdata) {\n        if (!dealer.bankAccount) {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: `${dealer.dba}'s Bank Account is required`\n          });\n          isContinue = false;\n          break;\n        }\n        if (dealer.dtoList) {\n          for (const dto of dealer.dtoList) {\n            if (dto.onlyPrincialAmount === 0 && dto.isOnlyPrincial) {\n              this.messageService.add({\n                severity: 'warning',\n                summary: 'Warning',\n                detail: 'Only Principal amount should be greater than 0!'\n              });\n              isOnlyPrincipalSave = false;\n              break;\n            }\n            if (dto.isPayOff && !dto.isPartialPayment && dto.displayMail && !dto.isTrusted) {\n              if (this.holdSwitch && dto.isHold) {\n                if (!dto.holdType) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Special Title Type is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n                if (dto.holdType.value === 'T' && !dto.holdContactInfo) {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Shipping contact is required'\n                  });\n                  isContinue = false;\n                  break;\n                }\n                if (dto.holdType.value === 'D' || dto.holdType.value === 'H') {\n                  if (!dto.newContactInfo) {\n                    this.messageService.add({\n                      severity: 'error',\n                      summary: 'Error',\n                      detail: 'Shipping contact is required'\n                    });\n                    isContinue = false;\n                    break;\n                  }\n                  if (!dto.newLocationInfo) {\n                    this.messageService.add({\n                      severity: 'error',\n                      summary: 'Error',\n                      detail: 'Shipping location is required'\n                    });\n                    isContinue = false;\n                    break;\n                  }\n                }\n              } else {\n                if (dto.contactSwitch) {\n                  if (!dto.newContactInfo) {\n                    this.messageService.add({\n                      severity: 'error',\n                      summary: 'Error',\n                      detail: 'Shipping contact is required'\n                    });\n                    isContinue = false;\n                    break;\n                  }\n                  if (!dto.newLocationInfo) {\n                    this.messageService.add({\n                      severity: 'error',\n                      summary: 'Error',\n                      detail: 'Shipping location is required'\n                    });\n                    isContinue = false;\n                    break;\n                  }\n                } else {\n                  if (!dto.contactInfo) {\n                    this.messageService.add({\n                      severity: 'error',\n                      summary: 'Error',\n                      detail: 'Shipping contact is required'\n                    });\n                    isContinue = false;\n                    break;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n      if (!isOnlyPrincipalSave || !isContinue) {\n        return;\n      }\n      this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\n        next: response => {\n          if (response.status === 'success') {\n            this.messageService.add({\n              severity: 'success',\n              summary: 'Success',\n              detail: response.results\n            });\n            this.cancel();\n          } else if (response.status === 'warning') {\n            this.messageService.add({\n              severity: 'warn',\n              summary: 'Warning',\n              detail: response.results\n            });\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.results\n            });\n          }\n        },\n        error: error => {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to process payment'\n          });\n        }\n      });\n    }\n    cancel() {\n      this.router.navigate(['/loan/schedule-payment']);\n    }\n    getPaymentLength() {\n      return this.formdata.reduce((total, dealer) => {\n        return total + (dealer.dtoList?.length || 0);\n      }, 0);\n    }\n    removeFee(dealer, feeItem) {\n      if (dealer && dealer.dealerLevelFeeList) {\n        const index = dealer.dealerLevelFeeList.findIndex(item => item.description === feeItem.description && item.remainingAmount === feeItem.remainingAmount);\n        if (index > -1) {\n          dealer.dealerLevelFeeList.splice(index, 1);\n          this.recalculateTotal(dealer);\n        }\n      }\n    }\n    recalculateTotal(dealer) {\n      const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total, fee) => {\n        return total + (fee.remainingAmount || 0);\n      }, 0);\n      dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\n    }\n    // 处理仅本金支付的金额变更\n    onlyPrincipalAmountChange(item) {\n      if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\n        if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n          this.messageService.add({\n            severity: 'warning',\n            summary: 'Warning',\n            detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\n          });\n          item.onlyPrincialAmount = 0;\n        }\n      } else {\n        if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\n          this.messageService.add({\n            severity: 'warning',\n            summary: 'Warning',\n            detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\n          });\n          item.onlyPrincialAmount = 0;\n        }\n      }\n    }\n    // 打开Other Amount输入对话框\n    inputOtherAmount(item) {\n      this.selectedItem = item;\n      this.tempOtherAmount = item.otherAmount || 0;\n      this.showOtherAmountDialog = true;\n    }\n    // 取消Other Amount输入\n    cancelOtherAmount() {\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n    }\n    // 确认Other Amount输入\n    confirmOtherAmount() {\n      if (this.selectedItem) {\n        this.selectedItem.otherAmount = this.tempOtherAmount;\n        if (this.tempOtherAmount !== 0) {\n          this.selectedItem.buttonName = 'OtherAmount';\n        } else {\n          this.selectedItem.buttonName = 'Curtailment';\n        }\n      }\n      this.showOtherAmountDialog = false;\n      this.selectedItem = null;\n      this.tempOtherAmount = 0;\n    }\n    // 获取最小日期\n    getMinDate(item) {\n      return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\n    }\n    // 获取最大日期\n    getMaxDate(item) {\n      return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\n    }\n    // Add Contact Dialog\n    addContactDialog(item) {\n      this.selectedContactItem = item;\n      this.contactDialogMode = 'add';\n      this.contactForm = {\n        contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\n        firstName: '',\n        lastName: '',\n        addressLine1: '',\n        addressLine2: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        phone: '',\n        email: ''\n      };\n      this.showContactDialog = true;\n    }\n    // Edit Contact Dialog\n    editContactDialog(contactInfo, item) {\n      this.selectedContactItem = item;\n      this.contactDialogMode = 'edit';\n      this.editingContact = contactInfo;\n      this.contactForm = {\n        contactReference: contactInfo.contactReference,\n        firstName: contactInfo.firstName,\n        lastName: contactInfo.lastName,\n        addressLine1: contactInfo.addressLine1,\n        addressLine2: contactInfo.addressLine2 || '',\n        city: contactInfo.city,\n        state: contactInfo.state,\n        zipCode: contactInfo.zipCode,\n        phone: contactInfo.phone,\n        email: contactInfo.email || ''\n      };\n      this.showContactDialog = true;\n    }\n    // Save Contact\n    saveContact() {\n      if (this.contactDialogMode === 'add') {\n        const newContact = {\n          ...this.contactForm\n        };\n        // Add to contact list\n        if (this.selectedContactItem.contactDtoList) {\n          this.selectedContactItem.contactDtoList.push(newContact);\n        } else {\n          this.selectedContactItem.contactDtoList = [newContact];\n        }\n        // Set as current contact\n        this.selectedContactItem.contactInfo = newContact;\n        // Update all loans for same dealer\n        this.formdata.forEach(dealer => {\n          if (dealer.dealerId === this.selectedContactItem.dealerId) {\n            dealer.dtoList?.forEach(loan => {\n              if (loan.contactDtoList) {\n                loan.contactDtoList.push({\n                  ...newContact\n                });\n                if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n                  loan.isDisabledEdit = true;\n                }\n              }\n            });\n          }\n        });\n      } else {\n        // Edit mode\n        const updatedContact = {\n          ...this.contactForm\n        };\n        // Update in all places\n        this.formdata.forEach(dealer => {\n          if (dealer.dealerId === this.selectedContactItem.dealerId) {\n            dealer.dtoList?.forEach(loan => {\n              if (loan.contactDtoList) {\n                // Update in contact list\n                const index = loan.contactDtoList.findIndex(c => c.contactReference === this.editingContact.contactReference);\n                if (index > -1) {\n                  loan.contactDtoList[index] = updatedContact;\n                }\n                // Update current selection if matches\n                if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\n                  loan.contactInfo = updatedContact;\n                }\n              }\n            });\n          }\n        });\n      }\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.editingContact = null;\n    }\n    // Cancel Contact Dialog\n    cancelContactDialog() {\n      if (this.contactDialogMode === 'add') {\n        this.tempContactNumber--;\n      }\n      this.showContactDialog = false;\n      this.selectedContactItem = null;\n      this.editingContact = null;\n    }\n    // Handle shipping contact change\n    shippingContactChange(item) {\n      if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\n        item.isDisabledEdit = true;\n      } else {\n        item.isDisabledEdit = false;\n      }\n    }\n    // Remove payoff item\n    removePayoff(dealer, item) {\n      if (dealer.dtoList) {\n        const index = dealer.dtoList.findIndex(dto => dto === item);\n        if (index > -1) {\n          dealer.dtoList.splice(index, 1);\n        }\n      }\n    }\n    static #_ = this.ɵfac = function LoanProceedComponent_Factory(t) {\n      return new (t || LoanProceedComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.LoanSchedulePaymentService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoanProceedComponent,\n      selectors: [[\"app-loan-proceed\"]],\n      features: [i0.ɵɵProvidersFeature([MessageService])],\n      decls: 58,\n      vars: 40,\n      consts: [[1, \"flex\", \"flex-column\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"flex\"], [1, \"colorce3434\", \"font-bold\", \"pl-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"greyButton\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Submit\", 1, \"greenButton\", 3, \"click\", \"disabled\"], [\"class\", \"panel border-round p-3 text-sm flex flex-column\", 4, \"ngFor\", \"ngForOf\"], [\"header\", \"Other Amount\", 3, \"visibleChange\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"closeOnEscape\", \"closable\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-3\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [\"for\", \"otherAmount\"], [\"id\", \"otherAmount\", \"mode\", \"currency\", \"currency\", \"USD\", 3, \"ngModelChange\", \"ngModel\", \"minFractionDigits\", \"maxFractionDigits\", \"maxlength\", \"max\"], [\"pTemplate\", \"footer\"], [3, \"visibleChange\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"header\", \"closeOnEscape\", \"closable\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"for\", \"firstName\"], [\"pInputText\", \"\", \"id\", \"firstName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"lastName\"], [\"pInputText\", \"\", \"id\", \"lastName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"addressLine1\"], [\"pInputText\", \"\", \"id\", \"addressLine1\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"addressLine2\"], [\"pInputText\", \"\", \"id\", \"addressLine2\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"city\"], [\"pInputText\", \"\", \"id\", \"city\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"state\"], [\"id\", \"state\", \"optionLabel\", \"text\", \"optionValue\", \"value\", \"placeholder\", \"Select\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"showClear\"], [\"for\", \"zipCode\"], [\"pInputText\", \"\", \"id\", \"zipCode\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"phone\"], [\"pInputText\", \"\", \"id\", \"phone\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"email\"], [\"pInputText\", \"\", \"id\", \"email\", 3, \"ngModelChange\", \"ngModel\"], [1, \"panel\", \"border-round\", \"p-3\", \"text-sm\", \"flex\", \"flex-column\"], [1, \"flex\", \"align-items-center\", \"flex-1\"], [1, \"flex\", \"w-3\"], [1, \"text-right\", \"color2B2E3A\", \"pr-2\"], [1, \"pl-2\", \"color2B2E3A\", \"font-bold\"], [1, \"pl-2\", \"font-bold\", \"colorce3434\"], [1, \"flex\", \"w-3\", \"align-items-center\"], [\"placeholder\", \"Select\", \"filterBy\", \"reference\", 1, \"dropdownStyle\", \"pl-2\", \"w-15rem\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"showClear\", \"optionLabel\", \"optionValue\", \"filter\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"src\", \"./assets/img/upicon.png\", 1, \"cursor-pointer\"], [\"class\", \"panel border-round p-3 flex mt-3 color2B2E3A relative\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-ellipsis\"], [1, \"panel\", \"border-round\", \"p-3\", \"flex\", \"mt-3\", \"color2B2E3A\", \"relative\"], [1, \"absolute\", \"closeIcon\", \"z-1\"], [1, \"pi\", \"pi-times-circle\", \"text-xl\", \"colorce3434\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"w-full\"], [1, \"flex\", \"w-4\"], [1, \"pl-2\"], [1, \"flex\", \"w-8\"], [1, \"pl-2\", \"colorce3434\"], [1, \"w-3\", \"flex\", \"flex-column\", \"gap-3\", \"p-3\", \"border-right-1\", \"border-color-AEB9CC\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"font-bold\"], [1, \"pi\", \"pi-car\"], [1, \"color2B2E3A\"], [1, \"w-4\", \"flex\", \"flex-column\", \"gap-3\", \"py-3\", \"px-5\", \"border-right-1\", \"border-color-AEB9CC\"], [1, \"pi\", \"pi-book\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"pl-3\"], [\"class\", \"pi pi-angle-down cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"pi pi-angle-up cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"flex flex-column trbg border-round-sm color82808F\", 4, \"ngIf\"], [\"class\", \"flex justify-content-between align-items-center\", 4, \"ngIf\"], [1, \"w-6\", \"pl-3\"], [1, \"w-6\"], [1, \"calendarStyle\", \"w-full\", 3, \"ngModelChange\", \"onSelect\", \"ngModel\", \"selectOtherMonths\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"dateFormat\", \"showTime\", \"showIcon\", \"readonlyInput\", \"minDate\", \"maxDate\", \"disabled\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [\"class\", \"flex flex-column gap-3\", 4, \"ngIf\"], [\"class\", \"w-5 flex flex-column gap-3 py-3 px-5\", 4, \"ngIf\"], [1, \"pi\", \"pi-angle-down\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-angle-up\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"flex-column\", \"trbg\", \"border-round-sm\", \"color82808F\"], [1, \"flex\", \"flex-column\", \"w-12\", \"p-3\", \"gap-3\"], [\"class\", \"flex justify-content-between align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"class\", \"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\", \"pTooltip\", \"Estimated Amount\", \"tooltipPosition\", \"top\", 4, \"ngIf\"], [\"pTooltip\", \"Estimated Amount\", \"tooltipPosition\", \"top\", 1, \"pi\", \"pi-question\", \"text-xs\", \"color4B78E8FF\", \"border-1\", \"border-round-3xl\", \"questionIcon\"], [1, \"text-primary\", \"cursor-pointer\", 3, \"click\"], [1, \"w-12\", \"flex\", \"gap-2\", \"justify-content-between\", \"align-items-center\"], [\"inputId\", \"principalOnly\", 1, \"w-6\", 3, \"ngModelChange\", \"ngModel\", \"binary\"], [1, \"w-full\"], [\"inputId\", \"onlyPrincialAmount\", \"mode\", \"decimal\", 1, \"inputNumberRadius\", \"w-full\", 3, \"ngModelChange\", \"minFractionDigits\", \"maxFractionDigits\", \"ngModel\", \"maxlength\"], [1, \"calendarStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\", \"selectOtherMonths\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"dateFormat\"], [1, \"w-5\", \"flex\", \"flex-column\", \"gap-3\", \"py-3\", \"px-5\"], [1, \"pi\", \"pi-envelope\"], [1, \"flex\", \"align-items-center\"], [1, \"w-4\"], [1, \"w-8\", \"flex\", \"gap-6\"], [1, \"w-8\"], [1, \"calendarStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"selectOtherMonths\", \"showButtonBar\", \"monthNavigator\", \"yearNavigator\", \"readonlyInput\", \"dateFormat\"], [1, \"flex\", \"gap-2\", \"align-items-center\"], [\"inputId\", \"holdCheckbox\", 3, \"ngModelChange\", \"ngModel\", \"binary\"], [\"class\", \"flex align-items-center\", 4, \"ngIf\"], [\"optionLabel\", \"text\", \"placeholder\", \"Select\", 1, \"dropdownStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"showClear\"], [1, \"flex\", \"align-items-start\"], [4, \"ngIf\"], [1, \"w-8\", \"flex\", \"gap-2\"], [\"optionLabel\", \"contactReference\", \"placeholder\", \"Select\", 1, \"dropdownStyle\", \"w-full\", 3, \"ngModelChange\", \"onChange\", \"ngModel\", \"options\", \"showClear\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\", \"disabled\"], [\"optionLabel\", \"firstName\", \"placeholder\", \"Select\", 1, \"dropdownStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"showClear\"], [\"optionLabel\", \"address1\", \"placeholder\", \"Select\", 1, \"dropdownStyle\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"options\", \"showClear\"], [1, \"m-0\"], [\"class\", \"m-0\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"flex\", \"justify-content-end\", \"gap-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Confirm\", 1, \"greenButton\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Save\", 1, \"greenButton\", 3, \"click\"]],\n      template: function LoanProceedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"Total: \");\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"currency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function LoanProceedComponent_Template_button_click_8_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoanProceedComponent_Template_button_click_9_listener() {\n            return ctx.pay();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, LoanProceedComponent_div_10_Template, 28, 13, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-dialog\", 8);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoanProceedComponent_Template_p_dialog_visibleChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.showOtherAmountDialog, $event) || (ctx.showOtherAmountDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p-inputNumber\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_p_inputNumber_ngModelChange_16_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.tempOtherAmount, $event) || (ctx.tempOtherAmount = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, LoanProceedComponent_ng_template_17_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p-dialog\", 14);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoanProceedComponent_Template_p_dialog_visibleChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.showContactDialog, $event) || (ctx.showContactDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 15)(21, \"div\", 10)(22, \"label\", 16);\n          i0.ɵɵtext(23, \"First Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.firstName, $event) || (ctx.contactForm.firstName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 10)(26, \"label\", 18);\n          i0.ɵɵtext(27, \"Last Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.lastName, $event) || (ctx.contactForm.lastName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 10)(30, \"label\", 20);\n          i0.ɵɵtext(31, \"Address Line 1 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"input\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_32_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.addressLine1, $event) || (ctx.contactForm.addressLine1 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 10)(34, \"label\", 22);\n          i0.ɵɵtext(35, \"Address Line 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"input\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_36_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.addressLine2, $event) || (ctx.contactForm.addressLine2 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 10)(38, \"label\", 24);\n          i0.ɵɵtext(39, \"City *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_40_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.city, $event) || (ctx.contactForm.city = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 10)(42, \"label\", 26);\n          i0.ɵɵtext(43, \"State *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p-dropdown\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_p_dropdown_ngModelChange_44_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.state, $event) || (ctx.contactForm.state = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 10)(46, \"label\", 28);\n          i0.ɵɵtext(47, \"Zip Code *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"input\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_48_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.zipCode, $event) || (ctx.contactForm.zipCode = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 10)(50, \"label\", 30);\n          i0.ɵɵtext(51, \"Phone *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"input\", 31);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_52_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.phone, $event) || (ctx.contactForm.phone = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 10)(54, \"label\", 32);\n          i0.ɵɵtext(55, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"input\", 33);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoanProceedComponent_Template_input_ngModelChange_56_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.contactForm.email, $event) || (ctx.contactForm.email = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(57, LoanProceedComponent_ng_template_57_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 36, ctx.getTotal()));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.getPaymentLength() === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.formdata);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(38, _c0));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showOtherAmountDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"closeOnEscape\", false)(\"closable\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tempOtherAmount);\n          i0.ɵɵproperty(\"minFractionDigits\", 2)(\"maxFractionDigits\", 2)(\"maxlength\", 14)(\"max\", ctx.selectedItem == null ? null : ctx.selectedItem.otherAmountLimit);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(39, _c1));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showContactDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"header\", ctx.contactDialogMode === \"add\" ? \"Add Contact\" : \"Edit Contact\")(\"closeOnEscape\", false)(\"closable\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.firstName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.addressLine1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.addressLine2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.city);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.state);\n          i0.ɵɵproperty(\"options\", ctx.stateList)(\"showClear\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.zipCode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.phone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.contactForm.email);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ButtonDirective, i3.PrimeTemplate, i6.Checkbox, i7.InputText, i8.Tooltip, i9.Dialog, i10.Calendar, i11.Dropdown, i12.InputNumber, i13.DefaultValueAccessor, i13.NgControlStatus, i13.RequiredValidator, i13.MaxLengthValidator, i13.NgModel, i14.InputGroup, i15.InputGroupAddon, i16.Ripple, i4.CurrencyPipe, i4.DatePipe],\n      styles: [\".questionIcon[_ngcontent-%COMP%] {\\n  padding: 2px;\\n}\\n\\n.closeIcon[_ngcontent-%COMP%] {\\n  right: 20px;\\n}\\n\\n  .p-calendar .p-datepicker {\\n  width: 360px !important;\\n}\\n\\n[_nghost-%COMP%]     .inputNumberRadius .p-inputtext {\\n  border-radius: 0 !important;\\n  font-size: 14px;\\n}\\n\\n[_nghost-%COMP%]     .inputNumberBorder .p-inputtext {\\n  border-right: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbG9hbi9zY2hlZHVsZS1wYXltZW50L2xvYW4tcHJvY2VlZC9sb2FuLXByb2NlZWQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0FBQ0Y7O0FBQ0E7RUFDRSxXQUFBO0FBRUY7O0FBQ0E7RUFDRSx1QkFBQTtBQUVGOztBQUNBO0VBQ0UsMkJBQUE7RUFDQSxlQUFBO0FBRUY7O0FBQ0E7RUFDRSxlQUFBO0FBRUYiLCJzb3VyY2VzQ29udGVudCI6WyIucXVlc3Rpb25JY29ue1xyXG4gIHBhZGRpbmc6MnB4O1xyXG59XHJcbi5jbG9zZUljb257XHJcbiAgcmlnaHQ6MjBweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5wLWNhbGVuZGFyIC5wLWRhdGVwaWNrZXIge1xyXG4gIHdpZHRoOiAzNjBweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG46aG9zdCA6Om5nLWRlZXAgLmlucHV0TnVtYmVyUmFkaXVzIC5wLWlucHV0dGV4dCB7XHJcbiAgYm9yZGVyLXJhZGl1czogMCAhaW1wb3J0YW50O1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxufVxyXG5cclxuOmhvc3QgOjpuZy1kZWVwIC5pbnB1dE51bWJlckJvcmRlciAucC1pbnB1dHRleHQge1xyXG4gIGJvcmRlci1yaWdodDogMDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return LoanProceedComponent;\n})();", "map": {"version": 3, "names": ["moment", "MessageService", "i0", "ɵɵtext", "ɵɵtextInterpolate2", "item_r3", "bankAccountRanking", "reference", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "item_r4", "ɵɵlistener", "LoanProceedComponent_div_10_div_26_Template_i_click_2_listener", "feeItem_r6", "ɵɵrestoreView", "_r5", "$implicit", "dealer_r2", "ɵɵnextContext", "ctx_r6", "ɵɵresetView", "removeFee", "ɵɵtextInterpolate", "description", "ɵɵpipeBind1", "remainingAmount", "LoanProceedComponent_div_10_div_27_i_23_Template_i_click_0_listener", "_r10", "show<PERSON><PERSON>off", "LoanProceedComponent_div_10_div_27_i_24_Template_i_click_0_listener", "_r11", "fee_r12", "feeName", "amount", "ɵɵelement", "ɵɵtemplate", "LoanProceedComponent_div_10_div_27_div_29_div_8_Template", "LoanProceedComponent_div_10_div_27_div_29_i_13_Template", "LoanProceedComponent_div_10_div_27_div_29_i_20_Template", "item_r9", "principal", "ɵɵproperty", "extraAmountList", "isShowEstimation", "ɵɵtextInterpolate1", "interestPrice", "insurancePrice", "LoanProceedComponent_div_10_div_27_div_30_Template_a_click_7_listener", "_r13", "inputOtherAmount", "otherAmount", "ɵɵtwoWayListener", "LoanProceedComponent_div_10_div_27_div_36_Template_p_checkbox_ngModelChange_4_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "isOnlyPrincial", "ɵɵtwoWayProperty", "LoanProceedComponent_div_10_div_27_div_37_Template_p_inputNumber_ngModelChange_8_listener", "_r15", "onlyPrincialAmount", "onlyPrincipalAmountChange", "LoanProceedComponent_div_10_div_27_div_37_Template_p_calendar_ngModelChange_13_listener", "scheduleDate", "LoanProceedComponent_div_10_div_27_div_38_div_14_Template_p_dropdown_ngModelChange_4_listener", "_r17", "holdType", "holdTypeList", "LoanProceedComponent_div_10_div_27_div_38_div_20_Template_p_dropdown_ngModelChange_4_listener", "_r18", "contactInfo", "LoanProceedComponent_div_10_div_27_div_38_div_20_Template_p_dropdown_onChange_4_listener", "shippingContactChange", "LoanProceedComponent_div_10_div_27_div_38_div_20_Template_button_click_5_listener", "addContactDialog", "LoanProceedComponent_div_10_div_27_div_38_div_20_Template_button_click_6_listener", "editContactDialog", "contactDtoList", "isDisabledEdit", "LoanProceedComponent_div_10_div_27_div_38_div_21_Template_p_dropdown_ngModelChange_4_listener", "_r19", "newContactInfo", "newContactDtoList", "LoanProceedComponent_div_10_div_27_div_38_div_22_Template_p_dropdown_ngModelChange_4_listener", "_r20", "newLocationInfo", "newLocationDtoList", "addressLine2", "LoanProceedComponent_div_10_div_27_div_38_div_27_p_5_Template", "firstName", "lastName", "addressLine1", "ɵɵtextInterpolate3", "city", "state", "zipCode", "address2", "LoanProceedComponent_div_10_div_27_div_38_div_28_p_5_Template", "address1", "LoanProceedComponent_div_10_div_27_div_38_Template_p_calendar_ngModelChange_9_listener", "_r16", "titleReleaseDate", "LoanProceedComponent_div_10_div_27_div_38_Template_p_checkbox_ngModelChange_13_listener", "isHold", "LoanProceedComponent_div_10_div_27_div_38_div_14_Template", "LoanProceedComponent_div_10_div_27_div_38_Template_p_dropdown_ngModelChange_19_listener", "mailFeeInfo", "LoanProceedComponent_div_10_div_27_div_38_div_20_Template", "LoanProceedComponent_div_10_div_27_div_38_div_21_Template", "LoanProceedComponent_div_10_div_27_div_38_div_22_Template", "LoanProceedComponent_div_10_div_27_div_38_div_27_Template", "LoanProceedComponent_div_10_div_27_div_38_div_28_Template", "holdSwitch", "postageFee", "contactSwitch", "LoanProceedComponent_div_10_div_27_Template_i_click_2_listener", "_r8", "<PERSON><PERSON><PERSON><PERSON>", "LoanProceedComponent_div_10_div_27_i_23_Template", "LoanProceedComponent_div_10_div_27_i_24_Template", "LoanProceedComponent_div_10_div_27_div_29_Template", "LoanProceedComponent_div_10_div_27_div_30_Template", "LoanProceedComponent_div_10_div_27_Template_p_calendar_ngModelChange_35_listener", "LoanProceedComponent_div_10_div_27_Template_p_calendar_onSelect_35_listener", "scheduleDateChange", "LoanProceedComponent_div_10_div_27_div_36_Template", "LoanProceedComponent_div_10_div_27_div_37_Template", "LoanProceedComponent_div_10_div_27_div_38_Template", "LoanProceedComponent_div_10_div_27_div_39_Template", "vin", "year", "make", "model", "ɵɵpipeBind2", "nextDueDate", "payoff", "buttonName", "totalMoney", "is<PERSON>ayOff", "isPartialPayment", "getMinDate", "getMaxDate", "isShowPrincipal", "displayMail", "isTrusted", "LoanProceedComponent_div_10_Template_p_dropdown_ngModelChange_22_listener", "_r1", "bankAccount", "LoanProceedComponent_div_10_ng_template_23_Template", "LoanProceedComponent_div_10_ng_template_24_Template", "LoanProceedComponent_div_10_div_26_Template", "LoanProceedComponent_div_10_div_27_Template", "dealerCode", "dba", "getSubTotal", "bankAccountList", "dealerLevelFeeList", "dtoList", "LoanProceedComponent_ng_template_17_Template_button_click_1_listener", "_r21", "cancelOtherAmount", "LoanProceedComponent_ng_template_17_Template_button_click_2_listener", "confirmOtherAmount", "LoanProceedComponent_ng_template_57_Template_button_click_1_listener", "_r22", "cancelContactDialog", "LoanProceedComponent_ng_template_57_Template_button_click_2_listener", "saveContact", "LoanProceedComponent", "constructor", "router", "route", "loanSchedulePaymentService", "messageService", "formdata", "stateList", "uccProviderList", "showOtherAmountDialog", "selectedItem", "tempOtherAmount", "calendarConfig", "showButtonBar", "monthNavigator", "yearNavigator", "dateFormat", "showTime", "showIcon", "readonlyInput", "appendTo", "showContactDialog", "selectedContactItem", "contactDialogMode", "editingContact", "tempContactNumber", "contactForm", "contactReference", "phone", "email", "ngOnInit", "mockData", "selectData", "data", "JSON", "stringify", "add", "severity", "summary", "detail", "navigate", "paymentData", "parse", "selectDealer<PERSON>ee", "getMakePaymentInfo", "subscribe", "next", "response", "code", "results", "releaseHoldTypeList", "releaseHoldSwitch", "for<PERSON>ach", "dealer", "dto", "newLocationDto", "newContactDto", "releaseDate", "Date", "delayDays", "format", "holdTypes", "filter", "c", "value", "length", "toDate", "scheduleDateEnd", "currentDate", "paymentSource", "matchingAccount", "find", "account", "bankAccountDtoId", "bankAccountId", "message", "error", "console", "getTotal", "reduce", "total", "dealerTotal", "dealerInfo", "item", "fee", "getDatePickerConfig", "config", "minDate", "defaultDate", "maxDate", "log", "now", "diffDays", "diff", "diffInterest", "interestDaily", "diffInsurance", "insuranceDaily", "interestPriceTemp", "insurancePriceTemp", "tempTotalMoney", "pay", "isContinue", "isOnlyPrincipalSave", "holdContactInfo", "editMakePayment", "status", "cancel", "getPaymentLength", "feeItem", "index", "findIndex", "splice", "recalculateTotal", "dealerLevelFeeTotal", "parseFloat", "payOffPrincipal", "newContact", "push", "dealerId", "loan", "includes", "updatedContact", "_", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "LoanSchedulePaymentService", "i3", "_2", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "LoanProceedComponent_Template", "rf", "ctx", "LoanProceedComponent_Template_button_click_8_listener", "LoanProceedComponent_Template_button_click_9_listener", "LoanProceedComponent_div_10_Template", "LoanProceedComponent_Template_p_dialog_visibleChange_11_listener", "LoanProceedComponent_Template_p_inputNumber_ngModelChange_16_listener", "LoanProceedComponent_ng_template_17_Template", "LoanProceedComponent_Template_p_dialog_visibleChange_18_listener", "LoanProceedComponent_Template_input_ngModelChange_24_listener", "LoanProceedComponent_Template_input_ngModelChange_28_listener", "LoanProceedComponent_Template_input_ngModelChange_32_listener", "LoanProceedComponent_Template_input_ngModelChange_36_listener", "LoanProceedComponent_Template_input_ngModelChange_40_listener", "LoanProceedComponent_Template_p_dropdown_ngModelChange_44_listener", "LoanProceedComponent_Template_input_ngModelChange_48_listener", "LoanProceedComponent_Template_input_ngModelChange_52_listener", "LoanProceedComponent_Template_input_ngModelChange_56_listener", "LoanProceedComponent_ng_template_57_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "otherAmountLimit", "_c1"], "sources": ["D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-proceed\\loan-proceed.component.ts", "D:\\workspace\\flooring\\flooring-nighthawk-website-new\\ui\\src\\app\\pages\\loan\\schedule-payment\\loan-proceed\\loan-proceed.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport moment from 'moment';\r\nimport { LoanSchedulePaymentService } from '../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-loan-proceed',\r\n  templateUrl: './loan-proceed.component.html',\r\n  styleUrl: './loan-proceed.component.scss',\r\n  providers: [MessageService]\r\n})\r\nexport class LoanProceedComponent implements OnInit {\r\n  payoff: boolean = false;\r\n  formdata: any[] = [];\r\n  postageFee: any[] = [];\r\n  stateList: any[] = [];\r\n  holdTypeList: any[] = [];\r\n  uccProviderList: any[] = [];\r\n  holdSwitch: boolean = false;\r\n  isShowEstimation: boolean = false;\r\n\r\n  // Additional Payment dialog variables\r\n  showOtherAmountDialog: boolean = false;\r\n  selectedItem: any = null;\r\n  tempOtherAmount: number = 0;\r\n\r\n  // Calendar configuration\r\n  calendarConfig: any = {\r\n    showButtonBar: true,\r\n    monthNavigator: true,\r\n    yearNavigator: true,\r\n    dateFormat: 'mm/dd/yy',\r\n    showTime: false,\r\n    showIcon: false,\r\n    readonlyInput: true,\r\n    appendTo: 'body'\r\n  };\r\n\r\n  // Contact Dialog\r\n  showContactDialog: boolean = false;\r\n  selectedContactItem: any = null;\r\n  contactDialogMode: 'add' | 'edit' = 'add';\r\n  editingContact: any = null;\r\n  tempContactNumber: number = 0;\r\n\r\n  // Contact Form\r\n  contactForm = {\r\n    contactReference: '',\r\n    firstName: '',\r\n    lastName: '',\r\n    addressLine1: '',\r\n    addressLine2: '',\r\n    city: '',\r\n    state: '',\r\n    zipCode: '',\r\n    phone: '',\r\n    email: ''\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private loanSchedulePaymentService: LoanSchedulePaymentService,\r\n    private messageService: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // 临时使用mock数据替代路由参数\r\n    const mockData = {\r\n      selectData: [\r\n        {\r\n          \"loanId\": \"937f86a5-b25a-4a1b-98fe-bef8e61a1ff5\",\r\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\r\n          \"vDealerLevelDto\": null,\r\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\r\n          \"accountNumber\": 351764,\r\n          \"cost\": 2000,\r\n          \"currentCost\": 2000,\r\n          \"sold\": \"\",\r\n          \"soldOperationType\": null,\r\n          \"soldDisp\": \"\",\r\n          \"financeTag\": null,\r\n          \"assetId\": null,\r\n          \"titleStatus\": \"RE\",\r\n          \"titleStatusDisplay\": \"Trusted Title Received\",\r\n          \"interestDaily\": 0,\r\n          \"insuranceDaily\": 0,\r\n          \"interestPrice\": 0,\r\n          \"insurancePrice\": 0,\r\n          \"interestPriceTemp\": 0,\r\n          \"insurancePriceTemp\": 0,\r\n          \"isOnlyPrincial\": false,\r\n          \"onlyPrincialAmount\": 0,\r\n          \"isPastDue\": true,\r\n          \"year\": 2022,\r\n          \"fromDealerView\": null,\r\n          \"isScheduled\": false,\r\n          \"make\": \"BMW\",\r\n          \"model\": \"320Li\",\r\n          \"vin\": \"2GCEK19J245287165\",\r\n          \"titleReleaseDate\": null,\r\n          \"delayDays\": 0,\r\n          \"titleReleaseHoldDate\": null,\r\n          \"holdType\": null,\r\n          \"uccProviderDto\": null,\r\n          \"uccProviderId\": null,\r\n          \"titleNote\": null,\r\n          \"isHasTitleFile\": false,\r\n          \"fileManagementUrl\": null,\r\n          \"contactSwitch\": false,\r\n          \"contactDtoList\": null,\r\n          \"contactDto\": null,\r\n          \"newLocationDtoList\": null,\r\n          \"newLocationDto\": null,\r\n          \"newContactDtoList\": null,\r\n          \"newContactDto\": null,\r\n          \"vinLast6\": null,\r\n          \"maturituDate\": \"2023-02-28T00:00:00\",\r\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\r\n          \"dueDate\": \"0001-01-01T00:00:00\",\r\n          \"isHold\": false,\r\n          \"isTrusted\": false,\r\n          \"payOff\": 90,\r\n          \"isPayOff\": true,\r\n          \"isPartialPayment\": false,\r\n          \"isCurtailment\": false,\r\n          \"isShowPrincipal\": false,\r\n          \"curtailment\": 0,\r\n          \"otherAmount\": 0,\r\n          \"otherAmountDisplay\": 0,\r\n          \"paidAmount\": 90,\r\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\r\n          \"scheduleDateMessage\": null,\r\n          \"currentDate\": null,\r\n          \"scheduleDateEnd\": null,\r\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"termId\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197\",\r\n          \"termNo\": 3,\r\n          \"dealerCode\": \"160625\",\r\n          \"dealerName\": null,\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"dba\": \"MY160625\",\r\n          \"legalName\": \"MY160625\",\r\n          \"buttonName\": \"PayOff\",\r\n          \"termIdListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\r\n          \"principalListStr\": \"16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,\",\r\n          \"principalInterest\": null,\r\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\r\n          \"payOffPrincipal\": 0,\r\n          \"otherAmountLimit\": 0,\r\n          \"payOffTermFee\": 0,\r\n          \"payOffFeeItem\": 0,\r\n          \"payOffInterest\": 0,\r\n          \"payOffInsurance\": 0,\r\n          \"partPrincipal\": 0,\r\n          \"partTermFee\": 0,\r\n          \"partFeeItem\": 0,\r\n          \"partInterest\": 0,\r\n          \"partInsurance\": 0,\r\n          \"mailFee\": 0,\r\n          \"mailFeeName\": null,\r\n          \"principal\": 0,\r\n          \"titleName\": null,\r\n          \"titleContent\": null,\r\n          \"titleShoping\": null,\r\n          \"titlepostage\": null,\r\n          \"contactsId\": null,\r\n          \"newLocationId\": null,\r\n          \"newContactId\": null,\r\n          \"provincialMoney\": 0,\r\n          \"totalMoney\": 0,\r\n          \"tempTotalMoney\": 0,\r\n          \"subtotal\": 0,\r\n          \"chargeDay\": 0,\r\n          \"chargeDayMoneyInterest\": 0,\r\n          \"chargeDayMoneyInsurance\": 0,\r\n          \"stockId\": 214,\r\n          \"extraAmountMoney\": 0,\r\n          \"displayMail\": false,\r\n          \"vehicle\": \"287165-BMW 320Li 2022\",\r\n          \"feeAmountNoReserve\": 90,\r\n          \"reserveFee\": 0,\r\n          \"extraAmountList\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPayoff\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPartial\": [\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 50,\r\n              \"tempAmount\": 50,\r\n              \"feeId\": null,\r\n              \"feeName\": \"TERM_3\",\r\n              \"createDate\": null,\r\n              \"amount\": 50,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 0,\r\n              \"tempAmount\": 0,\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 0,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 15,\r\n              \"tempAmount\": 15,\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"payInfoList\": null,\r\n          \"loanStatus\": \"L\",\r\n          \"isDuePayOff\": false,\r\n          \"isSoldPayOff\": false,\r\n          \"nsfCount\": 0,\r\n          \"waivedAmount\": 0,\r\n          \"extensionHide\": false,\r\n          \"paidPrinciple\": 0,\r\n          \"paidTermFee\": 0,\r\n          \"paidFee\": 0,\r\n          \"paidPcr\": 0,\r\n          \"paidInterest\": 0,\r\n          \"paidInsurance\": 0,\r\n          \"otherAmountDisable\": true\r\n        },\r\n        {\r\n          \"loanId\": \"88f81a91-b4c3-491d-a511-05d95bd05103\",\r\n          \"creditLineId\": \"36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2\",\r\n          \"vDealerLevelDto\": null,\r\n          \"creditLineBucketId\": \"878b52e6-5d7c-4361-8c3b-5b221a2a27fd\",\r\n          \"accountNumber\": 352478,\r\n          \"cost\": 2000,\r\n          \"currentCost\": 2000,\r\n          \"sold\": \"\",\r\n          \"soldOperationType\": \"\",\r\n          \"soldDisp\": \"\",\r\n          \"financeTag\": \"\",\r\n          \"assetId\": null,\r\n          \"titleStatus\": \"PT\",\r\n          \"titleStatusDisplay\": \"Pending Release Trusted\",\r\n          \"interestDaily\": 0,\r\n          \"insuranceDaily\": 0,\r\n          \"interestPrice\": 0,\r\n          \"insurancePrice\": 0,\r\n          \"interestPriceTemp\": 0,\r\n          \"insurancePriceTemp\": 0,\r\n          \"isOnlyPrincial\": false,\r\n          \"onlyPrincialAmount\": 0,\r\n          \"isPastDue\": true,\r\n          \"year\": 2023,\r\n          \"fromDealerView\": null,\r\n          \"isScheduled\": false,\r\n          \"make\": \"BMW\",\r\n          \"model\": \"320Li\",\r\n          \"vin\": \"2GCEK19J245287243\",\r\n          \"titleReleaseDate\": null,\r\n          \"delayDays\": 0,\r\n          \"titleReleaseHoldDate\": null,\r\n          \"holdType\": null,\r\n          \"uccProviderDto\": null,\r\n          \"uccProviderId\": null,\r\n          \"titleNote\": null,\r\n          \"isHasTitleFile\": false,\r\n          \"fileManagementUrl\": null,\r\n          \"contactSwitch\": false,\r\n          \"contactDtoList\": null,\r\n          \"contactDto\": null,\r\n          \"newLocationDtoList\": null,\r\n          \"newLocationDto\": null,\r\n          \"newContactDtoList\": null,\r\n          \"newContactDto\": null,\r\n          \"vinLast6\": null,\r\n          \"maturituDate\": \"2023-02-28T00:00:00\",\r\n          \"nextDueDate\": \"2022-12-30T00:00:00\",\r\n          \"dueDate\": \"0001-01-01T00:00:00\",\r\n          \"isHold\": false,\r\n          \"isTrusted\": false,\r\n          \"payOff\": 2313,\r\n          \"isPayOff\": true,\r\n          \"isPartialPayment\": false,\r\n          \"isCurtailment\": false,\r\n          \"isShowPrincipal\": false,\r\n          \"curtailment\": 0,\r\n          \"otherAmount\": 0,\r\n          \"otherAmountDisplay\": 0,\r\n          \"paidAmount\": 2313,\r\n          \"scheduleDate\": \"2025-06-06T00:00:00\",\r\n          \"scheduleDateMessage\": null,\r\n          \"currentDate\": null,\r\n          \"scheduleDateEnd\": null,\r\n          \"delearId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"termId\": \"6208475b-7444-403f-a1be-437d9be86200\",\r\n          \"termNo\": 3,\r\n          \"dealerCode\": \"160625\",\r\n          \"dealerName\": null,\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"dba\": \"MY160625\",\r\n          \"legalName\": \"MY160625\",\r\n          \"buttonName\": \"PayOff\",\r\n          \"termIdListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\r\n          \"principalListStr\": \"7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,\",\r\n          \"principalInterest\": null,\r\n          \"effectiveDate\": \"2023-01-31T00:00:00\",\r\n          \"payOffPrincipal\": 2000,\r\n          \"otherAmountLimit\": 0,\r\n          \"payOffTermFee\": 0,\r\n          \"payOffFeeItem\": 0,\r\n          \"payOffInterest\": 0,\r\n          \"payOffInsurance\": 0,\r\n          \"partPrincipal\": 2000,\r\n          \"partTermFee\": 0,\r\n          \"partFeeItem\": 0,\r\n          \"partInterest\": 0,\r\n          \"partInsurance\": 0,\r\n          \"mailFee\": 0,\r\n          \"mailFeeName\": null,\r\n          \"principal\": 0,\r\n          \"titleName\": null,\r\n          \"titleContent\": null,\r\n          \"titleShoping\": null,\r\n          \"titlepostage\": null,\r\n          \"contactsId\": null,\r\n          \"newLocationId\": null,\r\n          \"newContactId\": null,\r\n          \"provincialMoney\": 0,\r\n          \"totalMoney\": 0,\r\n          \"tempTotalMoney\": 0,\r\n          \"subtotal\": 0,\r\n          \"chargeDay\": 0,\r\n          \"chargeDayMoneyInterest\": 0,\r\n          \"chargeDayMoneyInsurance\": 0,\r\n          \"stockId\": 312,\r\n          \"extraAmountMoney\": 0,\r\n          \"displayMail\": false,\r\n          \"vehicle\": \"287243-BMW 320Li 2023\",\r\n          \"feeAmountNoReserve\": 65,\r\n          \"reserveFee\": 248,\r\n          \"extraAmountList\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPayoff\": [\r\n            {\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"RESERVE\",\r\n              \"feeName\": \"Reserve\",\r\n              \"createDate\": null,\r\n              \"amount\": 248,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"extraAmountListPartial\": [\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 2000,\r\n              \"tempAmount\": 2000,\r\n              \"feeId\": null,\r\n              \"feeName\": \"Principal\",\r\n              \"createDate\": null,\r\n              \"amount\": 2000,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": false,\r\n              \"fixAmount\": 0,\r\n              \"tempAmount\": 0,\r\n              \"feeId\": \"RESERVE\",\r\n              \"feeName\": \"Reserve\",\r\n              \"createDate\": null,\r\n              \"amount\": 248,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 15,\r\n              \"tempAmount\": 15,\r\n              \"feeId\": \"ADMIN_FEE\",\r\n              \"feeName\": \"Admin Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 15,\r\n              \"payFlag\": null\r\n            },\r\n            {\r\n              \"active\": true,\r\n              \"fixAmount\": 25,\r\n              \"tempAmount\": 25,\r\n              \"feeId\": \"TRUSTED_TITLE_FEE\",\r\n              \"feeName\": \"Trusted Title Fee\",\r\n              \"createDate\": null,\r\n              \"amount\": 25,\r\n              \"payFlag\": null\r\n            }\r\n          ],\r\n          \"payInfoList\": null,\r\n          \"loanStatus\": \"L\",\r\n          \"isDuePayOff\": false,\r\n          \"isSoldPayOff\": false,\r\n          \"nsfCount\": 0,\r\n          \"waivedAmount\": 0,\r\n          \"extensionHide\": false,\r\n          \"paidPrinciple\": 0,\r\n          \"paidTermFee\": 0,\r\n          \"paidFee\": 0,\r\n          \"paidPcr\": 0,\r\n          \"paidInterest\": 0,\r\n          \"paidInsurance\": 0,\r\n          \"otherAmountDisable\": true\r\n        }\r\n      ],\r\n      \"selectDealerFee\": [\r\n        {\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"paymentScheduleFeeItemId\": \"1003812b-784e-4bee-8a21-b52c6da253e0\",\r\n          \"feeName\": \"Audit Fee\",\r\n          \"description\": \"Audit Fee\",\r\n          \"remainingAmount\": 100,\r\n          \"chargedOffRemainingAmount\": 0,\r\n          \"paidStatus\": \"UP\",\r\n          \"dba\": \"MY160625\",\r\n          \"name\": \"MY160625\",\r\n          \"dealerCode\": \"160625\",\r\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\r\n          \"dueDate\": null,\r\n          \"createDate\": \"2023-06-20T09:29:05\",\r\n          \"postpaymentDealerFeeAmount\": 100,\r\n          \"removeDay\": true,\r\n          \"feeType\": \"P\"\r\n        },\r\n        {\r\n          \"dealerId\": \"8a89b13d-90ae-4fef-9a58-88eb13a7a78d\",\r\n          \"paymentScheduleFeeItemId\": \"d51e507d-c072-425d-b44a-a6005e3b7db4\",\r\n          \"feeName\": \"OC Defense Fee_CO\",\r\n          \"description\": \"OC Defense Fee_CO\",\r\n          \"remainingAmount\": 123,\r\n          \"chargedOffRemainingAmount\": 0,\r\n          \"paidStatus\": \"UP\",\r\n          \"dba\": \"MY160625\",\r\n          \"name\": \"MY160625\",\r\n          \"dealerCode\": \"160625\",\r\n          \"scheduleDate\": \"0001-01-01T00:00:00\",\r\n          \"dueDate\": null,\r\n          \"createDate\": \"2025-05-29T02:44:35\",\r\n          \"postpaymentDealerFeeAmount\": 123,\r\n          \"removeDay\": true,\r\n          \"feeType\": \"P\"\r\n        }\r\n      ]\r\n    };\r\n\r\n    const data = JSON.stringify(mockData);\r\n\r\n    if (!data) {\r\n      this.messageService.add({severity:'error', summary: 'Error', detail: 'No payment data found'});\r\n      this.router.navigate(['/loan/schedule-payment']);\r\n      return;\r\n    }\r\n\r\n    const paymentData = {\r\n      selectData: JSON.parse(data).selectData,\r\n      selectDealerFee: JSON.parse(data).selectDealerFee\r\n    };\r\n\r\n    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({\r\n      next: (response: any) => {\r\n        if (response.code === 200) {\r\n          this.formdata = response.data.results || [];\r\n          this.postageFee = response.data.postageFee || [];\r\n          this.stateList = response.data.stateList || [];\r\n          this.holdTypeList = response.data.releaseHoldTypeList || [];\r\n          this.uccProviderList = response.data.uccProviderList || [];\r\n          this.holdSwitch = response.data.releaseHoldSwitch || false;\r\n\r\n          this.formdata.forEach((dealer: any) => {\r\n            if (dealer.dtoList) {\r\n              dealer.dtoList.forEach((dto: any) => {\r\n                if (dto.isPayOff && !dto.isPartialPayment) {\r\n                  if (dto.contactSwitch) {\r\n                    dto.newLocationInfo = dto.newLocationDto;\r\n                    dto.newContactInfo = dto.newContactDto;\r\n                  } else {\r\n                    dto.contactInfo = dto.contactDtoList?.[0];\r\n                  }\r\n                  dto.mailFeeInfo = this.postageFee[0];\r\n\r\n                  const releaseDate = moment(new Date(dto.scheduleDate))\r\n                    .add(dto.delayDays, 'days')\r\n                    .format('MM/DD/YYYY');\r\n                  dto.titleReleaseDate = releaseDate;\r\n\r\n                  if (this.holdSwitch && dto.isHold) {\r\n                    const holdTypes = this.holdTypeList.filter((c: any) => c.value === 'H');\r\n                    if (holdTypes.length > 0) {\r\n                      dto.holdType = holdTypes[0];\r\n                    }\r\n                  }\r\n                }\r\n\r\n                // 转换日期格式\r\n                if(dto.scheduleDate) {\r\n                  dto.scheduleDate = moment(dto.scheduleDate).toDate();\r\n                }\r\n                if(dto.scheduleDateEnd) {\r\n                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();\r\n                }\r\n                if(dto.currentDate) {\r\n                  dto.currentDate = moment(dto.currentDate).toDate();\r\n                }\r\n\r\n                if (dealer.paymentSource && dealer.bankAccountList?.length) {\r\n                  const matchingAccount = dealer.bankAccountList.find((account: any) =>\r\n                    account.bankAccountDtoId === dealer.paymentSource.bankAccountId\r\n                  );\r\n                  if (matchingAccount) {\r\n                    dealer.bankAccount = matchingAccount.bankAccountDtoId;\r\n                  }\r\n                }\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          this.messageService.add({severity:'error', summary: 'Error', detail: response.message});\r\n          this.router.navigate(['/loan/schedule-payment']);\r\n        }\r\n      },\r\n      error: (error: Error) => {\r\n        this.messageService.add({severity:'error', summary: 'Error', detail: 'Failed to get payment info'});\r\n        console.error('Error getting payment info:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  showPayoff() {\r\n    this.payoff = !this.payoff;\r\n  }\r\n\r\n  getTotal(): number {\r\n    return this.formdata.reduce((total, dealer) => {\r\n      const dealerTotal = this.getSubTotal(dealer);\r\n      return total + dealerTotal;\r\n    }, 0);\r\n  }\r\n\r\n  getSubTotal(dealerInfo: any): number {\r\n    let total = 0;\r\n    if (dealerInfo.dtoList) {\r\n      dealerInfo.dtoList.forEach((item: any) => {\r\n        total += item.totalMoney || 0;\r\n        if (item.extraAmountList) {\r\n          item.extraAmountList.forEach((fee: any) => {\r\n            total += fee.amount || 0;\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    if (dealerInfo.dealerLevelFeeList) {\r\n      dealerInfo.dealerLevelFeeList.forEach((fee: any) => {\r\n        total += fee.remainingAmount || 0;\r\n      });\r\n    }\r\n\r\n    return total;\r\n  }\r\n\r\n  // 获取日期选择器配置\r\n  getDatePickerConfig(scheduleDate: string, scheduleDateEnd: string): any {\r\n    const config = { ...this.calendarConfig };\r\n    if (scheduleDate) {\r\n      config.minDate = new Date(scheduleDate);\r\n      config.defaultDate = new Date(scheduleDate);\r\n    }\r\n    if (scheduleDateEnd) {\r\n      config.maxDate = new Date(scheduleDateEnd);\r\n    }\r\n    return config;\r\n  }\r\n\r\n  // 日期变更处理\r\n  scheduleDateChange(item: any) {\r\n    console.log('scheduleDateChange', item);\r\n    if (!item.scheduleDate) {\r\n      return;\r\n    }\r\n\r\n    // 计算发布日期\r\n    const releaseDate = moment(item.scheduleDate)\r\n      .add(item.delayDays, 'days')\r\n      .format('MM/DD/YYYY');\r\n    item.titleReleaseDate = releaseDate;\r\n\r\n    // 计算日期差\r\n    const now = moment(item.currentDate).format('MM/DD/YYYY');\r\n    const diffDays = moment(item.scheduleDate)\r\n      .diff(moment(item.currentDate), 'days');\r\n\r\n    // 显示预估标记\r\n    this.isShowEstimation = diffDays > 0;\r\n\r\n    // 计算利息和保险费用\r\n    const diffInterest = diffDays * item.interestDaily;\r\n    const diffInsurance = diffDays * item.insuranceDaily;\r\n\r\n    // 更新金额\r\n    item.interestPrice = item.interestPriceTemp + diffInterest;\r\n    item.insurancePrice = item.insurancePriceTemp + diffInsurance;\r\n    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;\r\n  }\r\n\r\n  pay() {\r\n    let isContinue = true;\r\n    let isOnlyPrincipalSave = true;\r\n\r\n    for (const dealer of this.formdata) {\r\n      if (!dealer.bankAccount) {\r\n        this.messageService.add({\r\n          severity:'error',\r\n          summary: 'Error',\r\n          detail: `${dealer.dba}'s Bank Account is required`\r\n        });\r\n        isContinue = false;\r\n        break;\r\n      }\r\n\r\n      if (dealer.dtoList) {\r\n        for (const dto of dealer.dtoList) {\r\n          if (dto.onlyPrincialAmount === 0 && dto.isOnlyPrincial) {\r\n            this.messageService.add({\r\n              severity:'warning',\r\n              summary: 'Warning',\r\n              detail: 'Only Principal amount should be greater than 0!'\r\n            });\r\n            isOnlyPrincipalSave = false;\r\n            break;\r\n          }\r\n\r\n          if (dto.isPayOff && !dto.isPartialPayment && dto.displayMail && !dto.isTrusted) {\r\n            if (this.holdSwitch && dto.isHold) {\r\n              if (!dto.holdType) {\r\n                this.messageService.add({\r\n                  severity:'error',\r\n                  summary: 'Error',\r\n                  detail: 'Special Title Type is required'\r\n                });\r\n                isContinue = false;\r\n                break;\r\n              }\r\n\r\n              if (dto.holdType.value === 'T' && !dto.holdContactInfo) {\r\n                this.messageService.add({\r\n                  severity:'error',\r\n                  summary: 'Error',\r\n                  detail: 'Shipping contact is required'\r\n                });\r\n                isContinue = false;\r\n                break;\r\n              }\r\n\r\n              if ((dto.holdType.value === 'D' || dto.holdType.value === 'H')) {\r\n                if (!dto.newContactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n                if (!dto.newLocationInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping location is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              }\r\n            } else {\r\n              if (dto.contactSwitch) {\r\n                if (!dto.newContactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n                if (!dto.newLocationInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping location is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              } else {\r\n                if (!dto.contactInfo) {\r\n                  this.messageService.add({\r\n                    severity:'error',\r\n                    summary: 'Error',\r\n                    detail: 'Shipping contact is required'\r\n                  });\r\n                  isContinue = false;\r\n                  break;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (!isOnlyPrincipalSave || !isContinue) {\r\n      return;\r\n    }\r\n\r\n    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({\r\n      next: (response: any) => {\r\n        if (response.status === 'success') {\r\n          this.messageService.add({\r\n            severity:'success',\r\n            summary: 'Success',\r\n            detail: response.results\r\n          });\r\n          this.cancel();\r\n        } else if (response.status === 'warning') {\r\n          this.messageService.add({\r\n            severity:'warn',\r\n            summary: 'Warning',\r\n            detail: response.results\r\n          });\r\n        } else {\r\n          this.messageService.add({\r\n            severity:'error',\r\n            summary: 'Error',\r\n            detail: response.results\r\n          });\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        this.messageService.add({\r\n          severity:'error',\r\n          summary: 'Error',\r\n          detail: 'Failed to process payment'\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate(['/loan/schedule-payment']);\r\n  }\r\n\r\n  getPaymentLength(): number {\r\n    return this.formdata.reduce((total, dealer) => {\r\n      return total + (dealer.dtoList?.length || 0);\r\n    }, 0);\r\n  }\r\n\r\n  removeFee(dealer: any, feeItem: any) {\r\n    if (dealer && dealer.dealerLevelFeeList) {\r\n      const index = dealer.dealerLevelFeeList.findIndex((item: any) =>\r\n        item.description === feeItem.description &&\r\n        item.remainingAmount === feeItem.remainingAmount\r\n      );\r\n      if (index > -1) {\r\n        dealer.dealerLevelFeeList.splice(index, 1);\r\n        this.recalculateTotal(dealer);\r\n      }\r\n    }\r\n  }\r\n\r\n  private recalculateTotal(dealer: any) {\r\n    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total: number, fee: any) => {\r\n      return total + (fee.remainingAmount || 0);\r\n    }, 0);\r\n\r\n    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);\r\n  }\r\n\r\n  // 处理仅本金支付的金额变更\r\n  onlyPrincipalAmountChange(item: any) {\r\n    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {\r\n      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\r\n        this.messageService.add({\r\n          severity:'warning',\r\n          summary: 'Warning',\r\n          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'\r\n        });\r\n        item.onlyPrincialAmount = 0;\r\n      }\r\n    } else {\r\n      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {\r\n        this.messageService.add({\r\n          severity:'warning',\r\n          summary: 'Warning',\r\n          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'\r\n        });\r\n        item.onlyPrincialAmount = 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 打开Other Amount输入对话框\r\n  inputOtherAmount(item: any) {\r\n    this.selectedItem = item;\r\n    this.tempOtherAmount = item.otherAmount || 0;\r\n    this.showOtherAmountDialog = true;\r\n  }\r\n\r\n  // 取消Other Amount输入\r\n  cancelOtherAmount() {\r\n    this.showOtherAmountDialog = false;\r\n    this.selectedItem = null;\r\n    this.tempOtherAmount = 0;\r\n  }\r\n\r\n  // 确认Other Amount输入\r\n  confirmOtherAmount() {\r\n    if (this.selectedItem) {\r\n      this.selectedItem.otherAmount = this.tempOtherAmount;\r\n      if (this.tempOtherAmount !== 0) {\r\n        this.selectedItem.buttonName = 'OtherAmount';\r\n      } else {\r\n        this.selectedItem.buttonName = 'Curtailment';\r\n      }\r\n    }\r\n    this.showOtherAmountDialog = false;\r\n    this.selectedItem = null;\r\n    this.tempOtherAmount = 0;\r\n  }\r\n\r\n  // 获取最小日期\r\n  getMinDate(item: any): Date | null {\r\n    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;\r\n  }\r\n\r\n  // 获取最大日期\r\n  getMaxDate(item: any): Date | null {\r\n    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;\r\n  }\r\n\r\n  // Add Contact Dialog\r\n  addContactDialog(item: any) {\r\n    this.selectedContactItem = item;\r\n    this.contactDialogMode = 'add';\r\n    this.contactForm = {\r\n      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,\r\n      firstName: '',\r\n      lastName: '',\r\n      addressLine1: '',\r\n      addressLine2: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      phone: '',\r\n      email: ''\r\n    };\r\n    this.showContactDialog = true;\r\n  }\r\n\r\n  // Edit Contact Dialog\r\n  editContactDialog(contactInfo: any, item: any) {\r\n    this.selectedContactItem = item;\r\n    this.contactDialogMode = 'edit';\r\n    this.editingContact = contactInfo;\r\n    this.contactForm = {\r\n      contactReference: contactInfo.contactReference,\r\n      firstName: contactInfo.firstName,\r\n      lastName: contactInfo.lastName,\r\n      addressLine1: contactInfo.addressLine1,\r\n      addressLine2: contactInfo.addressLine2 || '',\r\n      city: contactInfo.city,\r\n      state: contactInfo.state,\r\n      zipCode: contactInfo.zipCode,\r\n      phone: contactInfo.phone,\r\n      email: contactInfo.email || ''\r\n    };\r\n    this.showContactDialog = true;\r\n  }\r\n\r\n  // Save Contact\r\n  saveContact() {\r\n    if (this.contactDialogMode === 'add') {\r\n      const newContact = { ...this.contactForm };\r\n      \r\n      // Add to contact list\r\n      if (this.selectedContactItem.contactDtoList) {\r\n        this.selectedContactItem.contactDtoList.push(newContact);\r\n      } else {\r\n        this.selectedContactItem.contactDtoList = [newContact];\r\n      }\r\n\r\n      // Set as current contact\r\n      this.selectedContactItem.contactInfo = newContact;\r\n\r\n      // Update all loans for same dealer\r\n      this.formdata.forEach((dealer: any) => {\r\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\r\n          dealer.dtoList?.forEach((loan: any) => {\r\n            if (loan.contactDtoList) {\r\n              loan.contactDtoList.push({ ...newContact });\r\n              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\r\n                loan.isDisabledEdit = true;\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // Edit mode\r\n      const updatedContact = { ...this.contactForm };\r\n      \r\n      // Update in all places\r\n      this.formdata.forEach((dealer: any) => {\r\n        if (dealer.dealerId === this.selectedContactItem.dealerId) {\r\n          dealer.dtoList?.forEach((loan: any) => {\r\n            if (loan.contactDtoList) {\r\n              // Update in contact list\r\n              const index = loan.contactDtoList.findIndex(\r\n                (c: any) => c.contactReference === this.editingContact.contactReference\r\n              );\r\n              if (index > -1) {\r\n                loan.contactDtoList[index] = updatedContact;\r\n              }\r\n\r\n              // Update current selection if matches\r\n              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {\r\n                loan.contactInfo = updatedContact;\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    this.showContactDialog = false;\r\n    this.selectedContactItem = null;\r\n    this.editingContact = null;\r\n  }\r\n\r\n  // Cancel Contact Dialog\r\n  cancelContactDialog() {\r\n    if (this.contactDialogMode === 'add') {\r\n      this.tempContactNumber--;\r\n    }\r\n    this.showContactDialog = false;\r\n    this.selectedContactItem = null;\r\n    this.editingContact = null;\r\n  }\r\n\r\n  // Handle shipping contact change\r\n  shippingContactChange(item: any) {\r\n    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {\r\n      item.isDisabledEdit = true;\r\n    } else {\r\n      item.isDisabledEdit = false;\r\n    }\r\n  }\r\n\r\n  // Remove payoff item\r\n  removePayoff(dealer: any, item: any) {\r\n    if (dealer.dtoList) {\r\n      const index = dealer.dtoList.findIndex((dto: any) => dto === item);\r\n      if (index > -1) {\r\n        dealer.dtoList.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<div class=\"flex flex-column\">\r\n  <div class=\"flex justify-content-between align-items-center mb-4\">\r\n    <div class=\"flex\">Total: <span class=\"colorce3434 font-bold pl-2\">{{getTotal() | currency}}</span></div>\r\n    <div class=\"flex align-items-center gap-3\">\r\n      <button pButton pRipple type=\"button\" class=\"greyButton\" label=\"Cancel\" (click)=\"cancel()\"></button>\r\n      <button pButton pRipple type=\"button\" class=\"greenButton\" label=\"Submit\"\r\n        [disabled]=\"getPaymentLength() === 0\" (click)=\"pay()\"></button>\r\n    </div>\r\n  </div>\r\n  <div class=\"panel border-round p-3 text-sm flex flex-column\" *ngFor=\"let dealer of formdata\">\r\n    <div class=\"flex justify-content-between align-items-center\">\r\n      <div class=\"flex align-items-center flex-1\">\r\n        <div class=\"flex w-3\">\r\n          <div class=\"text-right color2B2E3A pr-2\">Dealer Code</div>\r\n          <div class=\" pl-2 color2B2E3A font-bold\">{{dealer.dealerCode}}</div>\r\n        </div>\r\n        <div class=\"flex w-3\">\r\n          <div class=\" text-right color2B2E3A pr-2\">Dealer Name</div>\r\n          <div class=\" pl-2 color2B2E3A font-bold\">{{dealer.dba}}</div>\r\n        </div>\r\n        <div class=\"flex w-3\">\r\n          <div class=\" text-right color2B2E3A pr-2\">Subtotal</div>\r\n          <div class=\" pl-2 font-bold colorce3434\">{{getSubTotal(dealer) | currency}}</div>\r\n        </div>\r\n        <div class=\"flex w-3 align-items-center\">\r\n          <div class=\" text-right color2B2E3A pr-2\">Bank Account</div>\r\n          <p-dropdown class=\"dropdownStyle pl-2 w-15rem\"\r\n                      [(ngModel)]=\"dealer.bankAccount\"\r\n                      [options]=\"dealer.bankAccountList\"\r\n                      placeholder=\"Select\" [showClear]=\"true\"\r\n                      [optionLabel]=\"'reference'\"\r\n                      [optionValue]=\"'bankAccountDtoId'\"\r\n                      [filter]=\"true\"\r\n                      filterBy=\"reference\">\r\n            <ng-template pTemplate=\"selectedItem\" let-item>\r\n              {{item.bankAccountRanking}}.{{item.reference}}\r\n            </ng-template>\r\n            <ng-template pTemplate=\"item\" let-item>\r\n              <div class=\"text-ellipsis\">{{item.bankAccountRanking}}.{{item.reference}}</div>\r\n            </ng-template>\r\n          </p-dropdown>\r\n        </div>\r\n      </div>\r\n      <img src=\"./assets/img/upicon.png\" class=\"cursor-pointer\">\r\n    </div>\r\n\r\n    <div class=\"panel border-round p-3 flex mt-3 color2B2E3A relative\" *ngFor=\"let feeItem of dealer.dealerLevelFeeList\">\r\n      <div class=\"absolute closeIcon z-1\">\r\n        <i class=\"pi pi-times-circle text-xl colorce3434 cursor-pointer\" (click)=\"removeFee(dealer, feeItem)\"></i>\r\n      </div>\r\n      <div class=\"flex w-full\">\r\n        <div class=\"flex w-4\">\r\n          <div class=\"text-right color2B2E3A pr-2\">Fee Name:</div>\r\n          <div class=\"pl-2\">{{feeItem.description}}</div>\r\n        </div>\r\n        <div class=\"flex w-8\">\r\n          <div class=\"text-right color2B2E3A pr-2\">Pay Amount:</div>\r\n          <div class=\"pl-2 colorce3434\">{{feeItem.remainingAmount | currency}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"panel border-round p-3 flex mt-3 color2B2E3A relative\" *ngFor=\"let item of dealer.dtoList\">\r\n      <div class=\"absolute closeIcon z-1\">\r\n        <i class=\"pi pi-times-circle text-xl colorce3434 cursor-pointer\" (click)=\"removePayoff(dealer, item)\"></i>\r\n      </div>\r\n      \r\n      <!-- Vehicle Info -->\r\n      <div class=\"w-3 flex flex-column gap-3 p-3 border-right-1 border-color-AEB9CC\">\r\n        <div class=\"flex align-items-center gap-2 font-bold\"><i class=\"pi pi-car\"></i> Vehicle Info</div>\r\n        <div class=\"flex\">{{item.vin}}</div>\r\n        <div class=\"flex\">{{item.year}} {{item.make}} {{item.model}}</div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n          <span class=\"color2B2E3A\">Due Date:</span>\r\n          <span class=\"color2B2E3A\">{{item.nextDueDate | date:'MM/dd/yy'}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Payment Detail -->\r\n      <div class=\"w-4 flex flex-column gap-3 py-3 px-5 border-right-1 border-color-AEB9CC\">\r\n        <div class=\"flex align-items-center gap-2 font-bold\"><i class=\"pi pi-book\"></i> Payment Detail</div>\r\n        \r\n        <!-- Payment Type and Amount -->\r\n        <div class=\"flex align-items-center justify-content-between gap-2\">\r\n          <div class=\"flex align-items-center gap-2 pl-3\">\r\n            <i class=\"pi pi-angle-down cursor-pointer\" *ngIf=\"!payoff\" (click)=\"showPayoff()\"></i>\r\n            <i class=\"pi pi-angle-up cursor-pointer\" *ngIf=\"payoff\" (click)=\"showPayoff()\"></i>\r\n            {{item.buttonName}}\r\n          </div>\r\n          <div>{{item.totalMoney | currency}}</div>\r\n        </div>\r\n\r\n        <!-- Payment Details Breakdown -->\r\n        <div class=\"flex flex-column trbg border-round-sm color82808F\" *ngIf=\"payoff\">\r\n          <div class=\"flex flex-column w-12 p-3 gap-3\">\r\n            <!-- Principal -->\r\n            <div class=\"flex justify-content-between align-items-center\">\r\n              <div>Principal</div>\r\n              <div>{{item.principal | currency}}</div>\r\n            </div>\r\n\r\n            <!-- Extra Amounts -->\r\n            <div class=\"flex justify-content-between align-items-center\" *ngFor=\"let fee of item.extraAmountList\">\r\n              <div>{{fee.feeName}}</div>\r\n              <div>{{fee.amount | currency}}</div>\r\n            </div>\r\n\r\n            <!-- Interest -->\r\n            <div class=\"flex justify-content-between align-items-center\">\r\n              <div>Interest</div>\r\n              <div class=\"flex align-items-center gap-2\">\r\n                <i class=\"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\"\r\n                   pTooltip=\"Estimated Amount\"\r\n                   tooltipPosition=\"top\"\r\n                   *ngIf=\"isShowEstimation\"></i>\r\n                {{item.interestPrice | currency}}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- WIP -->\r\n            <div class=\"flex justify-content-between align-items-center\">\r\n              <div>WIP</div>\r\n              <div class=\"flex align-items-center gap-2\">\r\n                <i class=\"pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon\"\r\n                   pTooltip=\"Estimated Amount\"\r\n                   tooltipPosition=\"top\"\r\n                   *ngIf=\"isShowEstimation\"></i>\r\n                {{item.insurancePrice | currency}}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Additional Payment -->\r\n        <div class=\"flex justify-content-between align-items-center\" *ngIf=\"!item.isPayOff && !item.isPartialPayment\">\r\n          <div class=\"flex align-items-center gap-2 pl-3\">Additional Payment</div>\r\n          <div class=\"flex align-items-center gap-3\">\r\n            <span>{{item.otherAmount | currency}}</span>\r\n            <a class=\"text-primary cursor-pointer\" (click)=\"inputOtherAmount(item)\">Change</a>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Schedule Date -->\r\n        <div class=\"flex justify-content-between align-items-center\">\r\n          <div class=\"w-6 pl-3\">Schedule Date</div>\r\n          <div class=\"w-6\">\r\n            <p-calendar\r\n              [(ngModel)]=\"item.scheduleDate\"\r\n              (onSelect)=\"scheduleDateChange(item)\"\r\n              [selectOtherMonths]=\"true\"\r\n              [showButtonBar]=\"false\"\r\n              [monthNavigator]=\"true\"\r\n              [yearNavigator]=\"true\"\r\n              [dateFormat]=\"'mm/dd/yy'\"\r\n              [showTime]=\"false\"\r\n              [showIcon]=\"false\"\r\n              [readonlyInput]=\"true\"\r\n              [minDate]=\"getMinDate(item)\"\r\n              [maxDate]=\"getMaxDate(item)\"\r\n              [disabled]=\"item.isOnlyPrincial\"\r\n              class=\"calendarStyle w-full\">\r\n            </p-calendar>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Principal Only Section -->\r\n        <div class=\"flex flex-column\" *ngIf=\"item.buttonName === 'PayOff' && item.isShowPrincipal\">\r\n          <div class=\"w-12 flex gap-2 justify-content-between align-items-center\">\r\n            <div class=\"w-6\">Principal Only</div>\r\n            <p-checkbox\r\n              class=\"w-6\"\r\n              [(ngModel)]=\"item.isOnlyPrincial\"\r\n              [binary]=\"true\"\r\n              inputId=\"principalOnly\">\r\n            </p-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Principal Only Payment -->\r\n        <div class=\"flex flex-column gap-3\" *ngIf=\"item.isOnlyPrincial\">\r\n          <div class=\"flex justify-content-between align-items-center\">\r\n            <div class=\"w-6\">Payment</div>\r\n            <div class=\"w-6\">\r\n              <p-inputGroup class=\"w-full\">\r\n                <p-inputGroupAddon>$</p-inputGroupAddon>\r\n                <p-inputNumber class=\"inputNumberRadius w-full\"\r\n                              inputId=\"onlyPrincialAmount\"\r\n                              mode=\"decimal\"\r\n                              [minFractionDigits]=\"2\"\r\n                              [maxFractionDigits]=\"2\"\r\n                              [(ngModel)]=\"item.onlyPrincialAmount\"\r\n                              [maxlength]=\"14\"\r\n                              (ngModelChange)=\"onlyPrincipalAmountChange(item)\">\r\n                </p-inputNumber>\r\n              </p-inputGroup>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"flex justify-content-between align-items-center\">\r\n            <div class=\"w-6\">Schedule Date</div>\r\n            <div class=\"w-6\">\r\n              <p-calendar\r\n                [(ngModel)]=\"item.scheduleDate\"\r\n                [disabled]=\"true\"\r\n                [selectOtherMonths]=\"true\"\r\n                [showButtonBar]=\"true\"\r\n                [monthNavigator]=\"true\"\r\n                [yearNavigator]=\"true\"\r\n                [dateFormat]=\"'mm/dd/yy'\"\r\n                class=\"calendarStyle w-full\">\r\n              </p-calendar>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"w-5 flex flex-column gap-3 py-3 px-5\" *ngIf=\"item.displayMail && !item.isTrusted\">\r\n        <div class=\"flex align-items-center gap-2 font-bold\"><i class=\"pi pi-envelope\"></i>Title Shipping Info</div>\r\n        \r\n        <!-- Release Date -->\r\n        <div class=\"flex align-items-center\">\r\n          <div class=\"w-4\">Release Date</div>\r\n          <div class=\"w-8 flex gap-6\">\r\n            <div class=\"w-8\">\r\n              <p-calendar\r\n                [(ngModel)]=\"item.titleReleaseDate\"\r\n                [selectOtherMonths]=\"true\"\r\n                [showButtonBar]=\"true\"\r\n                [monthNavigator]=\"true\"\r\n                [yearNavigator]=\"true\"\r\n                [readonlyInput]=\"true\"\r\n                [dateFormat]=\"'mm/dd/yy'\"\r\n                class=\"calendarStyle w-full\">\r\n              </p-calendar>\r\n            </div>\r\n            <div class=\"flex gap-2 align-items-center\">\r\n              <div>Hold</div>\r\n              <p-checkbox\r\n                [(ngModel)]=\"item.isHold\"\r\n                [binary]=\"true\"\r\n                inputId=\"holdCheckbox\">\r\n              </p-checkbox>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Hold Type Selection -->\r\n        <div class=\"flex align-items-center\" *ngIf=\"holdSwitch && item.isHold\">\r\n          <div class=\"w-4\">Special Title Type</div>\r\n          <div class=\"w-8\">\r\n            <p-dropdown\r\n              class=\"dropdownStyle w-full\"\r\n              [(ngModel)]=\"item.holdType\"\r\n              [options]=\"holdTypeList\"\r\n              optionLabel=\"text\"\r\n              placeholder=\"Select\"\r\n              [showClear]=\"true\">\r\n            </p-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Shipping Method -->\r\n        <div class=\"flex align-items-center\">\r\n          <div class=\"w-4\">Shipping Method</div>\r\n          <div class=\"w-8\">\r\n            <p-dropdown\r\n              class=\"dropdownStyle w-full\"\r\n              [(ngModel)]=\"item.mailFeeInfo\"\r\n              [options]=\"postageFee\"\r\n              optionLabel=\"text\"\r\n              placeholder=\"Select\"\r\n              [showClear]=\"true\">\r\n            </p-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Shipping Contact -->\r\n        <div class=\"flex align-items-center\" *ngIf=\"!item.contactSwitch\">\r\n          <div class=\"w-4\">Shipping Contact</div>\r\n          <div class=\"w-8 flex gap-2\">\r\n            <p-dropdown\r\n              class=\"dropdownStyle w-full\"\r\n              [(ngModel)]=\"item.contactInfo\"\r\n              [options]=\"item.contactDtoList\"\r\n              optionLabel=\"contactReference\"\r\n              placeholder=\"Select\"\r\n              [showClear]=\"true\"\r\n              (onChange)=\"shippingContactChange(item)\">\r\n            </p-dropdown>\r\n            <button pButton\r\n                    type=\"button\"\r\n                    icon=\"pi pi-plus\"\r\n                    class=\"p-button-rounded p-button-text\"\r\n                    (click)=\"addContactDialog(item)\">\r\n            </button>\r\n            <button pButton\r\n                    type=\"button\"\r\n                    icon=\"pi pi-pencil\"\r\n                    class=\"p-button-rounded p-button-text\"\r\n                    [disabled]=\"item.isDisabledEdit\"\r\n                    (click)=\"editContactDialog(item.contactInfo, item)\">\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- New Contact Info (when contactSwitch is true) -->\r\n        <div class=\"flex align-items-center\" *ngIf=\"item.contactSwitch\">\r\n          <div class=\"w-4\">Shipping Contact</div>\r\n          <div class=\"w-8\">\r\n            <p-dropdown\r\n              class=\"dropdownStyle w-full\"\r\n              [(ngModel)]=\"item.newContactInfo\"\r\n              [options]=\"item.newContactDtoList\"\r\n              optionLabel=\"firstName\"\r\n              placeholder=\"Select\"\r\n              [showClear]=\"true\">\r\n            </p-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- New Location Info (when contactSwitch is true) -->\r\n        <div class=\"flex align-items-center\" *ngIf=\"item.contactSwitch\">\r\n          <div class=\"w-4\">Shipping Location</div>\r\n          <div class=\"w-8\">\r\n            <p-dropdown\r\n              class=\"dropdownStyle w-full\"\r\n              [(ngModel)]=\"item.newLocationInfo\"\r\n              [options]=\"item.newLocationDtoList\"\r\n              optionLabel=\"address1\"\r\n              placeholder=\"Select\"\r\n              [showClear]=\"true\">\r\n            </p-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Shipping Address Display -->\r\n        <div class=\"flex align-items-start\">\r\n          <div class=\"w-4\">Shipping Address</div>\r\n          <div class=\"w-8\">\r\n            <div *ngIf=\"!item.contactSwitch && item.contactInfo\">\r\n              <p class=\"m-0\">{{item.contactInfo.firstName}} {{item.contactInfo.lastName}}</p>\r\n              <p class=\"m-0\">{{item.contactInfo.addressLine1}}</p>\r\n              <p class=\"m-0\" *ngIf=\"item.contactInfo.addressLine2\">{{item.contactInfo.addressLine2}}</p>\r\n              <p class=\"m-0\">{{item.contactInfo.city}}, {{item.contactInfo.state}}, {{item.contactInfo.zipCode}}</p>\r\n            </div>\r\n            <div *ngIf=\"item.contactSwitch && item.newContactInfo && item.newLocationInfo\">\r\n              <p class=\"m-0\">{{item.newContactInfo.firstName}} {{item.newContactInfo.lastName}}</p>\r\n              <p class=\"m-0\">{{item.newLocationInfo.address1}}</p>\r\n              <p class=\"m-0\" *ngIf=\"item.newLocationInfo.address2\">{{item.newLocationInfo.address2}}</p>\r\n              <p class=\"m-0\">{{item.newLocationInfo.city}}, {{item.newLocationInfo.state}}, {{item.newLocationInfo.zipCode}}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div *ngIf=\"item.displayMail && item.isTrusted\" class=\"w-5 flex flex-column gap-3 py-3 px-5\">\r\n        <div class=\"text-center\">Title Released</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Other Amount Dialog -->\r\n<p-dialog\r\n  [(visible)]=\"showOtherAmountDialog\"\r\n  [modal]=\"true\"\r\n  [draggable]=\"false\"\r\n  [resizable]=\"false\"\r\n  [style]=\"{width: '30vw'}\"\r\n  header=\"Other Amount\"\r\n  [closeOnEscape]=\"false\"\r\n  [closable]=\"false\">\r\n  <div class=\"flex flex-column gap-3 p-3\">\r\n    <div class=\"flex justify-content-between align-items-center\">\r\n      <label for=\"otherAmount\">Amount</label>\r\n      <p-inputNumber\r\n        id=\"otherAmount\"\r\n        [(ngModel)]=\"tempOtherAmount\"\r\n        mode=\"currency\"\r\n        currency=\"USD\"\r\n        [minFractionDigits]=\"2\"\r\n        [maxFractionDigits]=\"2\"\r\n        [maxlength]=\"14\"\r\n        [max]=\"selectedItem?.otherAmountLimit\">\r\n      </p-inputNumber>\r\n    </div>\r\n  </div>\r\n  <ng-template pTemplate=\"footer\">\r\n    <div class=\"flex justify-content-end gap-2\">\r\n      <button pButton pRipple type=\"button\" label=\"Cancel\" class=\"greyButton\" (click)=\"cancelOtherAmount()\"></button>\r\n      <button pButton pRipple type=\"button\" label=\"Confirm\" class=\"greenButton\" (click)=\"confirmOtherAmount()\"></button>\r\n    </div>\r\n  </ng-template>\r\n</p-dialog>\r\n\r\n<!-- Contact Dialog -->\r\n<p-dialog\r\n  [(visible)]=\"showContactDialog\"\r\n  [modal]=\"true\"\r\n  [draggable]=\"false\"\r\n  [resizable]=\"false\"\r\n  [style]=\"{width: '45vw'}\"\r\n  [header]=\"contactDialogMode === 'add' ? 'Add Contact' : 'Edit Contact'\"\r\n  [closeOnEscape]=\"false\"\r\n  [closable]=\"false\">\r\n  <div class=\"flex flex-column gap-3 p-3\">\r\n    <!-- Contact Form -->\r\n    <div class=\"flex flex-column gap-3\">\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"firstName\">First Name *</label>\r\n        <input pInputText id=\"firstName\" [(ngModel)]=\"contactForm.firstName\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"lastName\">Last Name *</label>\r\n        <input pInputText id=\"lastName\" [(ngModel)]=\"contactForm.lastName\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"addressLine1\">Address Line 1 *</label>\r\n        <input pInputText id=\"addressLine1\" [(ngModel)]=\"contactForm.addressLine1\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"addressLine2\">Address Line 2</label>\r\n        <input pInputText id=\"addressLine2\" [(ngModel)]=\"contactForm.addressLine2\" />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"city\">City *</label>\r\n        <input pInputText id=\"city\" [(ngModel)]=\"contactForm.city\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"state\">State *</label>\r\n        <p-dropdown id=\"state\"\r\n                    [(ngModel)]=\"contactForm.state\"\r\n                    [options]=\"stateList\"\r\n                    optionLabel=\"text\"\r\n                    optionValue=\"value\"\r\n                    placeholder=\"Select\"\r\n                    [showClear]=\"true\"\r\n                    required>\r\n        </p-dropdown>\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"zipCode\">Zip Code *</label>\r\n        <input pInputText id=\"zipCode\" [(ngModel)]=\"contactForm.zipCode\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"phone\">Phone *</label>\r\n        <input pInputText id=\"phone\" [(ngModel)]=\"contactForm.phone\" required />\r\n      </div>\r\n\r\n      <div class=\"flex justify-content-between align-items-center\">\r\n        <label for=\"email\">Email</label>\r\n        <input pInputText id=\"email\" [(ngModel)]=\"contactForm.email\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <ng-template pTemplate=\"footer\">\r\n    <div class=\"flex justify-content-end gap-2\">\r\n      <button pButton pRipple type=\"button\" label=\"Cancel\" class=\"greyButton\" (click)=\"cancelContactDialog()\"></button>\r\n      <button pButton pRipple type=\"button\" label=\"Save\" class=\"greenButton\" (click)=\"saveContact()\"></button>\r\n    </div>\r\n  </ng-template>\r\n</p-dialog>\r\n"], "mappings": "AAEA,OAAOA,MAAM,MAAM,QAAQ;AAE3B,SAASC,cAAc,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;IC+B9BC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,OAAA,CAAAC,kBAAA,OAAAD,OAAA,CAAAE,SAAA,MACF;;;;;IAEEL,EAAA,CAAAM,cAAA,cAA2B;IAAAN,EAAA,CAAAC,MAAA,GAA8C;IAAAD,EAAA,CAAAO,YAAA,EAAM;;;;IAApDP,EAAA,CAAAQ,SAAA,EAA8C;IAA9CR,EAAA,CAAAE,kBAAA,KAAAO,OAAA,CAAAL,kBAAA,OAAAK,OAAA,CAAAJ,SAAA,KAA8C;;;;;;IAU/EL,EAFJ,CAAAM,cAAA,cAAqH,cAC/E,YACoE;IAArCN,EAAA,CAAAU,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,UAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAE,SAAA,CAAAJ,SAAA,EAAAJ,UAAA,CAA0B;IAAA,EAAC;IACvGZ,EADwG,CAAAO,YAAA,EAAI,EACtG;IAGFP,EAFJ,CAAAM,cAAA,cAAyB,cACD,cACqB;IAAAN,EAAA,CAAAC,MAAA,gBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACxDP,EAAA,CAAAM,cAAA,cAAkB;IAAAN,EAAA,CAAAC,MAAA,GAAuB;IAC3CD,EAD2C,CAAAO,YAAA,EAAM,EAC3C;IAEJP,EADF,CAAAM,cAAA,cAAsB,eACqB;IAAAN,EAAA,CAAAC,MAAA,mBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1DP,EAAA,CAAAM,cAAA,eAA8B;IAAAN,EAAA,CAAAC,MAAA,IAAsC;;IAG1ED,EAH0E,CAAAO,YAAA,EAAM,EACtE,EACF,EACF;;;;IAPkBP,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAqB,iBAAA,CAAAT,UAAA,CAAAU,WAAA,CAAuB;IAIXtB,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,QAAAX,UAAA,CAAAY,eAAA,EAAsC;;;;;;IA4BlExB,EAAA,CAAAM,cAAA,YAAkF;IAAvBN,EAAA,CAAAU,UAAA,mBAAAe,oEAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAa,IAAA;MAAA,MAAAR,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAS,UAAA,EAAY;IAAA,EAAC;IAAC3B,EAAA,CAAAO,YAAA,EAAI;;;;;;IACtFP,EAAA,CAAAM,cAAA,YAA+E;IAAvBN,EAAA,CAAAU,UAAA,mBAAAkB,oEAAA;MAAA5B,EAAA,CAAAa,aAAA,CAAAgB,IAAA;MAAA,MAAAX,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAS,UAAA,EAAY;IAAA,EAAC;IAAC3B,EAAA,CAAAO,YAAA,EAAI;;;;;IAiBjFP,EADF,CAAAM,cAAA,cAAsG,UAC/F;IAAAN,EAAA,CAAAC,MAAA,GAAe;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1BP,EAAA,CAAAM,cAAA,UAAK;IAAAN,EAAA,CAAAC,MAAA,GAAyB;;IAChCD,EADgC,CAAAO,YAAA,EAAM,EAChC;;;;IAFCP,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAqB,iBAAA,CAAAS,OAAA,CAAAC,OAAA,CAAe;IACf/B,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAO,OAAA,CAAAE,MAAA,EAAyB;;;;;IAO5BhC,EAAA,CAAAiC,SAAA,YAGgC;;;;;IAShCjC,EAAA,CAAAiC,SAAA,YAGgC;;;;;IA7BlCjC,EAJN,CAAAM,cAAA,cAA8E,cAC/B,cAEkB,UACtD;IAAAN,EAAA,CAAAC,MAAA,gBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACpBP,EAAA,CAAAM,cAAA,UAAK;IAAAN,EAAA,CAAAC,MAAA,GAA6B;;IACpCD,EADoC,CAAAO,YAAA,EAAM,EACpC;IAGNP,EAAA,CAAAkC,UAAA,IAAAC,wDAAA,kBAAsG;IAOpGnC,EADF,CAAAM,cAAA,cAA6D,WACtD;IAAAN,EAAA,CAAAC,MAAA,gBAAQ;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACnBP,EAAA,CAAAM,cAAA,eAA2C;IACzCN,EAAA,CAAAkC,UAAA,KAAAE,uDAAA,gBAG4B;IAC5BpC,EAAA,CAAAC,MAAA,IACF;;IACFD,EADE,CAAAO,YAAA,EAAM,EACF;IAIJP,EADF,CAAAM,cAAA,eAA6D,WACtD;IAAAN,EAAA,CAAAC,MAAA,WAAG;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACdP,EAAA,CAAAM,cAAA,eAA2C;IACzCN,EAAA,CAAAkC,UAAA,KAAAG,uDAAA,gBAG4B;IAC5BrC,EAAA,CAAAC,MAAA,IACF;;IAGND,EAHM,CAAAO,YAAA,EAAM,EACF,EACF,EACF;;;;;IAjCKP,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAe,OAAA,CAAAC,SAAA,EAA6B;IAIyCvC,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAwC,UAAA,YAAAF,OAAA,CAAAG,eAAA,CAAuB;IAY5FzC,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAwB,gBAAA,CAAsB;IAC1B1C,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAAuB,WAAA,QAAAe,OAAA,CAAAM,aAAA,OACF;IAUM5C,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAwB,gBAAA,CAAsB;IAC1B1C,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAAuB,WAAA,SAAAe,OAAA,CAAAO,cAAA,OACF;;;;;;IAOJ7C,EADF,CAAAM,cAAA,cAA8G,cAC5D;IAAAN,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEtEP,EADF,CAAAM,cAAA,aAA2C,WACnC;IAAAN,EAAA,CAAAC,MAAA,GAA+B;;IAAAD,EAAA,CAAAO,YAAA,EAAO;IAC5CP,EAAA,CAAAM,cAAA,YAAwE;IAAjCN,EAAA,CAAAU,UAAA,mBAAAoC,sEAAA;MAAA9C,EAAA,CAAAa,aAAA,CAAAkC,IAAA;MAAA,MAAAT,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA8B,gBAAA,CAAAV,OAAA,CAAsB;IAAA,EAAC;IAACtC,EAAA,CAAAC,MAAA,aAAM;IAElFD,EAFkF,CAAAO,YAAA,EAAI,EAC9E,EACF;;;;IAHIP,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,OAAAe,OAAA,CAAAW,WAAA,EAA+B;;;;;;IA+BrCjD,EAFJ,CAAAM,cAAA,aAA2F,cACjB,cACrD;IAAAN,EAAA,CAAAC,MAAA,qBAAc;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACrCP,EAAA,CAAAM,cAAA,qBAI0B;IAFxBN,EAAA,CAAAkD,gBAAA,2BAAAC,uFAAAC,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAwC,IAAA;MAAA,MAAAf,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAiB,cAAA,EAAAH,MAAA,MAAAd,OAAA,CAAAiB,cAAA,GAAAH,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAiC;IAKvCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;IALAP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAiB,cAAA,CAAiC;IACjCvD,EAAA,CAAAwC,UAAA,gBAAe;;;;;;IASjBxC,EAFJ,CAAAM,cAAA,cAAgE,cACD,cAC1C;IAAAN,EAAA,CAAAC,MAAA,cAAO;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAG1BP,EAFJ,CAAAM,cAAA,cAAiB,uBACc,wBACR;IAAAN,EAAA,CAAAC,MAAA,QAAC;IAAAD,EAAA,CAAAO,YAAA,EAAoB;IACxCP,EAAA,CAAAM,cAAA,wBAOgE;IAFlDN,EAAA,CAAAkD,gBAAA,2BAAAO,0FAAAL,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAqB,kBAAA,EAAAP,MAAA,MAAAd,OAAA,CAAAqB,kBAAA,GAAAP,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAqC;IAErCpD,EAAA,CAAAU,UAAA,2BAAA+C,0FAAA;MAAAzD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAiBD,MAAA,CAAA0C,yBAAA,CAAAtB,OAAA,CAA+B;IAAA,EAAC;IAIrEtC,EAHM,CAAAO,YAAA,EAAgB,EACH,EACX,EACF;IAGJP,EADF,CAAAM,cAAA,cAA6D,eAC1C;IAAAN,EAAA,CAAAC,MAAA,qBAAa;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAElCP,EADF,CAAAM,cAAA,eAAiB,sBASgB;IAP7BN,EAAA,CAAAkD,gBAAA,2BAAAW,wFAAAT,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAA6C,IAAA;MAAA,MAAApB,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAwB,YAAA,EAAAV,MAAA,MAAAd,OAAA,CAAAwB,YAAA,GAAAV,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA+B;IAWvCpD,EAHM,CAAAO,YAAA,EAAa,EACT,EACF,EACF;;;;IAzBgBP,EAAA,CAAAQ,SAAA,GAAuB;IACvBR,EADA,CAAAwC,UAAA,wBAAuB,wBACA;IACvBxC,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAqB,kBAAA,CAAqC;IACrC3D,EAAA,CAAAwC,UAAA,iBAAgB;IAW9BxC,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAwB,YAAA,CAA+B;IAM/B9D,EALA,CAAAwC,UAAA,kBAAiB,2BACS,uBACJ,wBACC,uBACD,0BACG;;;;;;IAuC/BxC,EADF,CAAAM,cAAA,cAAuE,cACpD;IAAAN,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEvCP,EADF,CAAAM,cAAA,cAAiB,qBAOM;IAJnBN,EAAA,CAAAkD,gBAAA,2BAAAa,8FAAAX,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAmD,IAAA;MAAA,MAAA1B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA2B,QAAA,EAAAb,MAAA,MAAAd,OAAA,CAAA2B,QAAA,GAAAb,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA2B;IAOjCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;;IAPAP,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA2B,QAAA,CAA2B;IAI3BjE,EAHA,CAAAwC,UAAA,YAAAtB,MAAA,CAAAgD,YAAA,CAAwB,mBAGN;;;;;;IAsBtBlE,EADF,CAAAM,cAAA,cAAiE,cAC9C;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,eAA4B,sBAQiB;IALzCN,EAAA,CAAAkD,gBAAA,2BAAAiB,8FAAAf,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAA9B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA+B,WAAA,EAAAjB,MAAA,MAAAd,OAAA,CAAA+B,WAAA,GAAAjB,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA8B;IAK9BpD,EAAA,CAAAU,UAAA,sBAAA4D,yFAAA;MAAAtE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAA9B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAqD,qBAAA,CAAAjC,OAAA,CAA2B;IAAA,EAAC;IAC1CtC,EAAA,CAAAO,YAAA,EAAa;IACbP,EAAA,CAAAM,cAAA,kBAIyC;IAAjCN,EAAA,CAAAU,UAAA,mBAAA8D,kFAAA;MAAAxE,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAA9B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAuD,gBAAA,CAAAnC,OAAA,CAAsB;IAAA,EAAC;IACxCtC,EAAA,CAAAO,YAAA,EAAS;IACTP,EAAA,CAAAM,cAAA,kBAK4D;IAApDN,EAAA,CAAAU,UAAA,mBAAAgE,kFAAA;MAAA1E,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAA9B,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAyD,iBAAA,CAAArC,OAAA,CAAA+B,WAAA,EAAA/B,OAAA,CAAyC;IAAA,EAAC;IAG/DtC,EAFI,CAAAO,YAAA,EAAS,EACL,EACF;;;;IArBAP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA+B,WAAA,CAA8B;IAI9BrE,EAHA,CAAAwC,UAAA,YAAAF,OAAA,CAAAsC,cAAA,CAA+B,mBAGb;IAaZ5E,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAwC,UAAA,aAAAF,OAAA,CAAAuC,cAAA,CAAgC;;;;;;IAQ1C7E,EADF,CAAAM,cAAA,cAAgE,cAC7C;IAAAN,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAErCP,EADF,CAAAM,cAAA,cAAiB,sBAOM;IAJnBN,EAAA,CAAAkD,gBAAA,2BAAA4B,8FAAA1B,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAzC,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA0C,cAAA,EAAA5B,MAAA,MAAAd,OAAA,CAAA0C,cAAA,GAAA5B,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAiC;IAOvCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;IAPAP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA0C,cAAA,CAAiC;IAIjChF,EAHA,CAAAwC,UAAA,YAAAF,OAAA,CAAA2C,iBAAA,CAAkC,mBAGhB;;;;;;IAOtBjF,EADF,CAAAM,cAAA,cAAgE,cAC7C;IAAAN,EAAA,CAAAC,MAAA,wBAAiB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEtCP,EADF,CAAAM,cAAA,cAAiB,sBAOM;IAJnBN,EAAA,CAAAkD,gBAAA,2BAAAgC,8FAAA9B,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAA7C,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,IAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA8C,eAAA,EAAAhC,MAAA,MAAAd,OAAA,CAAA8C,eAAA,GAAAhC,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAkC;IAOxCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;;;;IAPAP,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA8C,eAAA,CAAkC;IAIlCpF,EAHA,CAAAwC,UAAA,YAAAF,OAAA,CAAA+C,kBAAA,CAAmC,mBAGjB;;;;;IAYlBrF,EAAA,CAAAM,cAAA,aAAqD;IAAAN,EAAA,CAAAC,MAAA,GAAiC;IAAAD,EAAA,CAAAO,YAAA,EAAI;;;;IAArCP,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA+B,WAAA,CAAAiB,YAAA,CAAiC;;;;;IAFtFtF,EADF,CAAAM,cAAA,UAAqD,aACpC;IAAAN,EAAA,CAAAC,MAAA,GAA4D;IAAAD,EAAA,CAAAO,YAAA,EAAI;IAC/EP,EAAA,CAAAM,cAAA,aAAe;IAAAN,EAAA,CAAAC,MAAA,GAAiC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACpDP,EAAA,CAAAkC,UAAA,IAAAqD,6DAAA,iBAAqD;IACrDvF,EAAA,CAAAM,cAAA,aAAe;IAAAN,EAAA,CAAAC,MAAA,GAAmF;IACpGD,EADoG,CAAAO,YAAA,EAAI,EAClG;;;;IAJWP,EAAA,CAAAQ,SAAA,GAA4D;IAA5DR,EAAA,CAAAE,kBAAA,KAAAoC,OAAA,CAAA+B,WAAA,CAAAmB,SAAA,OAAAlD,OAAA,CAAA+B,WAAA,CAAAoB,QAAA,KAA4D;IAC5DzF,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA+B,WAAA,CAAAqB,YAAA,CAAiC;IAChC1F,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA+B,WAAA,CAAAiB,YAAA,CAAmC;IACpCtF,EAAA,CAAAQ,SAAA,GAAmF;IAAnFR,EAAA,CAAA2F,kBAAA,KAAArD,OAAA,CAAA+B,WAAA,CAAAuB,IAAA,QAAAtD,OAAA,CAAA+B,WAAA,CAAAwB,KAAA,QAAAvD,OAAA,CAAA+B,WAAA,CAAAyB,OAAA,KAAmF;;;;;IAKlG9F,EAAA,CAAAM,cAAA,aAAqD;IAAAN,EAAA,CAAAC,MAAA,GAAiC;IAAAD,EAAA,CAAAO,YAAA,EAAI;;;;IAArCP,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA8C,eAAA,CAAAW,QAAA,CAAiC;;;;;IAFtF/F,EADF,CAAAM,cAAA,UAA+E,aAC9D;IAAAN,EAAA,CAAAC,MAAA,GAAkE;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACrFP,EAAA,CAAAM,cAAA,aAAe;IAAAN,EAAA,CAAAC,MAAA,GAAiC;IAAAD,EAAA,CAAAO,YAAA,EAAI;IACpDP,EAAA,CAAAkC,UAAA,IAAA8D,6DAAA,iBAAqD;IACrDhG,EAAA,CAAAM,cAAA,aAAe;IAAAN,EAAA,CAAAC,MAAA,GAA+F;IAChHD,EADgH,CAAAO,YAAA,EAAI,EAC9G;;;;IAJWP,EAAA,CAAAQ,SAAA,GAAkE;IAAlER,EAAA,CAAAE,kBAAA,KAAAoC,OAAA,CAAA0C,cAAA,CAAAQ,SAAA,OAAAlD,OAAA,CAAA0C,cAAA,CAAAS,QAAA,KAAkE;IAClEzF,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA8C,eAAA,CAAAa,QAAA,CAAiC;IAChCjG,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA8C,eAAA,CAAAW,QAAA,CAAmC;IACpC/F,EAAA,CAAAQ,SAAA,GAA+F;IAA/FR,EAAA,CAAA2F,kBAAA,KAAArD,OAAA,CAAA8C,eAAA,CAAAQ,IAAA,QAAAtD,OAAA,CAAA8C,eAAA,CAAAS,KAAA,QAAAvD,OAAA,CAAA8C,eAAA,CAAAU,OAAA,KAA+F;;;;;;IApIpH9F,EADF,CAAAM,cAAA,cAA8F,cACvC;IAAAN,EAAA,CAAAiC,SAAA,YAA8B;IAAAjC,EAAA,CAAAC,MAAA,0BAAmB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAI1GP,EADF,CAAAM,cAAA,cAAqC,cAClB;IAAAN,EAAA,CAAAC,MAAA,mBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAG/BP,EAFJ,CAAAM,cAAA,cAA4B,cACT,qBASgB;IAP7BN,EAAA,CAAAkD,gBAAA,2BAAAgD,uFAAA9C,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAsF,IAAA;MAAA,MAAA7D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAA8D,gBAAA,EAAAhD,MAAA,MAAAd,OAAA,CAAA8D,gBAAA,GAAAhD,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAmC;IASvCpD,EADE,CAAAO,YAAA,EAAa,EACT;IAEJP,EADF,CAAAM,cAAA,eAA2C,WACpC;IAAAN,EAAA,CAAAC,MAAA,YAAI;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACfP,EAAA,CAAAM,cAAA,sBAGyB;IAFvBN,EAAA,CAAAkD,gBAAA,2BAAAmD,wFAAAjD,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAsF,IAAA;MAAA,MAAA7D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAgE,MAAA,EAAAlD,MAAA,MAAAd,OAAA,CAAAgE,MAAA,GAAAlD,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAyB;IAMjCpD,EAHM,CAAAO,YAAA,EAAa,EACT,EACF,EACF;IAGNP,EAAA,CAAAkC,UAAA,KAAAqE,yDAAA,kBAAuE;IAgBrEvG,EADF,CAAAM,cAAA,eAAqC,eAClB;IAAAN,EAAA,CAAAC,MAAA,uBAAe;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEpCP,EADF,CAAAM,cAAA,eAAiB,sBAOM;IAJnBN,EAAA,CAAAkD,gBAAA,2BAAAsD,wFAAApD,MAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAsF,IAAA;MAAA,MAAA7D,OAAA,GAAAtC,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAmE,WAAA,EAAArD,MAAA,MAAAd,OAAA,CAAAmE,WAAA,GAAArD,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA8B;IAOpCpD,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IA+CNP,EA5CA,CAAAkC,UAAA,KAAAwE,yDAAA,kBAAiE,KAAAC,yDAAA,kBA6BD,KAAAC,yDAAA,kBAeA;IAgB9D5G,EADF,CAAAM,cAAA,eAAoC,eACjB;IAAAN,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACvCP,EAAA,CAAAM,cAAA,eAAiB;IAOfN,EANA,CAAAkC,UAAA,KAAA2E,yDAAA,kBAAqD,KAAAC,yDAAA,kBAM0B;IAQrF9G,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;;IAhIIP,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAA8D,gBAAA,CAAmC;IAMnCpG,EALA,CAAAwC,UAAA,2BAA0B,uBACJ,wBACC,uBACD,uBACA,0BACG;IAOzBxC,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAgE,MAAA,CAAyB;IACzBtG,EAAA,CAAAwC,UAAA,gBAAe;IAQexC,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAA6F,UAAA,IAAAzE,OAAA,CAAAgE,MAAA,CAA+B;IAoB/DtG,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAmE,WAAA,CAA8B;IAI9BzG,EAHA,CAAAwC,UAAA,YAAAtB,MAAA,CAAA8F,UAAA,CAAsB,mBAGJ;IAMchH,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAA2E,aAAA,CAAyB;IA6BzBjH,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA2E,aAAA,CAAwB;IAexBjH,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA2E,aAAA,CAAwB;IAkBpDjH,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAA2E,aAAA,IAAA3E,OAAA,CAAA+B,WAAA,CAA6C;IAM7CrE,EAAA,CAAAQ,SAAA,EAAuE;IAAvER,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAA2E,aAAA,IAAA3E,OAAA,CAAA0C,cAAA,IAAA1C,OAAA,CAAA8C,eAAA,CAAuE;;;;;IAUjFpF,EADF,CAAAM,cAAA,cAA6F,eAClE;IAAAN,EAAA,CAAAC,MAAA,qBAAc;IACzCD,EADyC,CAAAO,YAAA,EAAM,EACzC;;;;;;IAnSJP,EAFJ,CAAAM,cAAA,cAAuG,cACjE,YACoE;IAArCN,EAAA,CAAAU,UAAA,mBAAAwG,+DAAA;MAAA,MAAA5E,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAsG,GAAA,EAAApG,SAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,aAAA,GAAAF,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAkG,YAAA,CAAApG,SAAA,EAAAsB,OAAA,CAA0B;IAAA,EAAC;IACvGtC,EADwG,CAAAO,YAAA,EAAI,EACtG;IAIJP,EADF,CAAAM,cAAA,cAA+E,cACxB;IAAAN,EAAA,CAAAiC,SAAA,YAAyB;IAACjC,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACjGP,EAAA,CAAAM,cAAA,aAAkB;IAAAN,EAAA,CAAAC,MAAA,GAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACpCP,EAAA,CAAAM,cAAA,aAAkB;IAAAN,EAAA,CAAAC,MAAA,IAA0C;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEhEP,EADF,CAAAM,cAAA,cAA2C,gBACf;IAAAN,EAAA,CAAAC,MAAA,iBAAS;IAAAD,EAAA,CAAAO,YAAA,EAAO;IAC1CP,EAAA,CAAAM,cAAA,gBAA0B;IAAAN,EAAA,CAAAC,MAAA,IAAsC;;IAEpED,EAFoE,CAAAO,YAAA,EAAO,EACnE,EACF;IAIJP,EADF,CAAAM,cAAA,eAAqF,eAC9B;IAAAN,EAAA,CAAAiC,SAAA,aAA0B;IAACjC,EAAA,CAAAC,MAAA,uBAAc;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAIlGP,EADF,CAAAM,cAAA,eAAmE,eACjB;IAE9CN,EADA,CAAAkC,UAAA,KAAAmF,gDAAA,gBAAkF,KAAAC,gDAAA,gBACH;IAC/EtH,EAAA,CAAAC,MAAA,IACF;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACNP,EAAA,CAAAM,cAAA,WAAK;IAAAN,EAAA,CAAAC,MAAA,IAA8B;;IACrCD,EADqC,CAAAO,YAAA,EAAM,EACrC;IA4CNP,EAzCA,CAAAkC,UAAA,KAAAqF,kDAAA,oBAA8E,KAAAC,kDAAA,kBAyCgC;IAU5GxH,EADF,CAAAM,cAAA,eAA6D,eACrC;IAAAN,EAAA,CAAAC,MAAA,qBAAa;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAEvCP,EADF,CAAAM,cAAA,eAAiB,sBAegB;IAb7BN,EAAA,CAAAkD,gBAAA,2BAAAuE,iFAAArE,MAAA;MAAA,MAAAd,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAsG,GAAA,EAAApG,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAhB,OAAA,CAAAwB,YAAA,EAAAV,MAAA,MAAAd,OAAA,CAAAwB,YAAA,GAAAV,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAA+B;IAC/BpD,EAAA,CAAAU,UAAA,sBAAAgH,4EAAA;MAAA,MAAApF,OAAA,GAAAtC,EAAA,CAAAa,aAAA,CAAAsG,GAAA,EAAApG,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAYD,MAAA,CAAAyG,kBAAA,CAAArF,OAAA,CAAwB;IAAA,EAAC;IAe3CtC,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IAgBNP,EAbA,CAAAkC,UAAA,KAAA0F,kDAAA,kBAA2F,KAAAC,kDAAA,oBAa3B;IAmClE7H,EAAA,CAAAO,YAAA,EAAM;IA2INP,EA1IA,CAAAkC,UAAA,KAAA4F,kDAAA,oBAA8F,KAAAC,kDAAA,kBA0ID;IAG/F/H,EAAA,CAAAO,YAAA,EAAM;;;;;IA9RgBP,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAqB,iBAAA,CAAAiB,OAAA,CAAA0F,GAAA,CAAY;IACZhI,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAA2F,kBAAA,KAAArD,OAAA,CAAA2F,IAAA,OAAA3F,OAAA,CAAA4F,IAAA,OAAA5F,OAAA,CAAA6F,KAAA,KAA0C;IAGhCnI,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAoI,WAAA,SAAA9F,OAAA,CAAA+F,WAAA,cAAsC;IAWlBrI,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAwC,UAAA,UAAAtB,MAAA,CAAAoH,MAAA,CAAa;IACftI,EAAA,CAAAQ,SAAA,EAAY;IAAZR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAoH,MAAA,CAAY;IACtDtI,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA2C,kBAAA,MAAAL,OAAA,CAAAiG,UAAA,MACF;IACKvI,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,SAAAe,OAAA,CAAAkG,UAAA,EAA8B;IAI2BxI,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAwC,UAAA,SAAAtB,MAAA,CAAAoH,MAAA,CAAY;IAyCdtI,EAAA,CAAAQ,SAAA,EAA8C;IAA9CR,EAAA,CAAAwC,UAAA,UAAAF,OAAA,CAAAmG,QAAA,KAAAnG,OAAA,CAAAoG,gBAAA,CAA8C;IAatG1I,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAwD,gBAAA,YAAAlB,OAAA,CAAAwB,YAAA,CAA+B;IAY/B9D,EAVA,CAAAwC,UAAA,2BAA0B,wBACH,wBACA,uBACD,0BACG,mBACP,mBACA,uBACI,YAAAtB,MAAA,CAAAyH,UAAA,CAAArG,OAAA,EACM,YAAApB,MAAA,CAAA0H,UAAA,CAAAtG,OAAA,EACA,aAAAA,OAAA,CAAAiB,cAAA,CACI;IAOPvD,EAAA,CAAAQ,SAAA,EAA0D;IAA1DR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAiG,UAAA,iBAAAjG,OAAA,CAAAuG,eAAA,CAA0D;IAapD7I,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAiB,cAAA,CAAyB;IAoCbvD,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAwG,WAAA,KAAAxG,OAAA,CAAAyG,SAAA,CAAyC;IA0ItF/I,EAAA,CAAAQ,SAAA,EAAwC;IAAxCR,EAAA,CAAAwC,UAAA,SAAAF,OAAA,CAAAwG,WAAA,IAAAxG,OAAA,CAAAyG,SAAA,CAAwC;;;;;;IApV1C/I,EAJR,CAAAM,cAAA,cAA6F,cAC9B,cACf,cACpB,cACqB;IAAAN,EAAA,CAAAC,MAAA,kBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC1DP,EAAA,CAAAM,cAAA,cAAyC;IAAAN,EAAA,CAAAC,MAAA,GAAqB;IAChED,EADgE,CAAAO,YAAA,EAAM,EAChE;IAEJP,EADF,CAAAM,cAAA,cAAsB,cACsB;IAAAN,EAAA,CAAAC,MAAA,mBAAW;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC3DP,EAAA,CAAAM,cAAA,eAAyC;IAAAN,EAAA,CAAAC,MAAA,IAAc;IACzDD,EADyD,CAAAO,YAAA,EAAM,EACzD;IAEJP,EADF,CAAAM,cAAA,eAAsB,eACsB;IAAAN,EAAA,CAAAC,MAAA,gBAAQ;IAAAD,EAAA,CAAAO,YAAA,EAAM;IACxDP,EAAA,CAAAM,cAAA,eAAyC;IAAAN,EAAA,CAAAC,MAAA,IAAkC;;IAC7ED,EAD6E,CAAAO,YAAA,EAAM,EAC7E;IAEJP,EADF,CAAAM,cAAA,eAAyC,eACG;IAAAN,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAO,YAAA,EAAM;IAC5DP,EAAA,CAAAM,cAAA,sBAOiC;IANrBN,EAAA,CAAAkD,gBAAA,2BAAA8F,0EAAA5F,MAAA;MAAA,MAAApC,SAAA,GAAAhB,EAAA,CAAAa,aAAA,CAAAoI,GAAA,EAAAlI,SAAA;MAAAf,EAAA,CAAAsD,kBAAA,CAAAtC,SAAA,CAAAkI,WAAA,EAAA9F,MAAA,MAAApC,SAAA,CAAAkI,WAAA,GAAA9F,MAAA;MAAA,OAAApD,EAAA,CAAAmB,WAAA,CAAAiC,MAAA;IAAA,EAAgC;IAU1CpD,EAHA,CAAAkC,UAAA,KAAAiH,mDAAA,0BAA+C,KAAAC,mDAAA,0BAGR;IAK7CpJ,EAFI,CAAAO,YAAA,EAAa,EACT,EACF;IACNP,EAAA,CAAAiC,SAAA,eAA0D;IAC5DjC,EAAA,CAAAO,YAAA,EAAM;IAkBNP,EAhBA,CAAAkC,UAAA,KAAAmH,2CAAA,mBAAqH,KAAAC,2CAAA,oBAgBd;IAuSzGtJ,EAAA,CAAAO,YAAA,EAAM;;;;;IAvV2CP,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAqB,iBAAA,CAAAL,SAAA,CAAAuI,UAAA,CAAqB;IAIrBvJ,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAqB,iBAAA,CAAAL,SAAA,CAAAwI,GAAA,CAAc;IAIdxJ,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,SAAAL,MAAA,CAAAuI,WAAA,CAAAzI,SAAA,GAAkC;IAK/DhB,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAwD,gBAAA,YAAAxC,SAAA,CAAAkI,WAAA,CAAgC;IAKhClJ,EAJA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA0I,eAAA,CAAkC,mBACK,4BACZ,mCACO,gBACnB;IAcsD1J,EAAA,CAAAQ,SAAA,GAA4B;IAA5BR,EAAA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA2I,kBAAA,CAA4B;IAgB/B3J,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAwC,UAAA,YAAAxB,SAAA,CAAA4I,OAAA,CAAiB;;;;;;IAqUnG5J,EADF,CAAAM,cAAA,eAA4C,gBAC4D;IAA9BN,EAAA,CAAAU,UAAA,mBAAAmJ,qEAAA;MAAA7J,EAAA,CAAAa,aAAA,CAAAiJ,IAAA;MAAA,MAAA5I,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA6I,iBAAA,EAAmB;IAAA,EAAC;IAAC/J,EAAA,CAAAO,YAAA,EAAS;IAC/GP,EAAA,CAAAM,cAAA,kBAAyG;IAA/BN,EAAA,CAAAU,UAAA,mBAAAsJ,qEAAA;MAAAhK,EAAA,CAAAa,aAAA,CAAAiJ,IAAA;MAAA,MAAA5I,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAA+I,kBAAA,EAAoB;IAAA,EAAC;IAC1GjK,EAD2G,CAAAO,YAAA,EAAS,EAC9G;;;;;;IA0EJP,EADF,CAAAM,cAAA,eAA4C,gBAC8D;IAAhCN,EAAA,CAAAU,UAAA,mBAAAwJ,qEAAA;MAAAlK,EAAA,CAAAa,aAAA,CAAAsJ,IAAA;MAAA,MAAAjJ,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAkJ,mBAAA,EAAqB;IAAA,EAAC;IAACpK,EAAA,CAAAO,YAAA,EAAS;IACjHP,EAAA,CAAAM,cAAA,kBAA+F;IAAxBN,EAAA,CAAAU,UAAA,mBAAA2J,qEAAA;MAAArK,EAAA,CAAAa,aAAA,CAAAsJ,IAAA;MAAA,MAAAjJ,MAAA,GAAAlB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAoJ,WAAA,EAAa;IAAA,EAAC;IAChGtK,EADiG,CAAAO,YAAA,EAAS,EACpG;;;ADrcV,WAAagK,oBAAoB;EAA3B,MAAOA,oBAAoB;IAgD/BC,YACUC,MAAc,EACdC,KAAqB,EACrBC,0BAAsD,EACtDC,cAA8B;MAH9B,KAAAH,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,0BAA0B,GAA1BA,0BAA0B;MAC1B,KAAAC,cAAc,GAAdA,cAAc;MAnDxB,KAAAtC,MAAM,GAAY,KAAK;MACvB,KAAAuC,QAAQ,GAAU,EAAE;MACpB,KAAA7D,UAAU,GAAU,EAAE;MACtB,KAAA8D,SAAS,GAAU,EAAE;MACrB,KAAA5G,YAAY,GAAU,EAAE;MACxB,KAAA6G,eAAe,GAAU,EAAE;MAC3B,KAAAhE,UAAU,GAAY,KAAK;MAC3B,KAAArE,gBAAgB,GAAY,KAAK;MAEjC;MACA,KAAAsI,qBAAqB,GAAY,KAAK;MACtC,KAAAC,YAAY,GAAQ,IAAI;MACxB,KAAAC,eAAe,GAAW,CAAC;MAE3B;MACA,KAAAC,cAAc,GAAQ;QACpBC,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,IAAI;QACpBC,aAAa,EAAE,IAAI;QACnBC,UAAU,EAAE,UAAU;QACtBC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;OACX;MAED;MACA,KAAAC,iBAAiB,GAAY,KAAK;MAClC,KAAAC,mBAAmB,GAAQ,IAAI;MAC/B,KAAAC,iBAAiB,GAAmB,KAAK;MACzC,KAAAC,cAAc,GAAQ,IAAI;MAC1B,KAAAC,iBAAiB,GAAW,CAAC;MAE7B;MACA,KAAAC,WAAW,GAAG;QACZC,gBAAgB,EAAE,EAAE;QACpB1G,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE,EAAE;QAChBJ,YAAY,EAAE,EAAE;QAChBM,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXqG,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;OACR;IAOE;IAEHC,QAAQA,CAAA;MACN;MACA,MAAMC,QAAQ,GAAG;QACfC,UAAU,EAAE,CACV;UACE,QAAQ,EAAE,sCAAsC;UAChD,cAAc,EAAE,sCAAsC;UACtD,iBAAiB,EAAE,IAAI;UACvB,oBAAoB,EAAE,sCAAsC;UAC5D,eAAe,EAAE,MAAM;UACvB,MAAM,EAAE,IAAI;UACZ,aAAa,EAAE,IAAI;UACnB,MAAM,EAAE,EAAE;UACV,mBAAmB,EAAE,IAAI;UACzB,UAAU,EAAE,EAAE;UACd,YAAY,EAAE,IAAI;UAClB,SAAS,EAAE,IAAI;UACf,aAAa,EAAE,IAAI;UACnB,oBAAoB,EAAE,wBAAwB;UAC9C,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,mBAAmB,EAAE,CAAC;UACtB,oBAAoB,EAAE,CAAC;UACvB,gBAAgB,EAAE,KAAK;UACvB,oBAAoB,EAAE,CAAC;UACvB,WAAW,EAAE,IAAI;UACjB,MAAM,EAAE,IAAI;UACZ,gBAAgB,EAAE,IAAI;UACtB,aAAa,EAAE,KAAK;UACpB,MAAM,EAAE,KAAK;UACb,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,mBAAmB;UAC1B,kBAAkB,EAAE,IAAI;UACxB,WAAW,EAAE,CAAC;UACd,sBAAsB,EAAE,IAAI;UAC5B,UAAU,EAAE,IAAI;UAChB,gBAAgB,EAAE,IAAI;UACtB,eAAe,EAAE,IAAI;UACrB,WAAW,EAAE,IAAI;UACjB,gBAAgB,EAAE,KAAK;UACvB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,KAAK;UACtB,gBAAgB,EAAE,IAAI;UACtB,YAAY,EAAE,IAAI;UAClB,oBAAoB,EAAE,IAAI;UAC1B,gBAAgB,EAAE,IAAI;UACtB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,IAAI;UACrB,UAAU,EAAE,IAAI;UAChB,cAAc,EAAE,qBAAqB;UACrC,aAAa,EAAE,qBAAqB;UACpC,SAAS,EAAE,qBAAqB;UAChC,QAAQ,EAAE,KAAK;UACf,WAAW,EAAE,KAAK;UAClB,QAAQ,EAAE,EAAE;UACZ,UAAU,EAAE,IAAI;UAChB,kBAAkB,EAAE,KAAK;UACzB,eAAe,EAAE,KAAK;UACtB,iBAAiB,EAAE,KAAK;UACxB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,oBAAoB,EAAE,CAAC;UACvB,YAAY,EAAE,EAAE;UAChB,cAAc,EAAE,qBAAqB;UACrC,qBAAqB,EAAE,IAAI;UAC3B,aAAa,EAAE,IAAI;UACnB,iBAAiB,EAAE,IAAI;UACvB,UAAU,EAAE,sCAAsC;UAClD,QAAQ,EAAE,sCAAsC;UAChD,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,QAAQ;UACtB,YAAY,EAAE,IAAI;UAClB,UAAU,EAAE,sCAAsC;UAClD,KAAK,EAAE,UAAU;UACjB,WAAW,EAAE,UAAU;UACvB,YAAY,EAAE,QAAQ;UACtB,eAAe,EAAE,uCAAuC;UACxD,kBAAkB,EAAE,uCAAuC;UAC3D,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,qBAAqB;UACtC,iBAAiB,EAAE,CAAC;UACpB,kBAAkB,EAAE,CAAC;UACrB,eAAe,EAAE,CAAC;UAClB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,iBAAiB,EAAE,CAAC;UACpB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,SAAS,EAAE,CAAC;UACZ,aAAa,EAAE,IAAI;UACnB,WAAW,EAAE,CAAC;UACd,WAAW,EAAE,IAAI;UACjB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,YAAY,EAAE,IAAI;UAClB,eAAe,EAAE,IAAI;UACrB,cAAc,EAAE,IAAI;UACpB,iBAAiB,EAAE,CAAC;UACpB,YAAY,EAAE,CAAC;UACf,gBAAgB,EAAE,CAAC;UACnB,UAAU,EAAE,CAAC;UACb,WAAW,EAAE,CAAC;UACd,wBAAwB,EAAE,CAAC;UAC3B,yBAAyB,EAAE,CAAC;UAC5B,SAAS,EAAE,GAAG;UACd,kBAAkB,EAAE,CAAC;UACrB,aAAa,EAAE,KAAK;UACpB,SAAS,EAAE,uBAAuB;UAClC,oBAAoB,EAAE,EAAE;UACxB,YAAY,EAAE,CAAC;UACf,iBAAiB,EAAE,CACjB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,uBAAuB,EAAE,CACvB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,wBAAwB,EAAE,CACxB;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,QAAQ;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,aAAa,EAAE,IAAI;UACnB,YAAY,EAAE,GAAG;UACjB,aAAa,EAAE,KAAK;UACpB,cAAc,EAAE,KAAK;UACrB,UAAU,EAAE,CAAC;UACb,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,KAAK;UACtB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,SAAS,EAAE,CAAC;UACZ,SAAS,EAAE,CAAC;UACZ,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,oBAAoB,EAAE;SACvB,EACD;UACE,QAAQ,EAAE,sCAAsC;UAChD,cAAc,EAAE,sCAAsC;UACtD,iBAAiB,EAAE,IAAI;UACvB,oBAAoB,EAAE,sCAAsC;UAC5D,eAAe,EAAE,MAAM;UACvB,MAAM,EAAE,IAAI;UACZ,aAAa,EAAE,IAAI;UACnB,MAAM,EAAE,EAAE;UACV,mBAAmB,EAAE,EAAE;UACvB,UAAU,EAAE,EAAE;UACd,YAAY,EAAE,EAAE;UAChB,SAAS,EAAE,IAAI;UACf,aAAa,EAAE,IAAI;UACnB,oBAAoB,EAAE,yBAAyB;UAC/C,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,mBAAmB,EAAE,CAAC;UACtB,oBAAoB,EAAE,CAAC;UACvB,gBAAgB,EAAE,KAAK;UACvB,oBAAoB,EAAE,CAAC;UACvB,WAAW,EAAE,IAAI;UACjB,MAAM,EAAE,IAAI;UACZ,gBAAgB,EAAE,IAAI;UACtB,aAAa,EAAE,KAAK;UACpB,MAAM,EAAE,KAAK;UACb,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,mBAAmB;UAC1B,kBAAkB,EAAE,IAAI;UACxB,WAAW,EAAE,CAAC;UACd,sBAAsB,EAAE,IAAI;UAC5B,UAAU,EAAE,IAAI;UAChB,gBAAgB,EAAE,IAAI;UACtB,eAAe,EAAE,IAAI;UACrB,WAAW,EAAE,IAAI;UACjB,gBAAgB,EAAE,KAAK;UACvB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,KAAK;UACtB,gBAAgB,EAAE,IAAI;UACtB,YAAY,EAAE,IAAI;UAClB,oBAAoB,EAAE,IAAI;UAC1B,gBAAgB,EAAE,IAAI;UACtB,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,IAAI;UACrB,UAAU,EAAE,IAAI;UAChB,cAAc,EAAE,qBAAqB;UACrC,aAAa,EAAE,qBAAqB;UACpC,SAAS,EAAE,qBAAqB;UAChC,QAAQ,EAAE,KAAK;UACf,WAAW,EAAE,KAAK;UAClB,QAAQ,EAAE,IAAI;UACd,UAAU,EAAE,IAAI;UAChB,kBAAkB,EAAE,KAAK;UACzB,eAAe,EAAE,KAAK;UACtB,iBAAiB,EAAE,KAAK;UACxB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,oBAAoB,EAAE,CAAC;UACvB,YAAY,EAAE,IAAI;UAClB,cAAc,EAAE,qBAAqB;UACrC,qBAAqB,EAAE,IAAI;UAC3B,aAAa,EAAE,IAAI;UACnB,iBAAiB,EAAE,IAAI;UACvB,UAAU,EAAE,sCAAsC;UAClD,QAAQ,EAAE,sCAAsC;UAChD,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,QAAQ;UACtB,YAAY,EAAE,IAAI;UAClB,UAAU,EAAE,sCAAsC;UAClD,KAAK,EAAE,UAAU;UACjB,WAAW,EAAE,UAAU;UACvB,YAAY,EAAE,QAAQ;UACtB,eAAe,EAAE,iHAAiH;UAClI,kBAAkB,EAAE,iHAAiH;UACrI,mBAAmB,EAAE,IAAI;UACzB,eAAe,EAAE,qBAAqB;UACtC,iBAAiB,EAAE,IAAI;UACvB,kBAAkB,EAAE,CAAC;UACrB,eAAe,EAAE,CAAC;UAClB,eAAe,EAAE,CAAC;UAClB,gBAAgB,EAAE,CAAC;UACnB,iBAAiB,EAAE,CAAC;UACpB,eAAe,EAAE,IAAI;UACrB,aAAa,EAAE,CAAC;UAChB,aAAa,EAAE,CAAC;UAChB,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,SAAS,EAAE,CAAC;UACZ,aAAa,EAAE,IAAI;UACnB,WAAW,EAAE,CAAC;UACd,WAAW,EAAE,IAAI;UACjB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,cAAc,EAAE,IAAI;UACpB,YAAY,EAAE,IAAI;UAClB,eAAe,EAAE,IAAI;UACrB,cAAc,EAAE,IAAI;UACpB,iBAAiB,EAAE,CAAC;UACpB,YAAY,EAAE,CAAC;UACf,gBAAgB,EAAE,CAAC;UACnB,UAAU,EAAE,CAAC;UACb,WAAW,EAAE,CAAC;UACd,wBAAwB,EAAE,CAAC;UAC3B,yBAAyB,EAAE,CAAC;UAC5B,SAAS,EAAE,GAAG;UACd,kBAAkB,EAAE,CAAC;UACrB,aAAa,EAAE,KAAK;UACpB,SAAS,EAAE,uBAAuB;UAClC,oBAAoB,EAAE,EAAE;UACxB,YAAY,EAAE,GAAG;UACjB,iBAAiB,EAAE,CACjB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,uBAAuB,EAAE,CACvB;YACE,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,wBAAwB,EAAE,CACxB;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,WAAW;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,EACD;YACE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE;WACZ,CACF;UACD,aAAa,EAAE,IAAI;UACnB,YAAY,EAAE,GAAG;UACjB,aAAa,EAAE,KAAK;UACpB,cAAc,EAAE,KAAK;UACrB,UAAU,EAAE,CAAC;UACb,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,KAAK;UACtB,eAAe,EAAE,CAAC;UAClB,aAAa,EAAE,CAAC;UAChB,SAAS,EAAE,CAAC;UACZ,SAAS,EAAE,CAAC;UACZ,cAAc,EAAE,CAAC;UACjB,eAAe,EAAE,CAAC;UAClB,oBAAoB,EAAE;SACvB,CACF;QACD,iBAAiB,EAAE,CACjB;UACE,UAAU,EAAE,sCAAsC;UAClD,0BAA0B,EAAE,sCAAsC;UAClE,SAAS,EAAE,WAAW;UACtB,aAAa,EAAE,WAAW;UAC1B,iBAAiB,EAAE,GAAG;UACtB,2BAA2B,EAAE,CAAC;UAC9B,YAAY,EAAE,IAAI;UAClB,KAAK,EAAE,UAAU;UACjB,MAAM,EAAE,UAAU;UAClB,YAAY,EAAE,QAAQ;UACtB,cAAc,EAAE,qBAAqB;UACrC,SAAS,EAAE,IAAI;UACf,YAAY,EAAE,qBAAqB;UACnC,4BAA4B,EAAE,GAAG;UACjC,WAAW,EAAE,IAAI;UACjB,SAAS,EAAE;SACZ,EACD;UACE,UAAU,EAAE,sCAAsC;UAClD,0BAA0B,EAAE,sCAAsC;UAClE,SAAS,EAAE,mBAAmB;UAC9B,aAAa,EAAE,mBAAmB;UAClC,iBAAiB,EAAE,GAAG;UACtB,2BAA2B,EAAE,CAAC;UAC9B,YAAY,EAAE,IAAI;UAClB,KAAK,EAAE,UAAU;UACjB,MAAM,EAAE,UAAU;UAClB,YAAY,EAAE,QAAQ;UACtB,cAAc,EAAE,qBAAqB;UACrC,SAAS,EAAE,IAAI;UACf,YAAY,EAAE,qBAAqB;UACnC,4BAA4B,EAAE,GAAG;UACjC,WAAW,EAAE,IAAI;UACjB,SAAS,EAAE;SACZ;OAEJ;MAED,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC;MAErC,IAAI,CAACE,IAAI,EAAE;QACT,IAAI,CAAC5B,cAAc,CAAC+B,GAAG,CAAC;UAACC,QAAQ,EAAC,OAAO;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAuB,CAAC,CAAC;QAC9F,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAChD;MACF;MAEA,MAAMC,WAAW,GAAG;QAClBT,UAAU,EAAEE,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACD,UAAU;QACvCW,eAAe,EAAET,IAAI,CAACQ,KAAK,CAACT,IAAI,CAAC,CAACU;OACnC;MAED,IAAI,CAACvC,0BAA0B,CAACwC,kBAAkB,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QACxEC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAACC,IAAI,KAAK,GAAG,EAAE;YACzB,IAAI,CAAC1C,QAAQ,GAAGyC,QAAQ,CAACd,IAAI,CAACgB,OAAO,IAAI,EAAE;YAC3C,IAAI,CAACxG,UAAU,GAAGsG,QAAQ,CAACd,IAAI,CAACxF,UAAU,IAAI,EAAE;YAChD,IAAI,CAAC8D,SAAS,GAAGwC,QAAQ,CAACd,IAAI,CAAC1B,SAAS,IAAI,EAAE;YAC9C,IAAI,CAAC5G,YAAY,GAAGoJ,QAAQ,CAACd,IAAI,CAACiB,mBAAmB,IAAI,EAAE;YAC3D,IAAI,CAAC1C,eAAe,GAAGuC,QAAQ,CAACd,IAAI,CAACzB,eAAe,IAAI,EAAE;YAC1D,IAAI,CAAChE,UAAU,GAAGuG,QAAQ,CAACd,IAAI,CAACkB,iBAAiB,IAAI,KAAK;YAE1D,IAAI,CAAC7C,QAAQ,CAAC8C,OAAO,CAAEC,MAAW,IAAI;cACpC,IAAIA,MAAM,CAAChE,OAAO,EAAE;gBAClBgE,MAAM,CAAChE,OAAO,CAAC+D,OAAO,CAAEE,GAAQ,IAAI;kBAClC,IAAIA,GAAG,CAACpF,QAAQ,IAAI,CAACoF,GAAG,CAACnF,gBAAgB,EAAE;oBACzC,IAAImF,GAAG,CAAC5G,aAAa,EAAE;sBACrB4G,GAAG,CAACzI,eAAe,GAAGyI,GAAG,CAACC,cAAc;sBACxCD,GAAG,CAAC7I,cAAc,GAAG6I,GAAG,CAACE,aAAa;oBACxC,CAAC,MAAM;sBACLF,GAAG,CAACxJ,WAAW,GAAGwJ,GAAG,CAACjJ,cAAc,GAAG,CAAC,CAAC;oBAC3C;oBACAiJ,GAAG,CAACpH,WAAW,GAAG,IAAI,CAACO,UAAU,CAAC,CAAC,CAAC;oBAEpC,MAAMgH,WAAW,GAAGlO,MAAM,CAAC,IAAImO,IAAI,CAACJ,GAAG,CAAC/J,YAAY,CAAC,CAAC,CACnD6I,GAAG,CAACkB,GAAG,CAACK,SAAS,EAAE,MAAM,CAAC,CAC1BC,MAAM,CAAC,YAAY,CAAC;oBACvBN,GAAG,CAACzH,gBAAgB,GAAG4H,WAAW;oBAElC,IAAI,IAAI,CAACjH,UAAU,IAAI8G,GAAG,CAACvH,MAAM,EAAE;sBACjC,MAAM8H,SAAS,GAAG,IAAI,CAAClK,YAAY,CAACmK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,KAAK,KAAK,GAAG,CAAC;sBACvE,IAAIH,SAAS,CAACI,MAAM,GAAG,CAAC,EAAE;wBACxBX,GAAG,CAAC5J,QAAQ,GAAGmK,SAAS,CAAC,CAAC,CAAC;sBAC7B;oBACF;kBACF;kBAEA;kBACA,IAAGP,GAAG,CAAC/J,YAAY,EAAE;oBACnB+J,GAAG,CAAC/J,YAAY,GAAGhE,MAAM,CAAC+N,GAAG,CAAC/J,YAAY,CAAC,CAAC2K,MAAM,EAAE;kBACtD;kBACA,IAAGZ,GAAG,CAACa,eAAe,EAAE;oBACtBb,GAAG,CAACa,eAAe,GAAG5O,MAAM,CAAC+N,GAAG,CAACa,eAAe,CAAC,CAACD,MAAM,EAAE;kBAC5D;kBACA,IAAGZ,GAAG,CAACc,WAAW,EAAE;oBAClBd,GAAG,CAACc,WAAW,GAAG7O,MAAM,CAAC+N,GAAG,CAACc,WAAW,CAAC,CAACF,MAAM,EAAE;kBACpD;kBAEA,IAAIb,MAAM,CAACgB,aAAa,IAAIhB,MAAM,CAAClE,eAAe,EAAE8E,MAAM,EAAE;oBAC1D,MAAMK,eAAe,GAAGjB,MAAM,CAAClE,eAAe,CAACoF,IAAI,CAAEC,OAAY,IAC/DA,OAAO,CAACC,gBAAgB,KAAKpB,MAAM,CAACgB,aAAa,CAACK,aAAa,CAChE;oBACD,IAAIJ,eAAe,EAAE;sBACnBjB,MAAM,CAAC1E,WAAW,GAAG2F,eAAe,CAACG,gBAAgB;oBACvD;kBACF;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAACpE,cAAc,CAAC+B,GAAG,CAAC;cAACC,QAAQ,EAAC,OAAO;cAAEC,OAAO,EAAE,OAAO;cAAEC,MAAM,EAAEQ,QAAQ,CAAC4B;YAAO,CAAC,CAAC;YACvF,IAAI,CAACzE,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;UAClD;QACF,CAAC;QACDoC,KAAK,EAAGA,KAAY,IAAI;UACtB,IAAI,CAACvE,cAAc,CAAC+B,GAAG,CAAC;YAACC,QAAQ,EAAC,OAAO;YAAEC,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAE;UAA4B,CAAC,CAAC;UACnGsC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;IACJ;IAEAxN,UAAUA,CAAA;MACR,IAAI,CAAC2G,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC5B;IAEA+G,QAAQA,CAAA;MACN,OAAO,IAAI,CAACxE,QAAQ,CAACyE,MAAM,CAAC,CAACC,KAAK,EAAE3B,MAAM,KAAI;QAC5C,MAAM4B,WAAW,GAAG,IAAI,CAAC/F,WAAW,CAACmE,MAAM,CAAC;QAC5C,OAAO2B,KAAK,GAAGC,WAAW;MAC5B,CAAC,EAAE,CAAC,CAAC;IACP;IAEA/F,WAAWA,CAACgG,UAAe;MACzB,IAAIF,KAAK,GAAG,CAAC;MACb,IAAIE,UAAU,CAAC7F,OAAO,EAAE;QACtB6F,UAAU,CAAC7F,OAAO,CAAC+D,OAAO,CAAE+B,IAAS,IAAI;UACvCH,KAAK,IAAIG,IAAI,CAAClH,UAAU,IAAI,CAAC;UAC7B,IAAIkH,IAAI,CAACjN,eAAe,EAAE;YACxBiN,IAAI,CAACjN,eAAe,CAACkL,OAAO,CAAEgC,GAAQ,IAAI;cACxCJ,KAAK,IAAII,GAAG,CAAC3N,MAAM,IAAI,CAAC;YAC1B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAIyN,UAAU,CAAC9F,kBAAkB,EAAE;QACjC8F,UAAU,CAAC9F,kBAAkB,CAACgE,OAAO,CAAEgC,GAAQ,IAAI;UACjDJ,KAAK,IAAII,GAAG,CAACnO,eAAe,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;MAEA,OAAO+N,KAAK;IACd;IAEA;IACAK,mBAAmBA,CAAC9L,YAAoB,EAAE4K,eAAuB;MAC/D,MAAMmB,MAAM,GAAG;QAAE,GAAG,IAAI,CAAC1E;MAAc,CAAE;MACzC,IAAIrH,YAAY,EAAE;QAChB+L,MAAM,CAACC,OAAO,GAAG,IAAI7B,IAAI,CAACnK,YAAY,CAAC;QACvC+L,MAAM,CAACE,WAAW,GAAG,IAAI9B,IAAI,CAACnK,YAAY,CAAC;MAC7C;MACA,IAAI4K,eAAe,EAAE;QACnBmB,MAAM,CAACG,OAAO,GAAG,IAAI/B,IAAI,CAACS,eAAe,CAAC;MAC5C;MACA,OAAOmB,MAAM;IACf;IAEA;IACAlI,kBAAkBA,CAAC+H,IAAS;MAC1BN,OAAO,CAACa,GAAG,CAAC,oBAAoB,EAAEP,IAAI,CAAC;MACvC,IAAI,CAACA,IAAI,CAAC5L,YAAY,EAAE;QACtB;MACF;MAEA;MACA,MAAMkK,WAAW,GAAGlO,MAAM,CAAC4P,IAAI,CAAC5L,YAAY,CAAC,CAC1C6I,GAAG,CAAC+C,IAAI,CAACxB,SAAS,EAAE,MAAM,CAAC,CAC3BC,MAAM,CAAC,YAAY,CAAC;MACvBuB,IAAI,CAACtJ,gBAAgB,GAAG4H,WAAW;MAEnC;MACA,MAAMkC,GAAG,GAAGpQ,MAAM,CAAC4P,IAAI,CAACf,WAAW,CAAC,CAACR,MAAM,CAAC,YAAY,CAAC;MACzD,MAAMgC,QAAQ,GAAGrQ,MAAM,CAAC4P,IAAI,CAAC5L,YAAY,CAAC,CACvCsM,IAAI,CAACtQ,MAAM,CAAC4P,IAAI,CAACf,WAAW,CAAC,EAAE,MAAM,CAAC;MAEzC;MACA,IAAI,CAACjM,gBAAgB,GAAGyN,QAAQ,GAAG,CAAC;MAEpC;MACA,MAAME,YAAY,GAAGF,QAAQ,GAAGT,IAAI,CAACY,aAAa;MAClD,MAAMC,aAAa,GAAGJ,QAAQ,GAAGT,IAAI,CAACc,cAAc;MAEpD;MACAd,IAAI,CAAC9M,aAAa,GAAG8M,IAAI,CAACe,iBAAiB,GAAGJ,YAAY;MAC1DX,IAAI,CAAC7M,cAAc,GAAG6M,IAAI,CAACgB,kBAAkB,GAAGH,aAAa;MAC7Db,IAAI,CAAClH,UAAU,GAAGkH,IAAI,CAACiB,cAAc,GAAGN,YAAY,GAAGE,aAAa;IACtE;IAEAK,GAAGA,CAAA;MACD,IAAIC,UAAU,GAAG,IAAI;MACrB,IAAIC,mBAAmB,GAAG,IAAI;MAE9B,KAAK,MAAMlD,MAAM,IAAI,IAAI,CAAC/C,QAAQ,EAAE;QAClC,IAAI,CAAC+C,MAAM,CAAC1E,WAAW,EAAE;UACvB,IAAI,CAAC0B,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE,GAAGc,MAAM,CAACpE,GAAG;WACtB,CAAC;UACFqH,UAAU,GAAG,KAAK;UAClB;QACF;QAEA,IAAIjD,MAAM,CAAChE,OAAO,EAAE;UAClB,KAAK,MAAMiE,GAAG,IAAID,MAAM,CAAChE,OAAO,EAAE;YAChC,IAAIiE,GAAG,CAAClK,kBAAkB,KAAK,CAAC,IAAIkK,GAAG,CAACtK,cAAc,EAAE;cACtD,IAAI,CAACqH,cAAc,CAAC+B,GAAG,CAAC;gBACtBC,QAAQ,EAAC,SAAS;gBAClBC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE;eACT,CAAC;cACFgE,mBAAmB,GAAG,KAAK;cAC3B;YACF;YAEA,IAAIjD,GAAG,CAACpF,QAAQ,IAAI,CAACoF,GAAG,CAACnF,gBAAgB,IAAImF,GAAG,CAAC/E,WAAW,IAAI,CAAC+E,GAAG,CAAC9E,SAAS,EAAE;cAC9E,IAAI,IAAI,CAAChC,UAAU,IAAI8G,GAAG,CAACvH,MAAM,EAAE;gBACjC,IAAI,CAACuH,GAAG,CAAC5J,QAAQ,EAAE;kBACjB,IAAI,CAAC2G,cAAc,CAAC+B,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACF+D,UAAU,GAAG,KAAK;kBAClB;gBACF;gBAEA,IAAIhD,GAAG,CAAC5J,QAAQ,CAACsK,KAAK,KAAK,GAAG,IAAI,CAACV,GAAG,CAACkD,eAAe,EAAE;kBACtD,IAAI,CAACnG,cAAc,CAAC+B,GAAG,CAAC;oBACtBC,QAAQ,EAAC,OAAO;oBAChBC,OAAO,EAAE,OAAO;oBAChBC,MAAM,EAAE;mBACT,CAAC;kBACF+D,UAAU,GAAG,KAAK;kBAClB;gBACF;gBAEA,IAAKhD,GAAG,CAAC5J,QAAQ,CAACsK,KAAK,KAAK,GAAG,IAAIV,GAAG,CAAC5J,QAAQ,CAACsK,KAAK,KAAK,GAAG,EAAG;kBAC9D,IAAI,CAACV,GAAG,CAAC7I,cAAc,EAAE;oBACvB,IAAI,CAAC4F,cAAc,CAAC+B,GAAG,CAAC;sBACtBC,QAAQ,EAAC,OAAO;sBAChBC,OAAO,EAAE,OAAO;sBAChBC,MAAM,EAAE;qBACT,CAAC;oBACF+D,UAAU,GAAG,KAAK;oBAClB;kBACF;kBACA,IAAI,CAAChD,GAAG,CAACzI,eAAe,EAAE;oBACxB,IAAI,CAACwF,cAAc,CAAC+B,GAAG,CAAC;sBACtBC,QAAQ,EAAC,OAAO;sBAChBC,OAAO,EAAE,OAAO;sBAChBC,MAAM,EAAE;qBACT,CAAC;oBACF+D,UAAU,GAAG,KAAK;oBAClB;kBACF;gBACF;cACF,CAAC,MAAM;gBACL,IAAIhD,GAAG,CAAC5G,aAAa,EAAE;kBACrB,IAAI,CAAC4G,GAAG,CAAC7I,cAAc,EAAE;oBACvB,IAAI,CAAC4F,cAAc,CAAC+B,GAAG,CAAC;sBACtBC,QAAQ,EAAC,OAAO;sBAChBC,OAAO,EAAE,OAAO;sBAChBC,MAAM,EAAE;qBACT,CAAC;oBACF+D,UAAU,GAAG,KAAK;oBAClB;kBACF;kBACA,IAAI,CAAChD,GAAG,CAACzI,eAAe,EAAE;oBACxB,IAAI,CAACwF,cAAc,CAAC+B,GAAG,CAAC;sBACtBC,QAAQ,EAAC,OAAO;sBAChBC,OAAO,EAAE,OAAO;sBAChBC,MAAM,EAAE;qBACT,CAAC;oBACF+D,UAAU,GAAG,KAAK;oBAClB;kBACF;gBACF,CAAC,MAAM;kBACL,IAAI,CAAChD,GAAG,CAACxJ,WAAW,EAAE;oBACpB,IAAI,CAACuG,cAAc,CAAC+B,GAAG,CAAC;sBACtBC,QAAQ,EAAC,OAAO;sBAChBC,OAAO,EAAE,OAAO;sBAChBC,MAAM,EAAE;qBACT,CAAC;oBACF+D,UAAU,GAAG,KAAK;oBAClB;kBACF;gBACF;cACF;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACC,mBAAmB,IAAI,CAACD,UAAU,EAAE;QACvC;MACF;MAEA,IAAI,CAAClG,0BAA0B,CAACqG,eAAe,CAAC,IAAI,CAACnG,QAAQ,CAAC,CAACuC,SAAS,CAAC;QACvEC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAAC2D,MAAM,KAAK,SAAS,EAAE;YACjC,IAAI,CAACrG,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,SAAS;cAClBC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;YACF,IAAI,CAAC0D,MAAM,EAAE;UACf,CAAC,MAAM,IAAI5D,QAAQ,CAAC2D,MAAM,KAAK,SAAS,EAAE;YACxC,IAAI,CAACrG,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,MAAM;cACfC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAAC5C,cAAc,CAAC+B,GAAG,CAAC;cACtBC,QAAQ,EAAC,OAAO;cAChBC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAEQ,QAAQ,CAACE;aAClB,CAAC;UACJ;QACF,CAAC;QACD2B,KAAK,EAAGA,KAAc,IAAI;UACxB,IAAI,CAACvE,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,OAAO;YAChBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACJ;IAEAoE,MAAMA,CAAA;MACJ,IAAI,CAACzG,MAAM,CAACsC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAClD;IAEAoE,gBAAgBA,CAAA;MACd,OAAO,IAAI,CAACtG,QAAQ,CAACyE,MAAM,CAAC,CAACC,KAAK,EAAE3B,MAAM,KAAI;QAC5C,OAAO2B,KAAK,IAAI3B,MAAM,CAAChE,OAAO,EAAE4E,MAAM,IAAI,CAAC,CAAC;MAC9C,CAAC,EAAE,CAAC,CAAC;IACP;IAEApN,SAASA,CAACwM,MAAW,EAAEwD,OAAY;MACjC,IAAIxD,MAAM,IAAIA,MAAM,CAACjE,kBAAkB,EAAE;QACvC,MAAM0H,KAAK,GAAGzD,MAAM,CAACjE,kBAAkB,CAAC2H,SAAS,CAAE5B,IAAS,IAC1DA,IAAI,CAACpO,WAAW,KAAK8P,OAAO,CAAC9P,WAAW,IACxCoO,IAAI,CAAClO,eAAe,KAAK4P,OAAO,CAAC5P,eAAe,CACjD;QACD,IAAI6P,KAAK,GAAG,CAAC,CAAC,EAAE;UACdzD,MAAM,CAACjE,kBAAkB,CAAC4H,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC1C,IAAI,CAACG,gBAAgB,CAAC5D,MAAM,CAAC;QAC/B;MACF;IACF;IAEQ4D,gBAAgBA,CAAC5D,MAAW;MAClC,MAAM6D,mBAAmB,GAAG,CAAC7D,MAAM,CAACjE,kBAAkB,IAAI,EAAE,EAAE2F,MAAM,CAAC,CAACC,KAAa,EAAEI,GAAQ,KAAI;QAC/F,OAAOJ,KAAK,IAAII,GAAG,CAACnO,eAAe,IAAI,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC;MAELoM,MAAM,CAACpF,UAAU,GAAGiJ,mBAAmB,GAAG,IAAI,CAAChI,WAAW,CAACmE,MAAM,CAAC;IACpE;IAEA;IACAhK,yBAAyBA,CAAC8L,IAAS;MACjC,IAAIgC,UAAU,CAAChC,IAAI,CAAClH,UAAU,CAAC,GAAGkJ,UAAU,CAAChC,IAAI,CAACiC,eAAe,CAAC,EAAE;QAClE,IAAID,UAAU,CAAChC,IAAI,CAAC/L,kBAAkB,CAAC,GAAG+N,UAAU,CAAChC,IAAI,CAACiC,eAAe,CAAC,IAAIjC,IAAI,CAAC/L,kBAAkB,IAAI,CAAC,EAAE;UAC1G,IAAI,CAACiH,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,SAAS;YAClBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE;WACT,CAAC;UACF4C,IAAI,CAAC/L,kBAAkB,GAAG,CAAC;QAC7B;MACF,CAAC,MAAM;QACL,IAAI+N,UAAU,CAAChC,IAAI,CAAC/L,kBAAkB,CAAC,IAAI+N,UAAU,CAAChC,IAAI,CAACiC,eAAe,CAAC,IAAIjC,IAAI,CAAC/L,kBAAkB,IAAI,CAAC,EAAE;UAC3G,IAAI,CAACiH,cAAc,CAAC+B,GAAG,CAAC;YACtBC,QAAQ,EAAC,SAAS;YAClBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE;WACT,CAAC;UACF4C,IAAI,CAAC/L,kBAAkB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA;IACAX,gBAAgBA,CAAC0M,IAAS;MACxB,IAAI,CAACzE,YAAY,GAAGyE,IAAI;MACxB,IAAI,CAACxE,eAAe,GAAGwE,IAAI,CAACzM,WAAW,IAAI,CAAC;MAC5C,IAAI,CAAC+H,qBAAqB,GAAG,IAAI;IACnC;IAEA;IACAjB,iBAAiBA,CAAA;MACf,IAAI,CAACiB,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IAC1B;IAEA;IACAjB,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACgB,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAAChI,WAAW,GAAG,IAAI,CAACiI,eAAe;QACpD,IAAI,IAAI,CAACA,eAAe,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACD,YAAY,CAAC1C,UAAU,GAAG,aAAa;QAC9C,CAAC,MAAM;UACL,IAAI,CAAC0C,YAAY,CAAC1C,UAAU,GAAG,aAAa;QAC9C;MACF;MACA,IAAI,CAACyC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IAC1B;IAEA;IACAvC,UAAUA,CAAC+G,IAAS;MAClB,OAAOA,IAAI,EAAE5L,YAAY,GAAGhE,MAAM,CAAC4P,IAAI,CAAC5L,YAAY,CAAC,CAAC2K,MAAM,EAAE,GAAG,IAAI;IACvE;IAEA;IACA7F,UAAUA,CAAC8G,IAAS;MAClB,OAAOA,IAAI,EAAEhB,eAAe,GAAG5O,MAAM,CAAC4P,IAAI,CAAChB,eAAe,CAAC,CAACD,MAAM,EAAE,GAAG,IAAI;IAC7E;IAEA;IACAhK,gBAAgBA,CAACiL,IAAS;MACxB,IAAI,CAAC7D,mBAAmB,GAAG6D,IAAI;MAC/B,IAAI,CAAC5D,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACG,WAAW,GAAG;QACjBC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAACF,iBAAiB,EAAE;QACjExG,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE,EAAE;QAChBJ,YAAY,EAAE,EAAE;QAChBM,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXqG,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;OACR;MACD,IAAI,CAACR,iBAAiB,GAAG,IAAI;IAC/B;IAEA;IACAjH,iBAAiBA,CAACN,WAAgB,EAAEqL,IAAS;MAC3C,IAAI,CAAC7D,mBAAmB,GAAG6D,IAAI;MAC/B,IAAI,CAAC5D,iBAAiB,GAAG,MAAM;MAC/B,IAAI,CAACC,cAAc,GAAG1H,WAAW;MACjC,IAAI,CAAC4H,WAAW,GAAG;QACjBC,gBAAgB,EAAE7H,WAAW,CAAC6H,gBAAgB;QAC9C1G,SAAS,EAAEnB,WAAW,CAACmB,SAAS;QAChCC,QAAQ,EAAEpB,WAAW,CAACoB,QAAQ;QAC9BC,YAAY,EAAErB,WAAW,CAACqB,YAAY;QACtCJ,YAAY,EAAEjB,WAAW,CAACiB,YAAY,IAAI,EAAE;QAC5CM,IAAI,EAAEvB,WAAW,CAACuB,IAAI;QACtBC,KAAK,EAAExB,WAAW,CAACwB,KAAK;QACxBC,OAAO,EAAEzB,WAAW,CAACyB,OAAO;QAC5BqG,KAAK,EAAE9H,WAAW,CAAC8H,KAAK;QACxBC,KAAK,EAAE/H,WAAW,CAAC+H,KAAK,IAAI;OAC7B;MACD,IAAI,CAACR,iBAAiB,GAAG,IAAI;IAC/B;IAEA;IACAtB,WAAWA,CAAA;MACT,IAAI,IAAI,CAACwB,iBAAiB,KAAK,KAAK,EAAE;QACpC,MAAM8F,UAAU,GAAG;UAAE,GAAG,IAAI,CAAC3F;QAAW,CAAE;QAE1C;QACA,IAAI,IAAI,CAACJ,mBAAmB,CAACjH,cAAc,EAAE;UAC3C,IAAI,CAACiH,mBAAmB,CAACjH,cAAc,CAACiN,IAAI,CAACD,UAAU,CAAC;QAC1D,CAAC,MAAM;UACL,IAAI,CAAC/F,mBAAmB,CAACjH,cAAc,GAAG,CAACgN,UAAU,CAAC;QACxD;QAEA;QACA,IAAI,CAAC/F,mBAAmB,CAACxH,WAAW,GAAGuN,UAAU;QAEjD;QACA,IAAI,CAAC/G,QAAQ,CAAC8C,OAAO,CAAEC,MAAW,IAAI;UACpC,IAAIA,MAAM,CAACkE,QAAQ,KAAK,IAAI,CAACjG,mBAAmB,CAACiG,QAAQ,EAAE;YACzDlE,MAAM,CAAChE,OAAO,EAAE+D,OAAO,CAAEoE,IAAS,IAAI;cACpC,IAAIA,IAAI,CAACnN,cAAc,EAAE;gBACvBmN,IAAI,CAACnN,cAAc,CAACiN,IAAI,CAAC;kBAAE,GAAGD;gBAAU,CAAE,CAAC;gBAC3C,IAAIG,IAAI,CAAC1N,WAAW,EAAE6H,gBAAgB,EAAE8F,QAAQ,CAAC,oBAAoB,CAAC,EAAE;kBACtED,IAAI,CAAClN,cAAc,GAAG,IAAI;gBAC5B;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMoN,cAAc,GAAG;UAAE,GAAG,IAAI,CAAChG;QAAW,CAAE;QAE9C;QACA,IAAI,CAACpB,QAAQ,CAAC8C,OAAO,CAAEC,MAAW,IAAI;UACpC,IAAIA,MAAM,CAACkE,QAAQ,KAAK,IAAI,CAACjG,mBAAmB,CAACiG,QAAQ,EAAE;YACzDlE,MAAM,CAAChE,OAAO,EAAE+D,OAAO,CAAEoE,IAAS,IAAI;cACpC,IAAIA,IAAI,CAACnN,cAAc,EAAE;gBACvB;gBACA,MAAMyM,KAAK,GAAGU,IAAI,CAACnN,cAAc,CAAC0M,SAAS,CACxChD,CAAM,IAAKA,CAAC,CAACpC,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,CACxE;gBACD,IAAImF,KAAK,GAAG,CAAC,CAAC,EAAE;kBACdU,IAAI,CAACnN,cAAc,CAACyM,KAAK,CAAC,GAAGY,cAAc;gBAC7C;gBAEA;gBACA,IAAIF,IAAI,CAAC1N,WAAW,EAAE6H,gBAAgB,KAAK,IAAI,CAACH,cAAc,CAACG,gBAAgB,EAAE;kBAC/E6F,IAAI,CAAC1N,WAAW,GAAG4N,cAAc;gBACnC;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAACrG,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC5B;IAEA;IACA3B,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAAC0B,iBAAiB,KAAK,KAAK,EAAE;QACpC,IAAI,CAACE,iBAAiB,EAAE;MAC1B;MACA,IAAI,CAACJ,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACE,cAAc,GAAG,IAAI;IAC5B;IAEA;IACAxH,qBAAqBA,CAACmL,IAAS;MAC7B,IAAIA,IAAI,CAACrL,WAAW,EAAE6H,gBAAgB,EAAE8F,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACtEtC,IAAI,CAAC7K,cAAc,GAAG,IAAI;MAC5B,CAAC,MAAM;QACL6K,IAAI,CAAC7K,cAAc,GAAG,KAAK;MAC7B;IACF;IAEA;IACAuC,YAAYA,CAACwG,MAAW,EAAE8B,IAAS;MACjC,IAAI9B,MAAM,CAAChE,OAAO,EAAE;QAClB,MAAMyH,KAAK,GAAGzD,MAAM,CAAChE,OAAO,CAAC0H,SAAS,CAAEzD,GAAQ,IAAKA,GAAG,KAAK6B,IAAI,CAAC;QAClE,IAAI2B,KAAK,GAAG,CAAC,CAAC,EAAE;UACdzD,MAAM,CAAChE,OAAO,CAAC2H,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC;MACF;IACF;IAAC,QAAAa,CAAA,G;uBAxkCU3H,oBAAoB,EAAAvK,EAAA,CAAAmS,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArS,EAAA,CAAAmS,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAtS,EAAA,CAAAmS,iBAAA,CAAAI,EAAA,CAAAC,0BAAA,GAAAxS,EAAA,CAAAmS,iBAAA,CAAAM,EAAA,CAAA1S,cAAA;IAAA;IAAA,QAAA2S,EAAA,G;YAApBnI,oBAAoB;MAAAoI,SAAA;MAAAC,QAAA,GAAA5S,EAAA,CAAA6S,kBAAA,CAFpB,CAAC9S,cAAc,CAAC;MAAA+S,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRzBnT,EAFJ,CAAAM,cAAA,aAA8B,aACsC,aAC9C;UAAAN,EAAA,CAAAC,MAAA,cAAO;UAAAD,EAAA,CAAAM,cAAA,cAAyC;UAAAN,EAAA,CAAAC,MAAA,GAAyB;;UAAOD,EAAP,CAAAO,YAAA,EAAO,EAAM;UAEtGP,EADF,CAAAM,cAAA,aAA2C,gBACkD;UAAnBN,EAAA,CAAAU,UAAA,mBAAA2S,sDAAA;YAAA,OAASD,GAAA,CAAAlC,MAAA,EAAQ;UAAA,EAAC;UAAClR,EAAA,CAAAO,YAAA,EAAS;UACpGP,EAAA,CAAAM,cAAA,gBACwD;UAAhBN,EAAA,CAAAU,UAAA,mBAAA4S,sDAAA;YAAA,OAASF,GAAA,CAAAxC,GAAA,EAAK;UAAA,EAAC;UAE3D5Q,EAF4D,CAAAO,YAAA,EAAS,EAC7D,EACF;UACNP,EAAA,CAAAkC,UAAA,KAAAqR,oCAAA,mBAA6F;UA6V/FvT,EAAA,CAAAO,YAAA,EAAM;UAGNP,EAAA,CAAAM,cAAA,mBAQqB;UAPnBN,EAAA,CAAAkD,gBAAA,2BAAAsQ,iEAAApQ,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAApI,qBAAA,EAAA5H,MAAA,MAAAgQ,GAAA,CAAApI,qBAAA,GAAA5H,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAU/BpD,EAFJ,CAAAM,cAAA,cAAwC,eACuB,iBAClC;UAAAN,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACvCP,EAAA,CAAAM,cAAA,yBAQyC;UANvCN,EAAA,CAAAkD,gBAAA,2BAAAuQ,sEAAArQ,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAlI,eAAA,EAAA9H,MAAA,MAAAgQ,GAAA,CAAAlI,eAAA,GAAA9H,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UASnCpD,EAFI,CAAAO,YAAA,EAAgB,EACZ,EACF;UACNP,EAAA,CAAAkC,UAAA,KAAAwR,4CAAA,0BAAgC;UAMlC1T,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAM,cAAA,oBAQqB;UAPnBN,EAAA,CAAAkD,gBAAA,2BAAAyQ,iEAAAvQ,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAxH,iBAAA,EAAAxI,MAAA,MAAAgQ,GAAA,CAAAxH,iBAAA,GAAAxI,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAYzBpD,EAJN,CAAAM,cAAA,cAAwC,eAEF,eAC2B,iBACpC;UAAAN,EAAA,CAAAC,MAAA,oBAAY;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAC3CP,EAAA,CAAAM,cAAA,iBAAgF;UAA/CN,EAAA,CAAAkD,gBAAA,2BAAA0Q,8DAAAxQ,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAzG,SAAA,EAAApC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAzG,SAAA,GAAApC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UACtEpD,EADE,CAAAO,YAAA,EAAgF,EAC5E;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACrC;UAAAN,EAAA,CAAAC,MAAA,mBAAW;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACzCP,EAAA,CAAAM,cAAA,iBAA8E;UAA9CN,EAAA,CAAAkD,gBAAA,2BAAA2Q,8DAAAzQ,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAxG,QAAA,EAAArC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAxG,QAAA,GAAArC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UACpEpD,EADE,CAAAO,YAAA,EAA8E,EAC1E;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACjC;UAAAN,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClDP,EAAA,CAAAM,cAAA,iBAAsF;UAAlDN,EAAA,CAAAkD,gBAAA,2BAAA4Q,8DAAA1Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAvG,YAAA,EAAAtC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAvG,YAAA,GAAAtC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAC5EpD,EADE,CAAAO,YAAA,EAAsF,EAClF;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACjC;UAAAN,EAAA,CAAAC,MAAA,sBAAc;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChDP,EAAA,CAAAM,cAAA,iBAA6E;UAAzCN,EAAA,CAAAkD,gBAAA,2BAAA6Q,8DAAA3Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAA3G,YAAA,EAAAlC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAA3G,YAAA,GAAAlC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAC5EpD,EADE,CAAAO,YAAA,EAA6E,EACzE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACzC;UAAAN,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChCP,EAAA,CAAAM,cAAA,iBAAsE;UAA1CN,EAAA,CAAAkD,gBAAA,2BAAA8Q,8DAAA5Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAArG,IAAA,EAAAxC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAArG,IAAA,GAAAxC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAC5DpD,EADE,CAAAO,YAAA,EAAsE,EAClE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClCP,EAAA,CAAAM,cAAA,sBAOqB;UANTN,EAAA,CAAAkD,gBAAA,2BAAA+Q,mEAAA7Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAApG,KAAA,EAAAzC,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAApG,KAAA,GAAAzC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAQ7CpD,EADE,CAAAO,YAAA,EAAa,EACT;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACtC;UAAAN,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UACvCP,EAAA,CAAAM,cAAA,iBAA4E;UAA7CN,EAAA,CAAAkD,gBAAA,2BAAAgR,8DAAA9Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAnG,OAAA,EAAA1C,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAnG,OAAA,GAAA1C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAClEpD,EADE,CAAAO,YAAA,EAA4E,EACxE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAClCP,EAAA,CAAAM,cAAA,iBAAwE;UAA3CN,EAAA,CAAAkD,gBAAA,2BAAAiR,8DAAA/Q,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAE,KAAA,EAAA/I,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAE,KAAA,GAAA/I,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC9DpD,EADE,CAAAO,YAAA,EAAwE,EACpE;UAGJP,EADF,CAAAM,cAAA,eAA6D,iBACxC;UAAAN,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAO,YAAA,EAAQ;UAChCP,EAAA,CAAAM,cAAA,iBAA+D;UAAlCN,EAAA,CAAAkD,gBAAA,2BAAAkR,8DAAAhR,MAAA;YAAApD,EAAA,CAAAsD,kBAAA,CAAA8P,GAAA,CAAAnH,WAAA,CAAAG,KAAA,EAAAhJ,MAAA,MAAAgQ,GAAA,CAAAnH,WAAA,CAAAG,KAAA,GAAAhJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAGlEpD,EAHM,CAAAO,YAAA,EAA+D,EAC3D,EACF,EACF;UAENP,EAAA,CAAAkC,UAAA,KAAAmS,4CAAA,0BAAgC;UAMlCrU,EAAA,CAAAO,YAAA,EAAW;;;UAjd2DP,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAuB,WAAA,QAAA6R,GAAA,CAAA/D,QAAA,IAAyB;UAIvFrP,EAAA,CAAAQ,SAAA,GAAqC;UAArCR,EAAA,CAAAwC,UAAA,aAAA4Q,GAAA,CAAAjC,gBAAA,SAAqC;UAGqCnR,EAAA,CAAAQ,SAAA,EAAW;UAAXR,EAAA,CAAAwC,UAAA,YAAA4Q,GAAA,CAAAvI,QAAA,CAAW;UAqW3F7K,EAAA,CAAAQ,SAAA,EAAyB;UAAzBR,EAAA,CAAAsU,UAAA,CAAAtU,EAAA,CAAAuU,eAAA,KAAAC,GAAA,EAAyB;UAJzBxU,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAApI,qBAAA,CAAmC;UAOnChL,EANA,CAAAwC,UAAA,eAAc,oBACK,oBACA,wBAGI,mBACL;UAMZxC,EAAA,CAAAQ,SAAA,GAA6B;UAA7BR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAlI,eAAA,CAA6B;UAM7BlL,EAHA,CAAAwC,UAAA,wBAAuB,wBACA,iBACP,QAAA4Q,GAAA,CAAAnI,YAAA,kBAAAmI,GAAA,CAAAnI,YAAA,CAAAwJ,gBAAA,CACsB;UAkB5CzU,EAAA,CAAAQ,SAAA,GAAyB;UAAzBR,EAAA,CAAAsU,UAAA,CAAAtU,EAAA,CAAAuU,eAAA,KAAAG,GAAA,EAAyB;UAJzB1U,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAxH,iBAAA,CAA+B;UAO/B5L,EANA,CAAAwC,UAAA,eAAc,oBACK,oBACA,WAAA4Q,GAAA,CAAAtH,iBAAA,4CAEoD,wBAChD,mBACL;UAMqB9L,EAAA,CAAAQ,SAAA,GAAmC;UAAnCR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAzG,SAAA,CAAmC;UAKpCxF,EAAA,CAAAQ,SAAA,GAAkC;UAAlCR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAxG,QAAA,CAAkC;UAK9BzF,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAvG,YAAA,CAAsC;UAKtC1F,EAAA,CAAAQ,SAAA,GAAsC;UAAtCR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAA3G,YAAA,CAAsC;UAK9CtF,EAAA,CAAAQ,SAAA,GAA8B;UAA9BR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAArG,IAAA,CAA8B;UAM9C5F,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAApG,KAAA,CAA+B;UAK/B7F,EAJA,CAAAwC,UAAA,YAAA4Q,GAAA,CAAAtI,SAAA,CAAqB,mBAIH;UAOC9K,EAAA,CAAAQ,SAAA,GAAiC;UAAjCR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAnG,OAAA,CAAiC;UAKnC9F,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAE,KAAA,CAA+B;UAK/BnM,EAAA,CAAAQ,SAAA,GAA+B;UAA/BR,EAAA,CAAAwD,gBAAA,YAAA4P,GAAA,CAAAnH,WAAA,CAAAG,KAAA,CAA+B;;;;;;;SD5bvD7B,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}