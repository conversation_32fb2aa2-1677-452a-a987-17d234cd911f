<div class="flex flex-column">
  <div class="flex justify-content-between align-items-center mb-4">
    <div class="flex">Total: <span class="colorce3434 font-bold pl-2">{{getTotal() | currency}}</span></div>
    <div class="flex align-items-center gap-3">
      <button pButton pRipple type="button" class="greyButton" label="Cancel" (click)="cancel()"></button>
      <button pButton pRipple type="button" class="greenButton" label="Submit"
        [disabled]="getPaymentLength() === 0" (click)="pay()"></button>
    </div>
  </div>
  <div class="panel border-round p-3 text-sm flex flex-column" *ngFor="let dealer of formdata">
    <div class="flex justify-content-between align-items-center">
      <div class="flex align-items-center flex-1">
        <div class="flex w-3">
          <div class="text-right color2B2E3A pr-2">Dealer Code</div>
          <div class=" pl-2 color2B2E3A font-bold">{{dealer.dealerCode}}</div>
        </div>
        <div class="flex w-3">
          <div class=" text-right color2B2E3A pr-2">Dealer Name</div>
          <div class=" pl-2 color2B2E3A font-bold">{{dealer.dba}}</div>
        </div>
        <div class="flex w-3">
          <div class=" text-right color2B2E3A pr-2">Subtotal</div>
          <div class=" pl-2 font-bold colorce3434">{{getSubTotal(dealer) | currency}}</div>
        </div>
        <div class="flex w-3 align-items-center">
          <div class=" text-right color2B2E3A pr-2">Bank Account</div>
          <p-dropdown class="dropdownStyle pl-2 w-15rem"
                      [(ngModel)]="dealer.bankAccount"
                      [options]="dealer.bankAccountList"
                      placeholder="Select"
                      [optionLabel]="'reference'"
                      [optionValue]="'bankAccountDtoId'"
                      [filter]="true"
                      filterBy="reference">
            <ng-template pTemplate="selectedItem" let-item>
              {{item.bankAccountRanking}}.{{item.reference}}
            </ng-template>
            <ng-template pTemplate="item" let-item>
              <div class="text-ellipsis">{{item.bankAccountRanking}}.{{item.reference}}</div>
            </ng-template>
          </p-dropdown>
        </div>
      </div>
      <img [src]="!dealer.isExpanded ? './assets/img/upicon.png' : './assets/img/downicon.png'" class="cursor-pointer" (click)="toggleDealer(dealer)">
    </div>

    <div *ngIf="dealer.isExpanded">
      <div class="panel border-round p-3 flex mt-3 color2B2E3A relative" *ngFor="let feeItem of dealer.dealerLevelFeeList">
        <div class="absolute closeIcon z-1">
          <i class="pi pi-times-circle text-xl colorce3434 cursor-pointer" (click)="removeFee(dealer, feeItem)"></i>
        </div>
        <div class="flex w-full">
          <div class="flex w-4">
            <div class="text-right color2B2E3A pr-2">Fee Name:</div>
            <div class="pl-2">{{feeItem.description}}</div>
          </div>
          <div class="flex w-8">
            <div class="text-right color2B2E3A pr-2">Pay Amount:</div>
            <div class="pl-2 colorce3434">{{feeItem.remainingAmount | currency}}</div>
          </div>
        </div>
      </div>

    <div class="panel border-round p-3 flex mt-3 color2B2E3A relative" *ngFor="let item of dealer.dtoList">
      <div class="absolute closeIcon z-1">
        <i class="pi pi-times-circle text-xl colorce3434 cursor-pointer" (click)="removePayoff(dealer, item)"></i>
      </div>
      
      <!-- Vehicle Info -->
      <div class="w-3 flex flex-column gap-3 p-3 border-right-1 border-color-AEB9CC">
        <div class="flex align-items-center gap-2 font-bold"><i class="pi pi-car"></i> Vehicle Info</div>
        <div class="flex">{{item.vin}}</div>
        <div class="flex">{{item.year}} {{item.make}} {{item.model}}</div>
        <div class="flex align-items-center gap-3">
          <span class="color2B2E3A">Due Date:</span>
          <span class="color2B2E3A">{{item.nextDueDate | date:'MM/dd/yy'}}</span>
        </div>
      </div>

      <!-- Payment Detail -->
      <div class="w-4 flex flex-column gap-2 py-3 px-5 border-right-1 border-color-AEB9CC">
        <div class="flex align-items-center gap-2 font-bold"><i class="pi pi-book"></i> Payment Detail</div>
        
        <!-- Payment Type and Amount -->
        <div class="flex align-items-center justify-content-between gap-2">
          <div class="flex align-items-center gap-2 pl-3">
            <i class="pi pi-angle-down cursor-pointer" *ngIf="!payoff" (click)="showPayoff()"></i>
            <i class="pi pi-angle-up cursor-pointer" *ngIf="payoff" (click)="showPayoff()"></i>
            {{item.buttonName}}
          </div>
          <div>{{item.totalMoney | currency}}</div>
        </div>

        <!-- Payment Details Breakdown -->
        <div class="flex flex-column trbg border-round-sm color82808F" *ngIf="payoff">
          <div class="flex flex-column w-12 p-3 gap-2">
            <!-- Principal -->
            <div class="flex justify-content-between align-items-center">
              <div>Principal</div>
              <div>{{item.principal | currency}}</div>
            </div>

            <!-- Extra Amounts -->
            <div class="flex justify-content-between align-items-center" *ngFor="let fee of item.extraAmountList">
              <div>{{fee.feeName}}</div>
              <div>{{fee.amount | currency}}</div>
            </div>

            <!-- Interest -->
            <div class="flex justify-content-between align-items-center">
              <div>Interest</div>
              <div class="flex align-items-center gap-2">
                <i class="pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon"
                   pTooltip="Estimated Amount"
                   tooltipPosition="top"
                   *ngIf="isShowEstimation"></i>
                {{item.interestPrice | currency}}
              </div>
            </div>

            <!-- WIP -->
            <div class="flex justify-content-between align-items-center">
              <div>WIP</div>
              <div class="flex align-items-center gap-2">
                <i class="pi pi-question text-xs color4B78E8FF border-1 border-round-3xl questionIcon"
                   pTooltip="Estimated Amount"
                   tooltipPosition="top"
                   *ngIf="isShowEstimation"></i>
                {{item.insurancePrice | currency}}
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Payment -->
        <div class="flex justify-content-between align-items-center" *ngIf="!item.isPayOff && !item.isPartialPayment">
          <div class="flex align-items-center gap-2 pl-3">Additional Payment</div>
          <div class="flex align-items-center gap-2">
            <span>{{item.otherAmount | currency}}</span>
            <a class="text-primary cursor-pointer" (click)="inputOtherAmount(item)">Change</a>
          </div>
        </div>

        <!-- Schedule Date -->
        <div class="flex justify-content-between align-items-center">
          <div class="w-6 pl-3">Schedule Date</div>
          <div class="w-6">
            <p-calendar
              [(ngModel)]="item.scheduleDate"
              (onSelect)="scheduleDateChange(item)"
              [selectOtherMonths]="true"
              [showButtonBar]="false"
              [monthNavigator]="true"
              [yearNavigator]="true"
              [dateFormat]="'mm/dd/yy'"
              [showTime]="false"
              [showIcon]="false"
              [readonlyInput]="true"
              [minDate]="getMinDate(item)"
              [maxDate]="getMaxDate(item)"
              [disabled]="item.isOnlyPrincial"
              class="calendarStyle w-full">
            </p-calendar>
          </div>
        </div>

        <!-- Principal Only Section -->
        <div class="flex flex-column" *ngIf="item.buttonName === 'PayOff' && item.isShowPrincipal">
          <div class="w-12 flex gap-2 justify-content-between align-items-center">
            <div class="w-6">Principal Only</div>
            <p-checkbox
              class="checkbox w-6"
              [(ngModel)]="item.isOnlyPrincial"
              [binary]="true"
              inputId="principalOnly">
            </p-checkbox>
          </div>
        </div>

        <!-- Principal Only Payment -->
        <div class="flex flex-column gap-2" *ngIf="item.isOnlyPrincial">
          <div class="flex justify-content-between align-items-center">
            <div class="w-6">Payment</div>
            <div class="w-6">
              <p-inputGroup class="w-full">
                <p-inputGroupAddon>$</p-inputGroupAddon>
                <p-inputNumber class="inputNumberRadius w-full"
                              inputId="onlyPrincialAmount"
                              mode="decimal"
                              [minFractionDigits]="2"
                              [maxFractionDigits]="2"
                              [(ngModel)]="item.onlyPrincialAmount"
                              [maxlength]="14"
                              (ngModelChange)="onlyPrincipalAmountChange(item)">
                </p-inputNumber>
              </p-inputGroup>
            </div>
          </div>

          <div class="flex justify-content-between align-items-center">
            <div class="w-6">Schedule Date</div>
            <div class="w-6">
              <p-calendar
                [(ngModel)]="item.scheduleDate"
                [disabled]="true"
                [selectOtherMonths]="true"
                [showButtonBar]="true"
                [monthNavigator]="true"
                [yearNavigator]="true"
                [dateFormat]="'mm/dd/yy'"
                class="calendarStyle w-full">
              </p-calendar>
            </div>
          </div>
        </div>
      </div>
      <div class="w-5 flex flex-column gap-2 py-3 px-5" *ngIf="item.displayMail && !item.isTrusted">
        <!-- Title Header -->
        <div class="flex align-items-center gap-2 font-bold">
          <i class="pi pi-envelope"></i>
          <span>Title Shipping Info</span>
          <button pButton
                  type="button"
                  icon="pi pi-file"
                  class="p-button-rounded p-button-text ml-2"
                  [disabled]="!item.isHasTitleFile"
                  (click)="viewTitle(item)">
          </button>
        </div>

        <!-- Special Title Handling Checkbox -->
        <div class="w-full" *ngIf="holdSwitch">
          <p-checkbox
            class="checkbox"
            [(ngModel)]="item.isHold"
            [binary]="true"
            [disabled]="item.isOnlyPrincial"
            label="Special Title Handling">
          </p-checkbox>
        </div>

        <!-- Special Title Handling Section -->
        <div class="flex flex-column gap-2" *ngIf="holdSwitch && item.isHold">
          <!-- Release Date -->
          <div class="flex align-items-center" *ngIf="item.holdType">
            <div class="w-4">Release Date</div>
            <div class="w-8">
              <div *ngIf="item.holdType.value === 'T' || item.holdType.value === 'H'">
                <p-calendar
                  [(ngModel)]="item.titleReleaseDate"
                  [showButtonBar]="false"
                  [monthNavigator]="true"
                  [yearNavigator]="true"
                  [readonlyInput]="true"
                  [dateFormat]="'mm/dd/yy'"
                  [minDate]="getReleaseDateMinDate(item)"
                  [maxDate]="getReleaseDateMaxDate(item)"
                  [disabled]="item.isOnlyPrincial"
                  class="w-full">
                </p-calendar>
              </div>
              <div *ngIf="item.holdType.value === 'D'" class="p-2">
                {{item.titleReleaseHoldDate | date:'MM/dd/yyyy'}}
              </div>
            </div>
          </div>

          <!-- Special Title Type -->
          <div class="flex align-items-center">
            <div class="w-4">Special Title Type</div>
            <div class="w-8">
              <p-dropdown
                [(ngModel)]="item.holdType"
                [options]="holdTypeList"
                optionLabel="text"
                [disabled]="item.isOnlyPrincial"
                placeholder="Select"
                class="w-full">
              </p-dropdown>
            </div>
          </div>

          <!-- Shipping Method -->
          <div class="flex align-items-center" *ngIf="item.holdType">
            <div class="w-4">Shipping Method</div>
            <div class="w-8">
              <p-dropdown
                [(ngModel)]="item.mailFeeInfo"
                [options]="postageFee"
                optionLabel="text"
                [disabled]="item.isOnlyPrincial"
                placeholder="Select"
                class="w-full">
              </p-dropdown>
            </div>
          </div>

          <!-- UCC Provider Contact (Type T) -->
          <div class="flex flex-column gap-2" *ngIf="item.holdType?.value === 'T'">
            <div class="flex align-items-center">
              <div class="w-4">Shipping Contact</div>
              <div class="w-8">
                <p-dropdown
                  [(ngModel)]="item.holdContactInfo"
                  [options]="item.uccProviderList"
                  optionLabel="uccProviderName"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  class="w-full">
                  <ng-template pTemplate="selectedItem" let-selected>
                    {{selected.uccProviderName}} - {{selected.address}}
                  </ng-template>
                  <ng-template pTemplate="item" let-provider>
                    {{provider.uccProviderName}} - {{provider.address}}
                  </ng-template>
                </p-dropdown>
              </div>
            </div>

            <!-- UCC Provider Address -->
            <div class="flex align-items-start my-2" *ngIf="item.holdContactInfo?.uccProviderId">
              <div class="w-4">Shipping Address</div>
              <div class="w-8">
                <p class="m-0">{{item.holdContactInfo.uccProviderName}} Title Dept</p>
                <p class="m-0">{{item.holdContactInfo.address}}</p>
                <p class="m-0" *ngIf="item.holdContactInfo">
                  {{item.holdContactInfo.city}}, {{item.holdContactInfo.state}}, {{item.holdContactInfo.zipCode}}
                </p>
              </div>
            </div>
          </div>

          <!-- Dealer Contact (Type D or H) -->
          <div class="flex flex-column gap-2" *ngIf="item.holdType?.value === 'D' || item.holdType?.value === 'H'">
            <!-- Contact Selection -->
            <div class="flex align-items-center">
              <div class="w-4">Shipping Contact</div>
              <div class="w-8">
                <p-dropdown
                  [(ngModel)]="item.newContactInfo"
                  [options]="item.newContactDtoList"
                  optionLabel="contactName"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  (onChange)="shippingContactChange(item)"
                  class="w-full">
                </p-dropdown>
              </div>
            </div>

            <!-- Location Selection -->
            <div class="flex align-items-center">
              <div class="w-4">Shipping Location</div>
              <div class="w-8">
                <p-dropdown
                  [(ngModel)]="item.newLocationInfo"
                  [options]="item.newLocationDtoList"
                  optionLabel="address1"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  (onChange)="shippingContactChange(item)"
                  class="w-full">
                </p-dropdown>
              </div>
            </div>

            <!-- Address Display -->
            <div class="flex align-items-start my-2">
              <div class="w-4">Shipping Address</div>
              <div class="w-8">
                <p class="m-0">{{item.newContactInfo?.firstName}} {{item.newContactInfo?.lastName}}</p>
                <p class="m-0">{{item.newLocationInfo?.address1}}</p>
                <p class="m-0" *ngIf="item.newLocationInfo">
                  {{item.newLocationInfo.city}}, {{item.newLocationInfo.state}}, {{item.newLocationInfo.zipCode}}
                </p>
              </div>
            </div>
          </div>

          <!-- Note -->
          <div class="flex align-items-start" *ngIf="item.holdType">
            <div class="w-4">Note</div>
            <div class="w-8">
              <textarea pInputTextarea
                        [(ngModel)]="item.note"
                        [rows]="3"
                        [maxlength]="256"
                        class="w-full">
              </textarea>
            </div>
          </div>
        </div>

        <!-- Regular Shipping Section -->
        <div class="flex flex-column gap-2" *ngIf="!item.isHold || !holdSwitch">
          <!-- Release Date -->
          <div class="flex align-items-center">
            <div class="w-4">Release Date</div>
            <div class="w-8">
              <p-calendar
                [(ngModel)]="item.titleReleaseDate"
                [showButtonBar]="true"
                [monthNavigator]="true"
                [yearNavigator]="true"
                [readonlyInput]="true"
                [dateFormat]="'mm/dd/yy'"
                [minDate]="getReleaseDateMinDate(item)"
                [maxDate]="getReleaseDateMaxDate(item)"
                [disabled]="item.isOnlyPrincial"
                class="w-full">
              </p-calendar>
            </div>
            <div class="w-2 text-right" *ngIf="!holdSwitch">Hold</div>
            <div class="w-2" *ngIf="!holdSwitch">
              <p-checkbox
                class="checkbox"
                [(ngModel)]="item.isHold"
                [binary]="true"
                [disabled]="item.isOnlyPrincial">
              </p-checkbox>
            </div>
          </div>

          <!-- Shipping Method -->
          <div class="flex align-items-center">
            <div class="w-4">Shipping Method</div>
            <div class="w-8">
              <p-dropdown
                [(ngModel)]="item.mailFeeInfo"
                [options]="postageFee"
                optionLabel="text"
                [disabled]="item.isOnlyPrincial"
                placeholder="Select"
                class="w-full">
              </p-dropdown>
            </div>
          </div>

          <!-- Regular Contact Info -->
          <div class="flex flex-column gap-2" *ngIf="!item.contactSwitch">
            <!-- Contact Selection -->
            <div class="flex align-items-center">
              <div class="w-4">Shipping Contact</div>
              <div class="w-8 flex gap-2">
                <p-dropdown
                  [(ngModel)]="item.contactInfo"
                  [options]="item.contactDtoList"
                  optionLabel="contactReference"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  (onChange)="shippingContactChange(item)"
                  class="w-full">
                </p-dropdown>
                <button pButton
                        type="button"
                        icon="pi pi-plus"
                        class="p-button-rounded p-button-text"
                        [disabled]="item.isOnlyPrincial"
                        (click)="addContactDialog(item)">
                </button>
                <button pButton
                        type="button"
                        icon="pi pi-pencil"
                        class="p-button-rounded p-button-text"
                        [disabled]="!item.isDisabledEdit || item.isOnlyPrincial"
                        (click)="editContactDialog(item.contactInfo, item)">
                </button>
              </div>
            </div>

            <!-- Address Display -->
            <div class="flex align-items-start my-2">
              <div class="w-4">Shipping Address</div>
              <div class="w-8">
                <p class="m-0">{{item.contactInfo?.firstName}} {{item.contactInfo?.lastName}}</p>
                <p class="m-0">{{item.contactInfo?.addressLine1}}</p>
                <p class="m-0">{{item.contactInfo?.city}}, {{item.contactInfo?.state}}, {{item.contactInfo?.zipCode}}</p>
              </div>
            </div>
          </div>

          <!-- New Contact Info -->
          <div class="flex flex-column gap-2" *ngIf="item.contactSwitch">
            <!-- Contact Selection -->
            <div class="flex align-items-center">
              <div class="w-4">Shipping Contact</div>
              <div class="w-8">
                <p-dropdown
                  [(ngModel)]="item.newContactInfo"
                  [options]="item.newContactDtoList"
                  optionLabel="contactName"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  (onChange)="shippingContactChange(item)"
                  class="w-full">
                </p-dropdown>
              </div>
            </div>

            <!-- Location Selection -->
            <div class="flex align-items-center">
              <div class="w-4">Shipping Location</div>
              <div class="w-8">
                <p-dropdown
                  [(ngModel)]="item.newLocationInfo"
                  [options]="item.newLocationDtoList"
                  optionLabel="address1"
                  [disabled]="item.isOnlyPrincial"
                  placeholder="Select"
                  (onChange)="shippingContactChange(item)"
                  class="w-full">
                </p-dropdown>
              </div>
            </div>

            <!-- Address Display -->
            <div class="flex align-items-start my-2">
              <div class="w-4">Shipping Address</div>
              <div class="w-8">
                <p class="m-0">{{item.newContactInfo?.firstName}} {{item.newContactInfo?.lastName}}</p>
                <p class="m-0">{{item.newLocationInfo?.address1}}</p>
                <p class="m-0" *ngIf="item.newLocationInfo">
                  {{item.newLocationInfo.city}}, {{item.newLocationInfo.state}}, {{item.newLocationInfo.zipCode}}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="item.displayMail && item.isTrusted" class="w-5 flex flex-column gap-2 py-3 px-5">
        <div>Title Released</div>
      </div>
    </div>
  </div>
</div>

<!-- Other Amount Dialog -->
<p-dialog
  [(visible)]="showOtherAmountDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [style]="{width: '30vw'}"
  header="Other Amount"
  [closeOnEscape]="false"
  [closable]="false">
  <div class="flex flex-column gap-3 p-3">
    <div class="flex justify-content-between align-items-center">
      <label for="otherAmount">Amount</label>
      <p-inputNumber
        id="otherAmount"
        [(ngModel)]="tempOtherAmount"
        mode="currency"
        currency="USD"
        [minFractionDigits]="2"
        [maxFractionDigits]="2"
        [maxlength]="14"
        [max]="selectedItem?.otherAmountLimit">
      </p-inputNumber>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <button pButton pRipple type="button" label="Cancel" class="greyButton" (click)="cancelOtherAmount()"></button>
      <button pButton pRipple type="button" label="Confirm" class="greenButton" (click)="confirmOtherAmount()"></button>
    </div>
  </ng-template>
</p-dialog>

<!-- Contact Dialog -->
<p-dialog
  [(visible)]="showContactDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [style]="{width: '45vw'}"
  [header]="contactDialogMode === 'add' ? 'Add Contact' : 'Edit Contact'"
  [closeOnEscape]="false"
  [closable]="false">
  <div class="flex flex-column gap-3 p-3">
    <!-- Contact Form -->
    <div class="flex flex-column gap-3">
      <div class="flex justify-content-between align-items-center">
        <label for="firstName">First Name *</label>
        <input pInputText id="firstName" [(ngModel)]="contactForm.firstName" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="lastName">Last Name *</label>
        <input pInputText id="lastName" [(ngModel)]="contactForm.lastName" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="addressLine1">Address Line 1 *</label>
        <input pInputText id="addressLine1" [(ngModel)]="contactForm.addressLine1" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="addressLine2">Address Line 2</label>
        <input pInputText id="addressLine2" [(ngModel)]="contactForm.addressLine2" />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="city">City *</label>
        <input pInputText id="city" [(ngModel)]="contactForm.city" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="state">State *</label>
        <p-dropdown id="state"
                    [(ngModel)]="contactForm.state"
                    [options]="stateList"
                    optionLabel="text"
                    optionValue="value"
                    placeholder="Select"
                    required>
        </p-dropdown>
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="zipCode">Zip Code *</label>
        <input pInputText id="zipCode" [(ngModel)]="contactForm.zipCode" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="phone">Phone *</label>
        <input pInputText id="phone" [(ngModel)]="contactForm.phone" required />
      </div>

      <div class="flex justify-content-between align-items-center">
        <label for="email">Email</label>
        <input pInputText id="email" [(ngModel)]="contactForm.email" />
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <button pButton pRipple type="button" label="Cancel" class="greyButton" (click)="cancelContactDialog()"></button>
      <button pButton pRipple type="button" label="Save" class="greenButton" (click)="saveContact()"></button>
    </div>
  </ng-template>
</p-dialog>
