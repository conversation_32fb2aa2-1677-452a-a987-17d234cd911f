.questionIcon{
  padding:2px;
}
.closeIcon{
  right:20px;
}

::ng-deep .p-calendar .p-datepicker {
  width: 360px !important;
}

:host ::ng-deep .inputNumberRadius .p-inputtext {
  border-radius: 0 !important;
  font-size: 14px;
}

:host ::ng-deep .inputNumberBorder .p-inputtext {
  border-right: 0;
}

::ng-deep {
  // 让所有输入框组件占满父元素
  .p-dropdown,
  .p-calendar,
  .p-inputtext,
  .p-inputtextarea,
  .p-inputnumber {
    width: 100% !important;
    
    input {
      width: 100% !important;
    }
  }

  .p-dropdown {
    .p-dropdown-label,
    .p-dropdown-item {
      font-size: 14px !important;
    }
  }

  .p-calendar {
    .p-inputtext {
      font-size: 14px !important;
    }
  }

  .p-datepicker {
    table td > span,
    table th > span {
      font-size: 14px !important;
    }
    .p-datepicker-header {
      .p-datepicker-title {
        .p-datepicker-month,
        .p-datepicker-year {
          font-size: 14px !important;
        }
      }
    }
  }

  .p-inputtext,
  .p-inputtextarea {
    font-size: 14px !important;
  }

  .p-checkbox-label {
    font-size: 14px !important;
  }
}
