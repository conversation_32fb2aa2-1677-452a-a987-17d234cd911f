import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { LoanSchedulePaymentService } from '../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-loan-proceed',
  templateUrl: './loan-proceed.component.html',
  styleUrl: './loan-proceed.component.scss',
  providers: [MessageService]
})
export class LoanProceedComponent implements OnInit {
  payoff: boolean = false;
  formdata: any[] = [];
  postageFee: any[] = [];
  stateList: any[] = [];
  holdTypeList: any[] = [];
  uccProviderList: any[] = [];
  holdSwitch: boolean = false;
  isShowEstimation: boolean = false;

  // Toggle dealer panel
  toggleDealer(dealer: any) {
    dealer.isExpanded = !dealer.isExpanded;
  }

  // Additional Payment dialog variables
  showOtherAmountDialog: boolean = false;
  selectedItem: any = null;
  tempOtherAmount: number = 0;

  // Calendar configuration
  calendarConfig: any = {
    showButtonBar: true,
    monthNavigator: true,
    yearNavigator: true,
    dateFormat: 'mm/dd/yy',
    showTime: false,
    showIcon: false,
    readonlyInput: true,
    appendTo: 'body'
  };

  // Contact Dialog
  showContactDialog: boolean = false;
  selectedContactItem: any = null;
  contactDialogMode: 'add' | 'edit' = 'add';
  editingContact: any = null;
  tempContactNumber: number = 0;

  // Contact Form
  contactForm = {
    contactReference: '',
    firstName: '',
    lastName: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    phone: '',
    email: ''
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private loanSchedulePaymentService: LoanSchedulePaymentService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    // 临时使用mock数据替代路由参数
    const mockData = {
      selectData: [
        {
          "loanId": "937f86a5-b25a-4a1b-98fe-bef8e61a1ff5",
          "creditLineId": "36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2",
          "vDealerLevelDto": null,
          "creditLineBucketId": "878b52e6-5d7c-4361-8c3b-5b221a2a27fd",
          "accountNumber": 351764,
          "cost": 2000,
          "currentCost": 2000,
          "sold": "",
          "soldOperationType": null,
          "soldDisp": "",
          "financeTag": null,
          "assetId": null,
          "titleStatus": "RE",
          "titleStatusDisplay": "Trusted Title Received",
          "interestDaily": 0,
          "insuranceDaily": 0,
          "interestPrice": 0,
          "insurancePrice": 0,
          "interestPriceTemp": 0,
          "insurancePriceTemp": 0,
          "isOnlyPrincial": false,
          "onlyPrincialAmount": 0,
          "isPastDue": true,
          "year": 2022,
          "fromDealerView": null,
          "isScheduled": false,
          "make": "BMW",
          "model": "320Li",
          "vin": "2GCEK19J245287165",
          "titleReleaseDate": null,
          "delayDays": 0,
          "titleReleaseHoldDate": null,
          "holdType": null,
          "uccProviderDto": null,
          "uccProviderId": null,
          "titleNote": null,
          "isHasTitleFile": false,
          "fileManagementUrl": null,
          "contactSwitch": false,
          "contactDtoList": null,
          "contactDto": null,
          "newLocationDtoList": null,
          "newLocationDto": null,
          "newContactDtoList": null,
          "newContactDto": null,
          "vinLast6": null,
          "maturituDate": "2023-02-28T00:00:00",
          "nextDueDate": "2022-12-30T00:00:00",
          "dueDate": "0001-01-01T00:00:00",
          "isHold": false,
          "isTrusted": false,
          "payOff": 90,
          "isPayOff": true,
          "isPartialPayment": false,
          "isCurtailment": false,
          "isShowPrincipal": false,
          "curtailment": 0,
          "otherAmount": 0,
          "otherAmountDisplay": 0,
          "paidAmount": 90,
          "scheduleDate": "2025-06-06T00:00:00",
          "scheduleDateMessage": null,
          "currentDate": null,
          "scheduleDateEnd": null,
          "delearId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "termId": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197",
          "termNo": 3,
          "dealerCode": "160625",
          "dealerName": null,
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "dba": "MY160625",
          "legalName": "MY160625",
          "buttonName": "PayOff",
          "termIdListStr": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,",
          "principalListStr": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,",
          "principalInterest": null,
          "effectiveDate": "2023-01-31T00:00:00",
          "payOffPrincipal": 0,
          "otherAmountLimit": 0,
          "payOffTermFee": 0,
          "payOffFeeItem": 0,
          "payOffInterest": 0,
          "payOffInsurance": 0,
          "partPrincipal": 0,
          "partTermFee": 0,
          "partFeeItem": 0,
          "partInterest": 0,
          "partInsurance": 0,
          "mailFee": 0,
          "mailFeeName": null,
          "principal": 0,
          "titleName": null,
          "titleContent": null,
          "titleShoping": null,
          "titlepostage": null,
          "contactsId": null,
          "newLocationId": null,
          "newContactId": null,
          "provincialMoney": 0,
          "totalMoney": 0,
          "tempTotalMoney": 0,
          "subtotal": 0,
          "chargeDay": 0,
          "chargeDayMoneyInterest": 0,
          "chargeDayMoneyInsurance": 0,
          "stockId": 214,
          "extraAmountMoney": 0,
          "displayMail": false,
          "vehicle": "287165-BMW 320Li 2022",
          "feeAmountNoReserve": 90,
          "reserveFee": 0,
          "extraAmountList": [
            {
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "extraAmountListPayoff": [
            {
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "extraAmountListPartial": [
            {
              "active": true,
              "fixAmount": 50,
              "tempAmount": 50,
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 0,
              "tempAmount": 0,
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 15,
              "tempAmount": 15,
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "payInfoList": null,
          "loanStatus": "L",
          "isDuePayOff": false,
          "isSoldPayOff": false,
          "nsfCount": 0,
          "waivedAmount": 0,
          "extensionHide": false,
          "paidPrinciple": 0,
          "paidTermFee": 0,
          "paidFee": 0,
          "paidPcr": 0,
          "paidInterest": 0,
          "paidInsurance": 0,
          "otherAmountDisable": true
        },
        {
          "loanId": "88f81a91-b4c3-491d-a511-05d95bd05103",
          "creditLineId": "36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2",
          "vDealerLevelDto": null,
          "creditLineBucketId": "878b52e6-5d7c-4361-8c3b-5b221a2a27fd",
          "accountNumber": 352478,
          "cost": 2000,
          "currentCost": 2000,
          "sold": "",
          "soldOperationType": "",
          "soldDisp": "",
          "financeTag": "",
          "assetId": null,
          "titleStatus": "PT",
          "titleStatusDisplay": "Pending Release Trusted",
          "interestDaily": 0,
          "insuranceDaily": 0,
          "interestPrice": 0,
          "insurancePrice": 0,
          "interestPriceTemp": 0,
          "insurancePriceTemp": 0,
          "isOnlyPrincial": false,
          "onlyPrincialAmount": 0,
          "isPastDue": true,
          "year": 2023,
          "fromDealerView": null,
          "isScheduled": false,
          "make": "BMW",
          "model": "320Li",
          "vin": "2GCEK19J245287243",
          "titleReleaseDate": null,
          "delayDays": 0,
          "titleReleaseHoldDate": null,
          "holdType": null,
          "uccProviderDto": null,
          "uccProviderId": null,
          "titleNote": null,
          "isHasTitleFile": false,
          "fileManagementUrl": null,
          "contactSwitch": false,
          "contactDtoList": null,
          "contactDto": null,
          "newLocationDtoList": null,
          "newLocationDto": null,
          "newContactDtoList": null,
          "newContactDto": null,
          "vinLast6": null,
          "maturituDate": "2023-02-28T00:00:00",
          "nextDueDate": "2022-12-30T00:00:00",
          "dueDate": "0001-01-01T00:00:00",
          "isHold": false,
          "isTrusted": false,
          "payOff": 2313,
          "isPayOff": true,
          "isPartialPayment": false,
          "isCurtailment": false,
          "isShowPrincipal": false,
          "curtailment": 0,
          "otherAmount": 0,
          "otherAmountDisplay": 0,
          "paidAmount": 2313,
          "scheduleDate": "2025-06-06T00:00:00",
          "scheduleDateMessage": null,
          "currentDate": null,
          "scheduleDateEnd": null,
          "delearId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "termId": "6208475b-7444-403f-a1be-437d9be86200",
          "termNo": 3,
          "dealerCode": "160625",
          "dealerName": null,
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "dba": "MY160625",
          "legalName": "MY160625",
          "buttonName": "PayOff",
          "termIdListStr": "7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,",
          "principalListStr": "7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,",
          "principalInterest": null,
          "effectiveDate": "2023-01-31T00:00:00",
          "payOffPrincipal": 2000,
          "otherAmountLimit": 0,
          "payOffTermFee": 0,
          "payOffFeeItem": 0,
          "payOffInterest": 0,
          "payOffInsurance": 0,
          "partPrincipal": 2000,
          "partTermFee": 0,
          "partFeeItem": 0,
          "partInterest": 0,
          "partInsurance": 0,
          "mailFee": 0,
          "mailFeeName": null,
          "principal": 0,
          "titleName": null,
          "titleContent": null,
          "titleShoping": null,
          "titlepostage": null,
          "contactsId": null,
          "newLocationId": null,
          "newContactId": null,
          "provincialMoney": 0,
          "totalMoney": 0,
          "tempTotalMoney": 0,
          "subtotal": 0,
          "chargeDay": 0,
          "chargeDayMoneyInterest": 0,
          "chargeDayMoneyInsurance": 0,
          "stockId": 312,
          "extraAmountMoney": 0,
          "displayMail": false,
          "vehicle": "287243-BMW 320Li 2023",
          "feeAmountNoReserve": 65,
          "reserveFee": 248,
          "extraAmountList": [
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "extraAmountListPayoff": [
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "RESERVE",
              "feeName": "Reserve",
              "createDate": null,
              "amount": 248,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "extraAmountListPartial": [
            {
              "active": true,
              "fixAmount": 2000,
              "tempAmount": 2000,
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "active": false,
              "fixAmount": 0,
              "tempAmount": 0,
              "feeId": "RESERVE",
              "feeName": "Reserve",
              "createDate": null,
              "amount": 248,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 15,
              "tempAmount": 15,
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "payInfoList": null,
          "loanStatus": "L",
          "isDuePayOff": false,
          "isSoldPayOff": false,
          "nsfCount": 0,
          "waivedAmount": 0,
          "extensionHide": false,
          "paidPrinciple": 0,
          "paidTermFee": 0,
          "paidFee": 0,
          "paidPcr": 0,
          "paidInterest": 0,
          "paidInsurance": 0,
          "otherAmountDisable": true
        }
      ],
      "selectDealerFee": [
        {
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "paymentScheduleFeeItemId": "1003812b-784e-4bee-8a21-b52c6da253e0",
          "feeName": "Audit Fee",
          "description": "Audit Fee",
          "remainingAmount": 100,
          "chargedOffRemainingAmount": 0,
          "paidStatus": "UP",
          "dba": "MY160625",
          "name": "MY160625",
          "dealerCode": "160625",
          "scheduleDate": "0001-01-01T00:00:00",
          "dueDate": null,
          "createDate": "2023-06-20T09:29:05",
          "postpaymentDealerFeeAmount": 100,
          "removeDay": true,
          "feeType": "P"
        },
        {
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "paymentScheduleFeeItemId": "d51e507d-c072-425d-b44a-a6005e3b7db4",
          "feeName": "OC Defense Fee_CO",
          "description": "OC Defense Fee_CO",
          "remainingAmount": 123,
          "chargedOffRemainingAmount": 0,
          "paidStatus": "UP",
          "dba": "MY160625",
          "name": "MY160625",
          "dealerCode": "160625",
          "scheduleDate": "0001-01-01T00:00:00",
          "dueDate": null,
          "createDate": "2025-05-29T02:44:35",
          "postpaymentDealerFeeAmount": 123,
          "removeDay": true,
          "feeType": "P"
        }
      ]
    };

    const data = JSON.stringify(mockData);

    if (!data) {
      this.messageService.add({severity:'error', summary: 'Error', detail: 'No payment data found'});
      this.router.navigate(['/loan/schedule-payment']);
      return;
    }

    const paymentData = {
      selectData: JSON.parse(data).selectData,
      selectDealerFee: JSON.parse(data).selectDealerFee
    };

    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({
      next: (response: any) => {
        if (response.code === 200) {
          this.formdata = response.data.results || [];
          this.postageFee = response.data.postageFee || [];
          this.stateList = response.data.stateList || [];
          this.holdTypeList = response.data.releaseHoldTypeList || [];
          this.uccProviderList = response.data.uccProviderList || [];
          this.holdSwitch = response.data.releaseHoldSwitch || false;

          this.formdata.forEach((dealer: any) => {
            dealer.isExpanded = true;
            if (dealer.dtoList) {
              dealer.dtoList.forEach((dto: any) => {
                if (dto.isPayOff && !dto.isPartialPayment) {
                  if (dto.contactSwitch) {
                    dto.newLocationInfo = dto.newLocationDto;
                    dto.newContactInfo = dto.newContactDto;
                  } else {
                    dto.contactInfo = dto.contactDtoList?.[0];
                  }
                  dto.mailFeeInfo = this.postageFee[0];

                  const releaseDate = moment(new Date(dto.scheduleDate))
                    .add(dto.delayDays, 'days')
                    .format('MM/DD/YYYY');
                  dto.titleReleaseDate = releaseDate;

                  if (this.holdSwitch && dto.isHold) {
                    const holdTypes = this.holdTypeList.filter((c: any) => c.value === 'H');
                    if (holdTypes.length > 0) {
                      dto.holdType = holdTypes[0];
                    }
                  }
                }

                if(dto.scheduleDate) {
                  dto.scheduleDate = moment(dto.scheduleDate).toDate();
                }
                if(dto.scheduleDateEnd) {
                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();
                }
                if(dto.currentDate) {
                  dto.currentDate = moment(dto.currentDate).toDate();
                }

                if (dealer.paymentSource && dealer.bankAccountList?.length) {
                  const matchingAccount = dealer.bankAccountList.find((account: any) =>
                    account.bankAccountDtoId === dealer.paymentSource.bankAccountId
                  );
                  if (matchingAccount) {
                    dealer.bankAccount = matchingAccount.bankAccountDtoId;
                  }
                }
              });
            }
          });

          // 初始化数据
          this.formdata.forEach((dealer: any) => {
            if (dealer.dtoList) {
              dealer.dtoList.forEach((item: any) => {
                this.scheduleDateChange(item);
              });
            }
          });
        } else {
          this.messageService.add({severity:'error', summary: 'Error', detail: response.message});
          this.router.navigate(['/loan/schedule-payment']);
        }
      },
      error: (error: Error) => {
        this.messageService.add({severity:'error', summary: 'Error', detail: 'Failed to get payment info'});
        console.error('Error getting payment info:', error);
      }
    });

  }

  showPayoff() {
    this.payoff = !this.payoff;
  }

  getTotal(): number {
    return this.formdata.reduce((total, dealer) => {
      const dealerTotal = this.getSubTotal(dealer);
      return total + dealerTotal;
    }, 0);
  }

  getSubTotal(dealerInfo: any): number {
    let total = 0;

    // Add dealer level fees
    if (dealerInfo.dealerLevelFeeList) {
      dealerInfo.dealerLevelFeeList.forEach((fee: any) => {
        total += fee.remainingAmount || 0;
      });
    }

    // Add dto list items
    if (dealerInfo.dtoList) {
      dealerInfo.dtoList.forEach((item: any) => {
        total += item.totalMoney || 0;
        total += parseFloat(item.otherAmount || 0); // 添加otherAmount
      });
    }

    return total;
  }

  getDatePickerConfig(scheduleDate: string, scheduleDateEnd: string): any {
    const config = { ...this.calendarConfig };
    if (scheduleDate) {
      config.minDate = new Date(scheduleDate);
      config.defaultDate = new Date(scheduleDate);
    }
    if (scheduleDateEnd) {
      config.maxDate = new Date(scheduleDateEnd);
    }
    return config;
  }

  scheduleDateChange(item: any) {
    const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');
    item.titleReleaseDate = releaseDate;

    // Calculate date difference to determine if estimation should be shown
    const now = moment(item.currentDate).format('MM/DD/YYYY');
    const scheduleDate = moment(item.scheduleDate).format('MM/DD/YYYY');
    const diffDays = Math.abs(moment(scheduleDate).diff(moment(now), 'days'));
    
    // Show estimation indicator if scheduled date is in the future
    this.isShowEstimation = diffDays > 0;

    // Calculate interest and insurance adjustments
    const diffInterest = diffDays * item.interestDaily;
    const diffInsurance = diffDays * item.insuranceDaily;
    
    // Update prices with differences
    item.interestPrice = item.interestPriceTemp + diffInterest;
    item.insurancePrice = item.insurancePriceTemp + diffInsurance;
    
    // Update total money
    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;
  }

  pay() {
    let isContinue = true;
    let isOnlyPrincipalSave = true;

    for (const dealer of this.formdata) {
      if (!dealer.bankAccount) {
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: `${dealer.dba}'s Bank Account is required`
        });
        isContinue = false;
        break;
      }

      if (dealer.dtoList) {
        for (const dto of dealer.dtoList) {
          if (dto.onlyPrincialAmount === 0 && dto.isOnlyPrincial) {
            this.messageService.add({
              severity:'warning',
              summary: 'Warning',
              detail: 'Only Principal amount should be greater than 0!'
            });
            isOnlyPrincipalSave = false;
            break;
          }

          if (dto.isPayOff && !dto.isPartialPayment && dto.displayMail && !dto.isTrusted) {
            if (this.holdSwitch && dto.isHold) {
              if (!dto.holdType) {
                this.messageService.add({
                  severity:'error',
                  summary: 'Error',
                  detail: 'Special Title Type is required'
                });
                isContinue = false;
                break;
              }

              if (dto.holdType.value === 'T' && !dto.holdContactInfo) {
                this.messageService.add({
                  severity:'error',
                  summary: 'Error',
                  detail: 'Shipping contact is required'
                });
                isContinue = false;
                break;
              }

              if ((dto.holdType.value === 'D' || dto.holdType.value === 'H')) {
                if (!dto.newContactInfo) {
                  this.messageService.add({
                    severity:'error',
                    summary: 'Error',
                    detail: 'Shipping contact is required'
                  });
                  isContinue = false;
                  break;
                }
                if (!dto.newLocationInfo) {
                  this.messageService.add({
                    severity:'error',
                    summary: 'Error',
                    detail: 'Shipping location is required'
                  });
                  isContinue = false;
                  break;
                }
              }
            } else {
              if (dto.contactSwitch) {
                if (!dto.newContactInfo) {
                  this.messageService.add({
                    severity:'error',
                    summary: 'Error',
                    detail: 'Shipping contact is required'
                  });
                  isContinue = false;
                  break;
                }
                if (!dto.newLocationInfo) {
                  this.messageService.add({
                    severity:'error',
                    summary: 'Error',
                    detail: 'Shipping location is required'
                  });
                  isContinue = false;
                  break;
                }
              } else {
                if (!dto.contactInfo) {
                  this.messageService.add({
                    severity:'error',
                    summary: 'Error',
                    detail: 'Shipping contact is required'
                  });
                  isContinue = false;
                  break;
                }
              }
            }
          }
        }
      }
    }

    if (!isOnlyPrincipalSave || !isContinue) {
      return;
    }

    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({
      next: (response: any) => {
        if (response.status === 'success') {
          this.messageService.add({
            severity:'success',
            summary: 'Success',
            detail: response.results
          });
          this.cancel();
        } else if (response.status === 'warning') {
          this.messageService.add({
            severity:'warn',
            summary: 'Warning',
            detail: response.results
          });
        } else {
          this.messageService.add({
            severity:'error',
            summary: 'Error',
            detail: response.results
          });
        }
      },
      error: (error: unknown) => {
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: 'Failed to process payment'
        });
      }
    });
  }

  cancel() {
    this.router.navigate(['/loan/schedule-payment']);
  }

  getPaymentLength(): number {
    return this.formdata.reduce((total, dealer) => {
      return total + (dealer.dtoList?.length || 0);
    }, 0);
  }

  removeFee(dealer: any, feeItem: any) {
    if (dealer && dealer.dealerLevelFeeList) {
      const index = dealer.dealerLevelFeeList.findIndex((item: any) =>
        item.description === feeItem.description &&
        item.remainingAmount === feeItem.remainingAmount
      );
      if (index > -1) {
        dealer.dealerLevelFeeList.splice(index, 1);
        this.recalculateTotal(dealer);
      }
    }
  }

  private recalculateTotal(dealer: any) {
    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total: number, fee: any) => {
      return total + (fee.remainingAmount || 0);
    }, 0);

    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);
  }

  // 处理仅本金支付的金额变更
  onlyPrincipalAmountChange(item: any) {
    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {
      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {
        this.messageService.add({
          severity:'warning',
          summary: 'Warning',
          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'
        });
        item.onlyPrincialAmount = 0;
      }
    } else {
      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {
        this.messageService.add({
          severity:'warning',
          summary: 'Warning',
          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'
        });
        item.onlyPrincialAmount = 0;
      }
    }
  }

  // 打开Other Amount输入对话框
  inputOtherAmount(item: any) {
    this.selectedItem = item;
    this.tempOtherAmount = item.otherAmount || 0;
    this.showOtherAmountDialog = true;
  }

  // 取消Other Amount输入
  cancelOtherAmount() {
    this.showOtherAmountDialog = false;
    this.selectedItem = null;
    this.tempOtherAmount = 0;
  }

  // 确认Other Amount输入
  confirmOtherAmount() {
    if (this.selectedItem) {
      this.selectedItem.otherAmount = this.tempOtherAmount;
      if (this.tempOtherAmount !== 0) {
        this.selectedItem.buttonName = 'OtherAmount';
      } else {
        this.selectedItem.buttonName = 'Curtailment';
      }
    }
    this.showOtherAmountDialog = false;
    this.selectedItem = null;
    this.tempOtherAmount = 0;
  }

  // 获取最小日期
  getMinDate(item: any): Date | null {
    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;
  }

  // 获取最大日期
  getMaxDate(item: any): Date | null {
    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;
  }

  // Add Contact Dialog
  addContactDialog(item: any) {
    this.selectedContactItem = item;
    this.contactDialogMode = 'add';
    this.contactForm = {
      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      phone: '',
      email: ''
    };
    this.showContactDialog = true;
  }

  // Edit Contact Dialog
  editContactDialog(contactInfo: any, item: any) {
    this.selectedContactItem = item;
    this.contactDialogMode = 'edit';
    this.editingContact = contactInfo;
    this.contactForm = {
      contactReference: contactInfo.contactReference,
      firstName: contactInfo.firstName,
      lastName: contactInfo.lastName,
      addressLine1: contactInfo.addressLine1,
      addressLine2: contactInfo.addressLine2 || '',
      city: contactInfo.city,
      state: contactInfo.state,
      zipCode: contactInfo.zipCode,
      phone: contactInfo.phone,
      email: contactInfo.email || ''
    };
    this.showContactDialog = true;
  }

  // Save Contact
  saveContact() {
    if (this.contactDialogMode === 'add') {
      const newContact = { ...this.contactForm };
      
      // Add to contact list
      if (this.selectedContactItem.contactDtoList) {
        this.selectedContactItem.contactDtoList.push(newContact);
      } else {
        this.selectedContactItem.contactDtoList = [newContact];
      }

      // Set as current contact
      this.selectedContactItem.contactInfo = newContact;

      // Update all loans for same dealer
      this.formdata.forEach((dealer: any) => {
        if (dealer.dealerId === this.selectedContactItem.dealerId) {
          dealer.dtoList?.forEach((loan: any) => {
            if (loan.contactDtoList) {
              loan.contactDtoList.push({ ...newContact });
              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {
                loan.isDisabledEdit = true;
              }
            }
          });
        }
      });
    } else {
      // Edit mode
      const updatedContact = { ...this.contactForm };
      
      // Update in all places
      this.formdata.forEach((dealer: any) => {
        if (dealer.dealerId === this.selectedContactItem.dealerId) {
          dealer.dtoList?.forEach((loan: any) => {
            if (loan.contactDtoList) {
              // Update in contact list
              const index = loan.contactDtoList.findIndex(
                (c: any) => c.contactReference === this.editingContact.contactReference
              );
              if (index > -1) {
                loan.contactDtoList[index] = updatedContact;
              }

              // Update current selection if matches
              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {
                loan.contactInfo = updatedContact;
              }
            }
          });
        }
      });
    }

    this.showContactDialog = false;
    this.selectedContactItem = null;
    this.editingContact = null;
  }

  // Cancel Contact Dialog
  cancelContactDialog() {
    if (this.contactDialogMode === 'add') {
      this.tempContactNumber--;
    }
    this.showContactDialog = false;
    this.selectedContactItem = null;
    this.editingContact = null;
  }

  // Handle shipping contact change
  shippingContactChange(item: any) {
    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {
      item.isDisabledEdit = true;
    } else {
      item.isDisabledEdit = false;
    }
  }

  // Remove payoff item
  removePayoff(dealer: any, item: any) {
    if (dealer.dtoList) {
      const index = dealer.dtoList.findIndex((dto: any) => dto === item);
      if (index > -1) {
        dealer.dtoList.splice(index, 1);
      }
    }
  }

  // View Title
  viewTitle(item: any) {
    if (item.fileManagementUrl) {
      window.open(item.fileManagementUrl, '_blank');
    }
  }
  
}
