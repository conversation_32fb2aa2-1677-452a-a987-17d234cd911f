import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { LoanSchedulePaymentService } from '../../../../service/loan/loan-schedule-payment/loan-schedule-payment.service';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-loan-proceed',
  templateUrl: './loan-proceed.component.html',
  styleUrl: './loan-proceed.component.scss',
  providers: [MessageService]
})
export class LoanProceedComponent implements OnInit {
  payoff: boolean = false;
  formdata: any[] = [];
  postageFee: any[] = [];
  stateList: any[] = [];
  holdTypeList: any[] = [];
  uccProviderList: any[] = [];
  holdSwitch: boolean = false;
  isShowEstimation: boolean = false;

  // Toggle dealer panel
  toggleDealer(dealer: any) {
    dealer.isExpanded = !dealer.isExpanded;
  }

  // Additional Payment dialog variables
  showOtherAmountDialog: boolean = false;
  selectedItem: any = null;
  tempOtherAmount: number = 0;

  // Calendar configuration
  calendarConfig: any = {
    showButtonBar: true,
    monthNavigator: true,
    yearNavigator: true,
    dateFormat: 'mm/dd/yy',
    showTime: false,
    showIcon: false,
    readonlyInput: true,
    appendTo: 'body'
  };

  // Contact Dialog
  showContactDialog: boolean = false;
  selectedContactItem: any = null;
  contactDialogMode: 'add' | 'edit' = 'add';
  editingContact: any = null;
  tempContactNumber: number = 0;

  // Contact Form
  contactForm = {
    contactReference: '',
    firstName: '',
    lastName: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    phone: '',
    email: ''
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private loanSchedulePaymentService: LoanSchedulePaymentService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    // 临时使用mock数据替代路由参数
    const mockData = {
      selectData: [
        {
          "loanId": "937f86a5-b25a-4a1b-98fe-bef8e61a1ff5",
          "creditLineId": "36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2",
          "vDealerLevelDto": null,
          "creditLineBucketId": "878b52e6-5d7c-4361-8c3b-5b221a2a27fd",
          "accountNumber": 351764,
          "cost": 2000,
          "currentCost": 2000,
          "sold": "",
          "soldOperationType": null,
          "soldDisp": "",
          "financeTag": null,
          "assetId": null,
          "titleStatus": "RE",
          "titleStatusDisplay": "Trusted Title Received",
          "interestDaily": 0,
          "insuranceDaily": 0,
          "interestPrice": 0,
          "insurancePrice": 0,
          "interestPriceTemp": 0,
          "insurancePriceTemp": 0,
          "isOnlyPrincial": false,
          "onlyPrincialAmount": 0,
          "isPastDue": true,
          "year": 2022,
          "fromDealerView": null,
          "isScheduled": false,
          "make": "BMW",
          "model": "320Li",
          "vin": "2GCEK19J245287165",
          "titleReleaseDate": null,
          "delayDays": 0,
          "titleReleaseHoldDate": null,
          "holdType": null,
          "uccProviderDto": null,
          "uccProviderId": null,
          "titleNote": null,
          "isHasTitleFile": false,
          "fileManagementUrl": null,
          "contactSwitch": false,
          "contactDtoList": null,
          "contactDto": null,
          "newLocationDtoList": null,
          "newLocationDto": null,
          "newContactDtoList": null,
          "newContactDto": null,
          "vinLast6": null,
          "maturituDate": "2023-02-28T00:00:00",
          "nextDueDate": "2022-12-30T00:00:00",
          "dueDate": "0001-01-01T00:00:00",
          "isHold": false,
          "isTrusted": false,
          "payOff": 90,
          "isPayOff": true,
          "isPartialPayment": false,
          "isCurtailment": false,
          "isShowPrincipal": false,
          "curtailment": 0,
          "otherAmount": 0,
          "otherAmountDisplay": 0,
          "paidAmount": 90,
          "scheduleDate": "2025-06-06T00:00:00",
          "scheduleDateMessage": null,
          "currentDate": null,
          "scheduleDateEnd": null,
          "delearId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "termId": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197",
          "termNo": 3,
          "dealerCode": "160625",
          "dealerName": null,
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "dba": "MY160625",
          "legalName": "MY160625",
          "buttonName": "PayOff",
          "termIdListStr": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,",
          "principalListStr": "16dba8d3-7d46-4a1a-8b22-bb1a5cb98197,",
          "principalInterest": null,
          "effectiveDate": "2023-01-31T00:00:00",
          "payOffPrincipal": 0,
          "otherAmountLimit": 0,
          "payOffTermFee": 0,
          "payOffFeeItem": 0,
          "payOffInterest": 0,
          "payOffInsurance": 0,
          "partPrincipal": 0,
          "partTermFee": 0,
          "partFeeItem": 0,
          "partInterest": 0,
          "partInsurance": 0,
          "mailFee": 0,
          "mailFeeName": null,
          "principal": 0,
          "titleName": null,
          "titleContent": null,
          "titleShoping": null,
          "titlepostage": null,
          "contactsId": null,
          "newLocationId": null,
          "newContactId": null,
          "provincialMoney": 0,
          "totalMoney": 0,
          "tempTotalMoney": 0,
          "subtotal": 0,
          "chargeDay": 0,
          "chargeDayMoneyInterest": 0,
          "chargeDayMoneyInsurance": 0,
          "stockId": 214,
          "extraAmountMoney": 0,
          "displayMail": false,
          "vehicle": "287165-BMW 320Li 2022",
          "feeAmountNoReserve": 90,
          "reserveFee": 0,
          "extraAmountList": [
            {
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "extraAmountListPayoff": [
            {
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "extraAmountListPartial": [
            {
              "active": true,
              "fixAmount": 50,
              "tempAmount": 50,
              "feeId": null,
              "feeName": "TERM_3",
              "createDate": null,
              "amount": 50,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 0,
              "tempAmount": 0,
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 0,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 15,
              "tempAmount": 15,
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            }
          ],
          "payInfoList": null,
          "loanStatus": "L",
          "isDuePayOff": false,
          "isSoldPayOff": false,
          "nsfCount": 0,
          "waivedAmount": 0,
          "extensionHide": false,
          "paidPrinciple": 0,
          "paidTermFee": 0,
          "paidFee": 0,
          "paidPcr": 0,
          "paidInterest": 0,
          "paidInsurance": 0,
          "otherAmountDisable": true
        },
        {
          "loanId": "88f81a91-b4c3-491d-a511-05d95bd05103",
          "creditLineId": "36f0cf46-9d79-4ddb-b337-43ee0fa4ccf2",
          "vDealerLevelDto": null,
          "creditLineBucketId": "878b52e6-5d7c-4361-8c3b-5b221a2a27fd",
          "accountNumber": 352478,
          "cost": 2000,
          "currentCost": 2000,
          "sold": "",
          "soldOperationType": "",
          "soldDisp": "",
          "financeTag": "",
          "assetId": null,
          "titleStatus": "PT",
          "titleStatusDisplay": "Pending Release Trusted",
          "interestDaily": 0,
          "insuranceDaily": 0,
          "interestPrice": 0,
          "insurancePrice": 0,
          "interestPriceTemp": 0,
          "insurancePriceTemp": 0,
          "isOnlyPrincial": false,
          "onlyPrincialAmount": 0,
          "isPastDue": true,
          "year": 2023,
          "fromDealerView": null,
          "isScheduled": false,
          "make": "BMW",
          "model": "320Li",
          "vin": "2GCEK19J245287243",
          "titleReleaseDate": null,
          "delayDays": 0,
          "titleReleaseHoldDate": null,
          "holdType": null,
          "uccProviderDto": null,
          "uccProviderId": null,
          "titleNote": null,
          "isHasTitleFile": false,
          "fileManagementUrl": null,
          "contactSwitch": false,
          "contactDtoList": null,
          "contactDto": null,
          "newLocationDtoList": null,
          "newLocationDto": null,
          "newContactDtoList": null,
          "newContactDto": null,
          "vinLast6": null,
          "maturituDate": "2023-02-28T00:00:00",
          "nextDueDate": "2022-12-30T00:00:00",
          "dueDate": "0001-01-01T00:00:00",
          "isHold": false,
          "isTrusted": false,
          "payOff": 2313,
          "isPayOff": true,
          "isPartialPayment": false,
          "isCurtailment": false,
          "isShowPrincipal": false,
          "curtailment": 0,
          "otherAmount": 0,
          "otherAmountDisplay": 0,
          "paidAmount": 2313,
          "scheduleDate": "2025-06-06T00:00:00",
          "scheduleDateMessage": null,
          "currentDate": null,
          "scheduleDateEnd": null,
          "delearId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "termId": "6208475b-7444-403f-a1be-437d9be86200",
          "termNo": 3,
          "dealerCode": "160625",
          "dealerName": null,
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "dba": "MY160625",
          "legalName": "MY160625",
          "buttonName": "PayOff",
          "termIdListStr": "7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,",
          "principalListStr": "7ce07ad1-e2c0-4a76-a6de-f87acdd57ab6,aaf2048d-4e57-4869-8b31-ca727182b6a2,6208475b-7444-403f-a1be-437d9be86200,",
          "principalInterest": null,
          "effectiveDate": "2023-01-31T00:00:00",
          "payOffPrincipal": 2000,
          "otherAmountLimit": 0,
          "payOffTermFee": 0,
          "payOffFeeItem": 0,
          "payOffInterest": 0,
          "payOffInsurance": 0,
          "partPrincipal": 2000,
          "partTermFee": 0,
          "partFeeItem": 0,
          "partInterest": 0,
          "partInsurance": 0,
          "mailFee": 0,
          "mailFeeName": null,
          "principal": 0,
          "titleName": null,
          "titleContent": null,
          "titleShoping": null,
          "titlepostage": null,
          "contactsId": null,
          "newLocationId": null,
          "newContactId": null,
          "provincialMoney": 0,
          "totalMoney": 0,
          "tempTotalMoney": 0,
          "subtotal": 0,
          "chargeDay": 0,
          "chargeDayMoneyInterest": 0,
          "chargeDayMoneyInsurance": 0,
          "stockId": 312,
          "extraAmountMoney": 0,
          "displayMail": false,
          "vehicle": "287243-BMW 320Li 2023",
          "feeAmountNoReserve": 65,
          "reserveFee": 248,
          "extraAmountList": [
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "extraAmountListPayoff": [
            {
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "feeId": "RESERVE",
              "feeName": "Reserve",
              "createDate": null,
              "amount": 248,
              "payFlag": null
            },
            {
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "extraAmountListPartial": [
            {
              "active": true,
              "fixAmount": 2000,
              "tempAmount": 2000,
              "feeId": null,
              "feeName": "Principal",
              "createDate": null,
              "amount": 2000,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            },
            {
              "active": false,
              "fixAmount": 0,
              "tempAmount": 0,
              "feeId": "RESERVE",
              "feeName": "Reserve",
              "createDate": null,
              "amount": 248,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 15,
              "tempAmount": 15,
              "feeId": "ADMIN_FEE",
              "feeName": "Admin Fee",
              "createDate": null,
              "amount": 15,
              "payFlag": null
            },
            {
              "active": true,
              "fixAmount": 25,
              "tempAmount": 25,
              "feeId": "TRUSTED_TITLE_FEE",
              "feeName": "Trusted Title Fee",
              "createDate": null,
              "amount": 25,
              "payFlag": null
            }
          ],
          "payInfoList": null,
          "loanStatus": "L",
          "isDuePayOff": false,
          "isSoldPayOff": false,
          "nsfCount": 0,
          "waivedAmount": 0,
          "extensionHide": false,
          "paidPrinciple": 0,
          "paidTermFee": 0,
          "paidFee": 0,
          "paidPcr": 0,
          "paidInterest": 0,
          "paidInsurance": 0,
          "otherAmountDisable": true
        }
      ],
      "selectDealerFee": [
        {
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "paymentScheduleFeeItemId": "1003812b-784e-4bee-8a21-b52c6da253e0",
          "feeName": "Audit Fee",
          "description": "Audit Fee",
          "remainingAmount": 100,
          "chargedOffRemainingAmount": 0,
          "paidStatus": "UP",
          "dba": "MY160625",
          "name": "MY160625",
          "dealerCode": "160625",
          "scheduleDate": "0001-01-01T00:00:00",
          "dueDate": null,
          "createDate": "2023-06-20T09:29:05",
          "postpaymentDealerFeeAmount": 100,
          "removeDay": true,
          "feeType": "P"
        },
        {
          "dealerId": "8a89b13d-90ae-4fef-9a58-88eb13a7a78d",
          "paymentScheduleFeeItemId": "d51e507d-c072-425d-b44a-a6005e3b7db4",
          "feeName": "OC Defense Fee_CO",
          "description": "OC Defense Fee_CO",
          "remainingAmount": 123,
          "chargedOffRemainingAmount": 0,
          "paidStatus": "UP",
          "dba": "MY160625",
          "name": "MY160625",
          "dealerCode": "160625",
          "scheduleDate": "0001-01-01T00:00:00",
          "dueDate": null,
          "createDate": "2025-05-29T02:44:35",
          "postpaymentDealerFeeAmount": 123,
          "removeDay": true,
          "feeType": "P"
        }
      ]
    };

    const data = JSON.stringify(mockData);

    if (!data) {
      this.messageService.add({severity:'error', summary: 'Error', detail: 'No payment data found'});
      this.router.navigate(['/loan/schedule-payment']);
      return;
    }

    const paymentData = {
      selectData: JSON.parse(data).selectData,
      selectDealerFee: JSON.parse(data).selectDealerFee
    };

    this.loanSchedulePaymentService.getMakePaymentInfo(paymentData).subscribe({
      next: (response: any) => {
        if (response.code === 200) {
          this.formdata = response.data.results || [];
          this.postageFee = response.data.postageFee || [];
          this.stateList = response.data.stateList || [];
          this.holdTypeList = response.data.releaseHoldTypeList || [];
          this.uccProviderList = response.data.uccProviderList || [];
          this.holdSwitch = response.data.releaseHoldSwitch || false;

          // select default post address - First loop (lines 113-145 in AngularJS)
          for (let i = 0; i < this.formdata.length; i++) {
            const dtoList = this.formdata[i].dtoList;
            if (dtoList != null && dtoList != undefined) {
              for (let j = 0; j < dtoList.length; j++) {
                if (dtoList[j].isPayOff == true && !dtoList[j].isPartialPayment) {
                  if (dtoList[j].contactSwitch) {
                    this.formdata[i].dtoList[j].newLocationInfo = dtoList[j].newLocationDto;
                    this.formdata[i].dtoList[j].newContactInfo = dtoList[j].newContactDto;
                  } else {
                    this.formdata[i].dtoList[j].contactInfo = dtoList[j].contactDtoList[0];
                  }
                  this.formdata[i].dtoList[j].mailFeeInfo = this.postageFee[0];
                }

                const releaseDate = moment(this.formdata[i].dtoList[j].scheduleDate).add(this.formdata[i].dtoList[j].delayDays, 'days').format("MM/DD/YYYY");
                this.formdata[i].dtoList[j].titleReleaseDate = releaseDate;

                this.formdata[i].dtoList[j].uccProviderList = this.uccProviderList;
                if (this.holdSwitch && this.formdata[i].dtoList[j].isHold) {
                  const holdTypes = this.holdTypeList.filter((c: any) => c.value == "H");
                  if (holdTypes.length > 0) {
                    this.formdata[i].dtoList[j].holdType = holdTypes[0];
                  }
                }
              }
            }
          }

          // Second loop for bank account matching (lines 147-159 in AngularJS)
          for (let i = 0; i < this.formdata.length; i++) {
            if (this.formdata[i].paymentSource != null && this.formdata[i].paymentSource != undefined
              && this.formdata[i].bankAccountList != null && this.formdata[i].bankAccountList != undefined) {
              for (let j = 0; j < this.formdata[i].bankAccountList.length; j++) {
                if (this.formdata[i].paymentSource.bankAccountId == this.formdata[i].bankAccountList[j].bankAccountDtoId) {
                  this.formdata[i].bankAccount = this.formdata[i].bankAccountList[j];
                  break;
                }
              }
            }
          }

          // Convert date fields and set expanded state after processing
          this.formdata.forEach((dealer: any) => {
            dealer.isExpanded = true;
            if (dealer.dtoList) {
              dealer.dtoList.forEach((dto: any) => {
                if(dto.scheduleDate) {
                  dto.scheduleDate = moment(dto.scheduleDate).toDate();
                }
                if(dto.scheduleDateEnd) {
                  dto.scheduleDateEnd = moment(dto.scheduleDateEnd).toDate();
                }
                if(dto.currentDate) {
                  dto.currentDate = moment(dto.currentDate).toDate();
                }
              });
            }
          });

          this.formdata.forEach((dealer: any) => {
            if (dealer.dtoList) {
              dealer.dtoList.forEach((item: any) => {
                this.scheduleDateChange(item);
              });
            }
          });
        } else {
          this.messageService.add({severity:'error', summary: 'Error', detail: response.message});
          this.router.navigate(['/loan/schedule-payment']);
        }
      },
      error: (error: Error) => {
        this.messageService.add({severity:'error', summary: 'Error', detail: 'Failed to get payment info'});
        console.error('Error getting payment info:', error);
      }
    });

  }

  showPayoff() {
    this.payoff = !this.payoff;
  }

  getTotal(): number {
    let total = 0.0;
    if (this.formdata != null) {
      for (let i = 0; i < this.formdata.length; i++) {
        if (this.formdata[i].dealerLevelFeeList != null) {
          for (let j = 0; j < this.formdata[i].dealerLevelFeeList.length; j++) {
            if (isNaN(this.formdata[i].dealerLevelFeeList[j].remainingAmount) == false) {
              total += this.formdata[i].dealerLevelFeeList[j].remainingAmount;
            }
          }
        }
        if (this.formdata[i].dtoList != null) {
          for (let j = 0; j < this.formdata[i].dtoList.length; j++) {
            if (isNaN(this.formdata[i].dtoList[j].totalMoney) == false) {
              total += this.formdata[i].dtoList[j].totalMoney;
            }
            if (isNaN(this.formdata[i].dtoList[j].otherAmount) == false) {
              total += parseFloat(this.formdata[i].dtoList[j].otherAmount);
            }
          }
        }
      }
    }
    return total;
  }

  getSubTotal(dealerInfo: any): number {
    let total = 0;

    if (dealerInfo != null) {
      if (dealerInfo.dealerLevelFeeList != null) {
        for (let j = 0; j < dealerInfo.dealerLevelFeeList.length; j++) {
          total += dealerInfo.dealerLevelFeeList[j].remainingAmount;
        }
      }
      if (dealerInfo.dtoList != null) {
        for (let j = 0; j < dealerInfo.dtoList.length; j++) {
          total += dealerInfo.dtoList[j].totalMoney;
          total += parseFloat(dealerInfo.dtoList[j].otherAmount);
        }
      }
    }

    return total;
  }

  getDatePickerConfig(scheduleDate: string, scheduleDateEnd: string): any {
    const config = { ...this.calendarConfig };
    if (scheduleDate) {
      config.minDate = new Date(scheduleDate);
      config.defaultDate = new Date(scheduleDate);
    }
    if (scheduleDateEnd) {
      config.maxDate = new Date(scheduleDateEnd);
    }
    return config;
  }

  scheduleDateChange(item: any) {
    const releaseDate = moment(item.scheduleDate).add(item.delayDays, 'days').format('MM/DD/YYYY');
    item.titleReleaseDate = releaseDate;

    const now = moment(item.currentDate).format('MM/dd/yyyy');
    const diffDays = this.dateDiff(moment(item.scheduleDate).format('MM/DD/YYYY'), now);

    if (diffDays > 0) {
      this.isShowEstimation = true;
    } else {
      this.isShowEstimation = false;
    }

    const diffInterest = diffDays * item.interestDaily;
    const diffInsurance = diffDays * item.insuranceDaily;
    item.interestPrice = item.interestPriceTemp + diffInterest;
    item.insurancePrice = item.insurancePriceTemp + diffInsurance;

    item.totalMoney = item.tempTotalMoney + diffInterest + diffInsurance;
  }

  // DateDiff function matching AngularJS version exactly
  private dateDiff(sDate1: string, sDate2: string): number {
    let aDate, oDate1, oDate2, iDays;
    aDate = sDate1.split("/");
    // oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])    // 12-18-2006
    oDate1 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]);    // 12-18-2006
    aDate = sDate2.split("/");
    // oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])
    oDate2 = new Date(aDate[0] + '-' + aDate[1] + '-' + aDate[2]);
    iDays = parseInt((Math.abs(oDate1.getTime() - oDate2.getTime()) / 1000 / 60 / 60 / 24).toString());    // Converts the number of milliseconds to days
    return iDays;
  }

  pay() {
    let isContinue = true;
    let isOnlyPrincipalSave = true;

    // Use forEach to match AngularJS logic exactly
    this.formdata.forEach((data: any) => {
      if (data.bankAccount == null || data.bankAccount == undefined) {
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: `${data.dba}'s Bank Account is required, please edit`
        });
        isContinue = false;
      }

      let isSave = true;
      let isSaveL = true;
      if (data.dtoList != null && data.dtoList != undefined) {
        for (let i = 0; i < data.dtoList.length; i++) {
          if (data.dtoList[i].contactSwitch) {
            if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && !(data.dtoList[i].displayMail && data.dtoList[i].isTrusted)) {

              if (this.holdSwitch) {

                if (data.dtoList[i].isHold) {
                  if (data.dtoList[i].holdType == undefined || data.dtoList[i].holdType == null || data.dtoList[i].holdType.length == 0) {
                    this.messageService.add({
                      severity:'error',
                      summary: 'Error',
                      detail: 'Special Title Type is required'
                    });
                    isContinue = false;
                  }
                  else if (data.dtoList[i].holdType.value == "T" && (data.dtoList[i].holdContactInfo == undefined || data.dtoList[i].holdContactInfo == null || data.dtoList[i].holdContactInfo.length == 0)) {
                    this.messageService.add({
                      severity:'error',
                      summary: 'Error',
                      detail: 'Shipping contact is required'
                    });
                    isContinue = false;
                  }
                  else if (data.dtoList[i].holdType.value == "D" || data.dtoList[i].holdType.value == "H") {
                    if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {
                      isSave = false;
                    } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {
                      isSaveL = false;
                    }
                  }
                }
                else {
                  // Special Title Handling check off
                  if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {
                    isSave = false;
                  } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {
                    isSaveL = false;
                  }
                }
              } else {
                if (data.dtoList[i].newContactInfo == undefined || data.dtoList[i].newContactInfo == null || data.dtoList[i].newContactInfo.length == 0) {
                  isSave = false;
                } else if (data.dtoList[i].newLocationInfo == undefined || data.dtoList[i].newLocationInfo == null || data.dtoList[i].newLocationInfo.length == 0) {
                  isSaveL = false;
                }
              }
            }

          } else {
            if (data.dtoList[i].isPayOff && !data.dtoList[i].isPartialPayment && (data.dtoList[i].contactInfo == undefined || data.dtoList[i].contactInfo == null || data.dtoList[i].contactInfo.length == 0)) {
              isSave = false;
            }
          }

          if (data.dtoList[i].onlyPrincialAmount == 0 && data.dtoList[i].isOnlyPrincial == true) {
            this.messageService.add({
              severity:'warning',
              summary: 'Warning',
              detail: 'Only Principal amount should be greater than 0!'
            });
            isOnlyPrincipalSave = false;
          }
        }
      }

      if (!isSave) {
        isContinue = false;
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: 'Shipping contact is required'
        });
      } else if (!isSaveL) {
        isContinue = false;
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: 'Shipping location is required'
        });
      }
    });

    if (!isOnlyPrincipalSave) {
      return;
    }

    if (!isContinue) {
      return;
    }

    this.loanSchedulePaymentService.editMakePayment(this.formdata).subscribe({
      next: (response: any) => {
        if (response.status === 'success') {
          this.messageService.add({
            severity:'success',
            summary: 'Success',
            detail: response.results
          });
          this.cancel();
        } else if (response.status === 'warning') {
          this.messageService.add({
            severity:'warn',
            summary: 'Warning',
            detail: response.results
          });
        } else {
          this.messageService.add({
            severity:'error',
            summary: 'Error',
            detail: response.results
          });
        }
      },
      error: () => {
        this.messageService.add({
          severity:'error',
          summary: 'Error',
          detail: 'Failed to process payment'
        });
      }
    });
  }

  cancel() {
    this.router.navigate(['/loan/schedule-payment']);
  }

  getPaymentLength(): number {
    let totalLength = 0;
    for (let i = 0; i < this.formdata.length; i++) {
      if (this.formdata[i].dealerLevelFeeList != undefined) {
        totalLength += this.formdata[i].dealerLevelFeeList.length;
      }

      if (this.formdata[i].dtoList != undefined) {
        totalLength += this.formdata[i].dtoList.length;
      }
    }

    return totalLength;
  }

  removeFee(dealer: any, feeItem: any) {
    if (dealer && dealer.dealerLevelFeeList) {
      const index = dealer.dealerLevelFeeList.findIndex((item: any) =>
        item.description === feeItem.description &&
        item.remainingAmount === feeItem.remainingAmount
      );
      if (index > -1) {
        dealer.dealerLevelFeeList.splice(index, 1);
        this.recalculateTotal(dealer);
      }
    }
  }

  private recalculateTotal(dealer: any) {
    const dealerLevelFeeTotal = (dealer.dealerLevelFeeList || []).reduce((total: number, fee: any) => {
      return total + (fee.remainingAmount || 0);
    }, 0);

    dealer.totalMoney = dealerLevelFeeTotal + this.getSubTotal(dealer);
  }

  onlyPrincipalAmountChange(item: any) {
    if (parseFloat(item.totalMoney) > parseFloat(item.payOffPrincipal)) {
      if (parseFloat(item.onlyPrincialAmount) > parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {
        this.messageService.add({
          severity:'warning',
          summary: 'Warning',
          detail: 'Principal amount should not be greater than payoff principal amount and greater than 0!'
        });
        item.onlyPrincialAmount = 0;
      }
    } else {
      if (parseFloat(item.onlyPrincialAmount) >= parseFloat(item.payOffPrincipal) || item.onlyPrincialAmount <= 0) {
        this.messageService.add({
          severity:'warning',
          summary: 'Warning',
          detail: 'Principal amount should be less than payoff principal amount and greater than 0!'
        });
        item.onlyPrincialAmount = 0;
      }
    }
  }

  inputOtherAmount(item: any) {
    this.selectedItem = item;
    this.tempOtherAmount = item.otherAmount || 0;
    this.showOtherAmountDialog = true;
  }

  cancelOtherAmount() {
    this.showOtherAmountDialog = false;
    this.selectedItem = null;
    this.tempOtherAmount = 0;
  }

  confirmOtherAmount() {
    if (this.selectedItem) {
      this.selectedItem.otherAmount = this.tempOtherAmount;
      if (this.tempOtherAmount !== 0) {
        this.selectedItem.buttonName = 'OtherAmount';
      } else {
        this.selectedItem.buttonName = 'Curtailment';
      }
    }
    this.showOtherAmountDialog = false;
    this.selectedItem = null;
    this.tempOtherAmount = 0;
  }

  getMinDate(item: any): Date | null {
    return item?.scheduleDate ? moment(item.scheduleDate).toDate() : null;
  }

  getMaxDate(item: any): Date | null {
    return item?.scheduleDateEnd ? moment(item.scheduleDateEnd).toDate() : null;
  }

  // Get minimum date for release date picker (based on title release date)
  getReleaseDateMinDate(item: any): Date | null {
    console.log('getReleaseDateMinDate', item.titleReleaseDate);
    return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;
  }

  // Get maximum date for release date picker (based on title release date)
  getReleaseDateMaxDate(item: any): Date | null {
    console.log('getReleaseDateMaxDate', item.titleReleaseDate);
    return item?.titleReleaseDate ? moment(item.titleReleaseDate).toDate() : null;
  }

  // Add Contact Dialog
  addContactDialog(item: any) {
    this.selectedContactItem = item;
    this.contactDialogMode = 'add';
    this.contactForm = {
      contactReference: `Temporary_Contact_${++this.tempContactNumber}`,
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipCode: '',
      phone: '',
      email: ''
    };
    this.showContactDialog = true;
  }

  // Edit Contact Dialog
  editContactDialog(contactInfo: any, item: any) {
    this.selectedContactItem = item;
    this.contactDialogMode = 'edit';
    this.editingContact = contactInfo;
    this.contactForm = {
      contactReference: contactInfo.contactReference,
      firstName: contactInfo.firstName,
      lastName: contactInfo.lastName,
      addressLine1: contactInfo.addressLine1,
      addressLine2: contactInfo.addressLine2 || '',
      city: contactInfo.city,
      state: contactInfo.state,
      zipCode: contactInfo.zipCode,
      phone: contactInfo.phone,
      email: contactInfo.email || ''
    };
    this.showContactDialog = true;
  }

  // Save Contact
  saveContact() {
    if (this.contactDialogMode === 'add') {
      const newContact = { ...this.contactForm };
      
      // Add to contact list
      if (this.selectedContactItem.contactDtoList) {
        this.selectedContactItem.contactDtoList.push(newContact);
      } else {
        this.selectedContactItem.contactDtoList = [newContact];
      }

      // Set as current contact
      this.selectedContactItem.contactInfo = newContact;

      // Update all loans for same dealer
      this.formdata.forEach((dealer: any) => {
        if (dealer.dealerId === this.selectedContactItem.dealerId) {
          dealer.dtoList?.forEach((loan: any) => {
            if (loan.contactDtoList) {
              loan.contactDtoList.push({ ...newContact });
              if (loan.contactInfo?.contactReference?.includes('Temporary_Contact_')) {
                loan.isDisabledEdit = true;
              }
            }
          });
        }
      });
    } else {
      // Edit mode
      const updatedContact = { ...this.contactForm };
      
      // Update in all places
      this.formdata.forEach((dealer: any) => {
        if (dealer.dealerId === this.selectedContactItem.dealerId) {
          dealer.dtoList?.forEach((loan: any) => {
            if (loan.contactDtoList) {
              // Update in contact list
              const index = loan.contactDtoList.findIndex(
                (c: any) => c.contactReference === this.editingContact.contactReference
              );
              if (index > -1) {
                loan.contactDtoList[index] = updatedContact;
              }

              // Update current selection if matches
              if (loan.contactInfo?.contactReference === this.editingContact.contactReference) {
                loan.contactInfo = updatedContact;
              }
            }
          });
        }
      });
    }

    this.showContactDialog = false;
    this.selectedContactItem = null;
    this.editingContact = null;
  }

  // Cancel Contact Dialog
  cancelContactDialog() {
    if (this.contactDialogMode === 'add') {
      this.tempContactNumber--;
    }
    this.showContactDialog = false;
    this.selectedContactItem = null;
    this.editingContact = null;
  }

  // Handle shipping contact change
  shippingContactChange(item: any) {
    if (item.contactInfo?.contactReference?.includes('Temporary_Contact_')) {
      item.isDisabledEdit = true;
    } else {
      item.isDisabledEdit = false;
    }
  }

  // Remove payoff item
  removePayoff(dealer: any, item: any) {
    if (dealer.dtoList) {
      const index = dealer.dtoList.findIndex((dto: any) => dto === item);
      if (index > -1) {
        dealer.dtoList.splice(index, 1);
      }
    }
  }

  // View Title
  viewTitle(item: any) {
    if (item.fileManagementUrl) {
      window.open(item.fileManagementUrl, '_blank');
    }
  }
  
}
