<div class="flex flex-column">
  <div class="flex justify-content-between mb-3 w-12">
    <div class="font20 font-bold color3D3D3D">Title Management</div>
  </div>
  <div class="flex panel flex-wrap text-sm " (keydown)="onKeydown($event)">
    <div class="box flex flex-wrap w-12 p-3 border-round">
      <div class="col-3">
        <div class="color3D3D3D py-2">Title Status</div>
        <p-multiSelect
          maxFileSize="20971000"
          [options]="titleStatusSelect"
          [(ngModel)]="formData['titleStatus']"
          placeholder="Select"
          optionLabel="text"
          optionValue="value"
          display="chip"
          class="multiSelectStyle"
          [showClear]="true"/>
      </div>
      <div class="col-3">
        <div class="color3D3D3D py-2">Loans Status</div>
        <p-multiSelect
          maxFileSize="20971000"
          [options]="loanStatusSelect"
          [(ngModel)]="formData['loanStatus']"
          placeholder="Select"
          optionLabel="text"
          optionValue="value"
          display="chip"
          class="multiSelectStyle"
          [showClear]="true"/>
      </div>
      <div class="col-3">
        <div class="color3D3D3D py-2">Dealer Status</div>
        <p-multiSelect
          maxFileSize="20971000"
          [options]="dealerStatusSelect"
          [(ngModel)]="formData['dealerStatus']"
          placeholder="Select"
          optionLabel="text"
          optionValue="value"
          display="chip"
          class="multiSelectStyle"
          [showClear]="true"/>
      </div>
      <div class="col-3">
        <div class="color3D3D3D py-2">Loan ID</div>
        <input class="inputStyle" placeholder="Loan ID" type="text" pInputText (input)="onInput($event)" [(ngModel)]="formData['loanId']"/>
      </div>
      <div class="col-3" *ngIf="!collapsed">
        <div class="color3D3D3D py-2">Dealer</div>
        <input class="inputStyle" placeholder="Dealer Code or DBA Name or Legal Name" type="text" pInputText
               [(ngModel)]="formData['dealerName']"/>
      </div>
      <div class="col-3" *ngIf="!collapsed">
        <div class="color3D3D3D py-2">VIN ( or Last 6 digits VIN)</div>
        <input class="inputStyle" placeholder="VIN ( or Last 6 digits VIN)" type="text" pInputText
               [(ngModel)]="formData['vin']"/>
      </div>
      <div class="flex flex-column col-3" *ngIf="!collapsed">
        <div class="color3D3D3D py-2">Sent Date</div>
        <div class="flex align-items-center flex-1">
          <p-calendar
            [showButtonBar]="true"
            [(ngModel)]="formData['releasedDateFrom']"
            [selectOtherMonths]="true"
            placeholder="From"
            [monthNavigator]="true"
            [yearNavigator]="true"
            inputId="navigators"
            class="calendarStyle"
            [maxDate]="formData['releasedDateTo']"
          ></p-calendar>
          <div class="px-3"> - </div>
          <p-calendar
            [(ngModel)]="formData['releasedDateTo']"
            [selectOtherMonths]="true"
            placeholder="To"
            showButtonBar="true"
            [monthNavigator]="true"
            [yearNavigator]="true"
            inputId="navigators"
            class="calendarStyle"
            [minDate]="formData['releasedDateFrom']"
          ></p-calendar>
        </div>
      </div>
      <div class="flex flex-column col-3" *ngIf="!collapsed">
        <div class="color3D3D3D py-2">Received Date</div>
        <div class="flex align-items-center flex-1">
          <p-calendar
            [showButtonBar]="true"
            [(ngModel)]="formData['receivedDateFrom']"
            [selectOtherMonths]="true"
            placeholder="From"
            [monthNavigator]="true"
            [yearNavigator]="true"
            inputId="navigators"
            class="calendarStyle"
            [maxDate]="formData['receivedDateTo']"
          ></p-calendar>
          <div class="px-3"> - </div>
          <p-calendar
            [(ngModel)]="formData['receivedDateTo']"
            [selectOtherMonths]="true"
            placeholder="To"
            showButtonBar="true"
            [monthNavigator]="true"
            [yearNavigator]="true"
            inputId="navigators"
            class="calendarStyle"
            [minDate]="formData['receivedDateFrom']"
          ></p-calendar>
        </div>
      </div>
      <div class="col-3" *ngIf="!collapsed">
        <div class="color3D3D3D py-2">Title Release Type</div>
        <p-multiSelect
          maxFileSize="20971000"
          [options]="titleReleaseTypeSelect"
          [(ngModel)]="formData['titleReleaseType']"
          placeholder="Select"
          optionLabel="text"
          optionValue="value"
          display="chip"
          class="multiSelectStyle"
          [showClear]="true"/>
      </div>
      <div class="flex align-items-center justify-content-between w-12">
        <img ngSrc="assets/img/sortby_up_icon.png" class="cursor-pointer" alt="" height="24" width="24"
             (click)="collapsed = !collapsed" *ngIf="!collapsed">
        <img ngSrc="assets/img/sortby_down_icon.png" class="cursor-pointer" alt="" height="24" width="24"
             (click)="collapsed = !collapsed" *ngIf="collapsed">
        <div class="flex align-items-center gap-3">
          <button pButton pRipple type="button" icon="pi pi-replay" class="greyButton" label="Reset"
                  (click)="reset()"></button>
          <button pButton pRipple type="button" icon="pi pi-search" class="greenButton" label="Search"
                  (click)="searchData(0, pageSize, true)"><!-- Call searchData with resetPage=true to reset to first page --></button>
        </div>
      </div>
    </div>
  </div>
  <div class="box flex flex-wrap w-12 p-3 border-round mt-4">
    <div class="w-full">
      <div class="flex align-items-center justify-content-between mb-3">
        <span class="font20 font-bold color3D3D3D">Total Record: {{ paidTotal }}</span>
        <div class="flex align-content-center gap-3">
          <div class="flex">
            <button pButton pRipple type="button" class="greenButton" label="Export"
                    [disabled]="titleList.length === 0" (click)="export()"
            ><img src="./assets/img/Excel.png" class="mr-2"></button>
          </div>
          <div class="flex">
            <button pButton pRipple type="button" class="greenButton"  [label]="'Check In(' + loanIdList.length + ')'"
                    [disabled]="loanIdList.length === 0"
                    (click)="showCheckin(loanIdList)"
            ></button>
          </div>
        </div>
      </div>
      <p-table
        #dt
        [resetPageOnSort]="true"
        [(rows)]="pageSize"
        [showCurrentPageReport]="true"
        [(first)]="first"
        (onPage)="searchData($event.first, $event.rows)"
        [paginator]="paidTotal > 10"
        [totalRecords]="paidTotal"
        [value]="titleList"
        [rowsPerPageOptions]="[10,25,50,100]"
        [scrollable]="true"
        [lazy]="true"
        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} entries'"
        styleClass="p-datatable-striped tableWidth"
        [tableStyle]="{'min-width': '60rem'}">
        <ng-template pTemplate="header">
          <tr>
            <th >
              Loan ID
            </th>
            <th >Loan Type</th>
            <th >Dealer Code</th>
            <th >DBA Name</th>
            <th >Legal Name</th>
            <th pSortableColumn="SN" (click)="sort('SN')">Source Name
              <p-sortIcon field="SN"></p-sortIcon>
            </th>
            <th >VIN</th>
            <th  pSortableColumn="OP" (click)="sort('OP')">Original Principal
              <p-sortIcon field="OP"></p-sortIcon>
            </th>
            <th >Remaining Principal</th>
            <th >Title Status</th>
            <th >Title Release Type</th>
            <th >Loan Status</th>
            <th >Tracking No. from Source</th>
            <th  pSortableColumn="RD" (click)="sort('RD')">
              Received Date
              <p-sortIcon field="RD"></p-sortIcon>
            </th>
            <th >Sent Date</th>
            <th >Tracking No. to Dealer</th>
            <th >IsFunded</th>
            <th >IsScheduled</th>
            <th alignFrozen="right" pFrozenColumn style="text-align: center">Action</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-title  >
          <tr [ngClass]="{'dqRed': title.isInComplete === true}">
            <td [pTooltip]="accountNumber" tooltipPosition="top" [autoHide]="false">
              <span class="text-primary">{{ title.accountNumber }}</span>
            </td>
            <ng-template #accountNumber>
              <div class="flex flex-column gap-2 w-12">
                <div *ngFor="let detail of loanDetails" class="flex align-items-center gap-1 text-xs">
                  <span>{{ detail.label }} :</span>
                  <span class="font-bold">{{ title.loanTib[detail.key] }}</span>
                </div>
              </div>
            </ng-template>
            <td>{{ title.loanTib.loanTypeDisp }}</td>
            <td  [pTooltip]="reference" tooltipPosition="top" [autoHide]="false">
              <span class="text-primary preserve-space">{{ title.reference }}</span>
              <span class="label status-danger" *ngIf="title.dealerTib.orgStatus != 'A'">{{ title.dealerTib.orgStatus }}</span>
              <ng-template #reference>
                <div class="flex flex-column gap-2 w-12">
                  <div *ngFor="let detail of dealerDetails" class="flex align-items-center gap-1 text-xs">
                    <span>{{ detail.label }} :</span>
                    <ng-container *ngIf="detail.key === 'creditLineUtilization' || detail.key === 'creditLineInfo'; else normalDisplay">
                      <span class="font-bold">{{ title.dealerTib[detail.key] | currency }}</span>
                    </ng-container>
                    <ng-template #normalDisplay>
                      <span class="font-bold">{{ title.dealerTib[detail.key] }}</span>
                    </ng-template>
                  </div>
                </div>
              </ng-template>
            </td>
            <td [pTooltip]="reference" tooltipPosition="top" [autoHide]="false">
              <span class="text-primary preserve-space">{{ title.dba }}</span>
              <span class="label status-danger" *ngIf="title.dealerTib.orgStatus != 'A'">{{ title.dealerTib.orgStatus }}</span>
            </td>
            <td [pTooltip]="reference" tooltipPosition="top" [autoHide]="false">
              <span class="text-primary preserve-space">{{ title.legalName }}
                <span class="label status-danger" *ngIf="title.dealerTib.orgStatus != 'A'">{{ title.dealerTib.orgStatus }}</span>
              </span>
            </td>
            <td [pTooltip]="title.sourceName ? sourceName : ''" tooltipPosition="top" [autoHide]="false">
              <span class="text-primary preserve-space">{{ title.sourceName }}</span>
              <ng-template #sourceName >
                <div class="flex flex-column gap-2 w-12">
                <li *ngFor="let dealer of title.contactTibsM">
                  {{dealer.contactTypeText}} :
                  <span>
                  {{dealer.firstName}} {{dealer.lastName}},
                  <ng-container *ngIf="dealer.phone">
                    <span>tel:
                      <a class="phone-link" [href]="'tel:' + (dealer.phone | phone)">{{dealer.phone | phone}}</a>,
                    </span>
                  </ng-container>
                  <a class="email-link" [href]="'mailto:' + dealer.email">{{dealer.email}}</a>
                </span>
                </li>
                </div>
              </ng-template>
            </td>
            <td>{{ title.vin }}</td>
            <td>{{ title.cost | currency }}</td>
            <td>{{ title.currentCost | currency }}</td>
            <td>{{ title.titleStatusText }}</td>
            <td>{{ title.titleReleaseTypeText }}</td>
            <td>{{ title.loanStatusText }}</td>

            <td [class]="title.trackingNoFromSuplier ? 'cursor-pointer' : ''"
                (click)="title.trackingNoFromSuplier && openTrakingNumberFromDba(title.trackingNoFromSuplier)">
              <span class="text-primary">{{ title.trackingNoFromSuplier }}</span>
            </td>
            <td>{{ title.strReceivedDate | date:'MM/dd/yyyy' }}</td>
            <td>{{ title.strReleasedDate | date:'MM/dd/yyyy' }}</td>
            <td [class]="title.trackingNumber ? 'cursor-pointer' : ''"
                (click)="title.trackingNumber && openTrakingNumberFromDba(title.carrierFullUrl)">
              <span class="text-primary">{{ title.trackingNumber }}</span>
            </td>
            <td>
              <input type="checkbox" [disabled]="true" [ngModel]="title.isFunded">
            </td>
            <td>
              <input type="checkbox" [disabled]="true" [ngModel]="title.isScheduled">
            </td>
            <td alignFrozen="right" pFrozenColumn>
              <div class="flex align-items-center">
                <span pTooltip="View" tooltipPosition="top" class="link">
                <button class="action_button" pButton [disabled]="title.isHasTitleFile == false" [ngClass]="{'dqRed': title.isInComplete === true}"
                        pTooltip="View" tooltipPosition="top" (click)="viewFile(title.fileManagementUrl,title.accountNumber)">
                  <img src="./assets/img/title-view.png">
                </button>
                </span>
                <span class="mx-2">|</span>
                <span pTooltip="Details" tooltipPosition="top">
                <button class="action_button" pButton
                        pTooltip="Details" tooltipPosition="top" (click)="toDetailOrEdit(title, 'details')">
                  <img src="./assets/img/details.png">
                </button>
                </span>
                <span class="mx-2">|</span>
                <button class="action_button" pButton
                        pTooltip="Edit" tooltipPosition="top"  (click)="toDetailOrEdit(title, 'edit')" *ngIf="isBtnVisible('Title_Edit')">
                  <img src="./assets/img/edit.png">
                </button>
                <span class="mx-2">|</span>
                <span pTooltip="{{ checkAddDeleteByLoanId(title.loanId) ? 'Add' : 'Remove' }}" tooltipPosition="top">
                <button class="action_button" pButton [disabled]="checkStatus(title.titleStatus)" [ngClass]="{'dqRed': title.isInComplete === true}"
                        pTooltip="{{ checkAddDeleteByLoanId(title.loanId) ? 'Add' : 'Remove' }}" tooltipPosition="top"
                        *ngIf="isBtnVisible('Title_Add')"
                        (click)="handleAddDelete(title, checkAddDeleteByLoanId(title.loanId) ? 'add' : 'delete')">
                  <img [src]="checkAddDeleteByLoanId(title.loanId) ? './assets/img/add.png' : './assets/img/loan-transaction-cancel.png'">
                </button>
                </span>
              </div>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage"  *ngIf="titleList === undefined || titleList === null || titleList?.length === 0">
          <tr *ngIf="isTitleList">
            <td colspan="20" class="no_data">No Data Available</td>
          </tr>
          <tr *ngIf="!isTitleList">
            <td colspan="12"></td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>

<app-loan-pop-soldExt></app-loan-pop-soldExt>
<app-check-in (refreshDataEvent)="handleAction($event,first,pageSize)"></app-check-in>
<app-title-detail-and-edit></app-title-detail-and-edit>
<app-preview-component></app-preview-component>
