import {Component, ViewChild} from '@angular/core';
import {StorageService} from "../../../../service/common/storage/storage.service";
import {DataDictionaryService} from "../../../../service/system/data-dictionary/data-dictionary.service";
import {DatePipe} from "@angular/common";
import {LoadingService} from "../../../../service/common/loading/loadingService";
import {MsgService} from "../../../../service/common/msg/msg.service";
import {SEVERITY_ERROR, SEVERITY_SUCCESS} from "../../../../domain/constant";
import _ from "lodash";
import {Table} from "primeng/table";
import moment from "moment";
import {CheckInComponent} from "../pop/check-in/check-in.component";
import {TitleService} from "../../../../service/title/title.service";
import {PreviewComponent} from "../../../../component/preview/preview.component";
import {FileManagementService} from "../../../../service/system/file/file-management.service";
import {TitleDetailAndEditComponent} from "../pop/title-detail-and-edit/title-detail-and-edit.component";
import {FileUtilService} from "../../../../util/file-util/file-util.service";
import { PermissionService } from '../../../../service/common/premission/permission.service';

@Component({
  selector: 'app-title-management',
  templateUrl: './title-management.component.html',
  styleUrl: './title-management.component.scss'
})
export class TitleManagementComponent {
  @ViewChild('dt') dt!: Table;
  @ViewChild(PreviewComponent) previewComponent!: PreviewComponent;
  isTitleList: boolean = false;
  isTooltipVisible: boolean = false;
  titleList: any[] = [];
  isAdding = true;
  @ViewChild(TitleDetailAndEditComponent) titleDetailAndEditComponent!: TitleDetailAndEditComponent;
  isLoanList: boolean = false;
  loanIdList: any[] = [];
  loanStatusSelect: any[] = [];
  titleStatusSelect: any[] = [];
  loanTypeSelect: any[] = [];
  titleReleaseTypeSelect: any[] = [];
  dealerStatusSelect: any[] = [];
  isFundedSelect: any[] = [{value: true, text: 'Yes'}, {value: false, text: 'No'}];
  formData: any = {};
  sortOptions: any = {};
  fileBase64: any;
  first: number = 1;
  pageSize: number = 10;
  paidTotal: number = 0;
  collapsed: boolean = false;
  loan: any = {};
  asset: any = {};
  dataDictionaryList: any[] = []
  @ViewChild(CheckInComponent) checkIn!: CheckInComponent;
  searchParam: any = {};
  permissionList: any[] = [];

  loanDetails = [
    {label: 'Loan ID', key: 'accountNumber'},
    {label: 'Loan Type', key: 'loanTypeDisp'},
    {label: 'Vehicle Info', key: 'vehicleInfo'},
    {label: 'Vin', key: 'vin'},
    {label: 'Dealer Name', key: 'dealerName'},
    {label: 'Source Name', key: 'sourceName'},
    {label: 'Floored Date', key: 'acceptanceDate'},
    {label: 'Payoff Date', key: 'maturityDate'},
    {label: 'Loan Status', key: 'loanStatusDisP'}
  ];

  dealerDetails = [
    {label: 'Dealer Code', key: 'reference'},
    {label: 'Dealer Name', key: 'dba'},
    {label: 'Limit', key: 'creditLineInfo'},
    {label: 'Utilization', key: 'creditLineUtilization'},
    {label: 'Live Loans', key: 'liveLoans'},
    {label: 'On Hold', key: 'isOnHold'},
    {label: 'Boss Dealer', key: 'isBossDealer'},
    {label: 'Dealer Status', key: 'orgStatusText'}
  ];

  constructor(
    private readonly appStorageService: StorageService,
    private readonly dataDictionaryService: DataDictionaryService,
    private readonly datePipe: DatePipe,
    private readonly titleService: TitleService,
    private readonly fileManagementService: FileManagementService,
    private readonly loadingService: LoadingService,
    private readonly msgService: MsgService,
    private readonly fileUtil: FileUtilService,
    private readonly permissionService: PermissionService,
  ) {
  }

  ngOnInit(): void {
    this.loanIdList = []
    this.getButtonPermission();
    this.getDropdownData();
  }

  // Getting the data for a dropdown value by data dictionary
  getDropdownData(): void {
    this.loadingService.show();
    const search: {} = {
      condition: {
        dataDictionaryTypeName_In: ['LoanStatus', 'TitleStatus', 'LoanType', 'TitleReleaseType', 'OrgStatus']
      },
      sort: []
    }
    this.dataDictionaryService.getDataDictionaryItems(search).subscribe({
      next: (response: any): void => {
        this.loadingService.hide();
        const data: [] = response.data.content;
        if (data) {
          this.dataDictionaryList = data;
          this.loanStatusSelect =
            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'LoanStatus' &&
              ['Cancelled', 'Charged Off', 'Paid', 'Live', 'Recovered'].includes(item['text']));
          this.titleStatusSelect =
            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'TitleStatus');
          this.loanTypeSelect =
            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'LoanType');
          this.titleReleaseTypeSelect =
            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'TitleReleaseType');
          this.dealerStatusSelect =
            data.filter((item: any): boolean => item['dataDictionaryTypeName'] === 'OrgStatus');
        }
      },
      error: (): void => {
        this.loadingService.hide();
      }
    });
  }

  // Search data with optional page and page size parameters
  searchData(first = 0, rows = this.pageSize, resetPage: boolean = false): void {
    if (resetPage) {
      this.first = 0;
    } else {
      this.first = first;
    }
    this.pageSize = rows;
    let search: any = {
      Filters: this.formatSearchData(),
      PageIndex: first,
      PageSize: rows,
      Sorting: this.sortOptions['property'] ? { [this.sortOptions['property']]: this.sortOptions['direction'] } : {}
    }
    this.searchParam = search;
    this.titleManagementSearch(search);
    this.isTitleList = true;
  }

  openTrakingNumberFromDba(trackingNoFromSuplier: any) {
    window.open('https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber=' + trackingNoFromSuplier);
  }

  openTrakingNumber(url: any) {
    window.open(url);
  }

  showTooltip() {
    this.isTooltipVisible = true;
  }

  hideTooltip() {
    this.isTooltipVisible = false;
  }

  viewFile(fileManagementUrl: string,accountNumber: any): void {
    let body: any = {
      filePath: fileManagementUrl,
      fileFrom: 'FTP'
    }
    this.fileManagementService.getBase64Str(body).subscribe({
      next: (e: any): void => {
        if (e.data) {
          // download file if using IE
          if (window.navigator.userAgent.indexOf("Trident") > -1) {
            const titleFileName = 'Title' + accountNumber + '.pdf';
            this.fileUtil.downloadImageFromBase64(e.data, titleFileName);
          } else {
            this.previewComponent.showDialog(e.data, 'application/pdf');
          }
        } else {
          let data = {
            summary: 'Alert',
            severity: "warning",
            detail: "No title file available.",
            isLeftButton: false,
            leftButtonLabel: "",
            isRightButton: true,
            rightButtonLabel: 'OK'
          }
          this.msgService.notification(data);
        }
      }
    });
  }

  sort(param: any): void {

    if (!this.isTitleList) {
      return;
    }

    if (this.sortOptions['property'] === param && this.sortOptions['property'] != undefined) {
      if (this.sortOptions['direction'] === 'asc') {
        this.sortOptions['direction'] = 'desc';
      } else {
        this.sortOptions['direction'] = 'asc'
      }
    } else {
      this.sortOptions['property'] = param;
      this.sortOptions['direction'] = 'asc'
    }

    let sorting: any = {}
    sorting[this.sortOptions['property']] = this.sortOptions['direction']
    const search = {
      Filters: this.formatSearchData(),
      PageIndex: 0,
      PageSize: 10,
      Sorting: sorting
    }

    this.searchParam = search;
    this.titleManagementSearch(search);
  }

  titleManagementSearch(search: any): void {
    this.loadingService.show();
    this.titleService.searchTitleDataList(search).subscribe({
      next: (e: any): void => {
        this.loadingService.hide();
        if (e.data) {
          this.titleList = e.data['results'];
          this.paidTotal = e.data['total'];
          if (this.titleList && this.titleList.length > 0) {
            this.titleList.forEach(item => {
              if (item.loanTib && item.loanTib.acceptanceDate) {
                item.loanTib.acceptanceDate = this.formatDateString(item.loanTib.acceptanceDate);
                item.loanTib.maturityDate = this.formatDateString(item.loanTib.maturityDate);
              }
            });
          } else {
            this.paidTotal = 0;
            this.pageSize = 10;
          }
        }
      },
      error: (): void => {
        this.loadingService.hide();
      }
    })
  }

  formatDateString(dateString: any) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    const yyyy = date.getFullYear();
    return `${mm}/${dd}/${yyyy}`;
  }

  toDetailOrEdit(title: any, action: string): void {
    this.titleDetailAndEditComponent.showDialog(this, title, action)
  }

  handleAddDelete(title: any, action: any) {
    if (action === 'add') {
      this.loanIdList.push(title)
    } else if (action === 'delete') {
      const index = this.loanIdList.findIndex(item => item.loanId === title.loanId);
      if (index !== -1) {
        this.loanIdList.splice(index, 1);
      }
      this.isAdding = true;
    }
  }

  checkAddDeleteByLoanId(loanId: any) {
    return !this.loanIdList.find(item => item.loanId === loanId);
  }


  checkStatus(status: any) {
    if (status == 'HR' || status == 'PRLT') {
      return false;
    } else {
      return true;
    }
  }

  export() {
    this.loadingService.show();
    let search: any = {
      Filters: this.formatSearchData(),
      PageIndex: 0,
      PageSize: 2100000000,
      Sorting: {},
      IsPaging: true
    }
    this.titleService.exportTitleData(search).subscribe({
      next: (res: Blob): void => {
        this.loadingService.hide();
        const url = window.URL.createObjectURL(res);
        const a = document.createElement('a');
        a.setAttribute('style', 'display:none');
        document.body.appendChild(a);
        a.href = url;
        a.download = this.generateFileName();
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      },
      error: (): void => {
        this.loadingService.hide();
        let dialogData = {
          summary: 'Export data error',
          severity: SEVERITY_ERROR,
          detail: 'Failed, please check and try again!',
          isLeftButton: false,
          leftButtonLabel: "",
          isRightButton: true,
          rightButtonLabel: 'OK'
        };
        this.msgService.notification(dialogData).onClose.subscribe();
      }
    });
  }

  generateFileName(): string {
    const timestamp = moment(new Date()).format('MMDDyyyyHHmmss');
    return `Title_${timestamp}.xls`;
  }


  showCheckin(title: any) {
    this.checkIn.showDialog(this, title);
  }

  refreshData() {
    this.searchData();
  }

  reset(): void {
    this.formData = {};
  }

  moment(date: any, format: string): string | null {
    return date ? this.datePipe.transform(date, format) : null;
  }

  remove(titleList: any) {
    this.loanIdList = titleList
  }

  handleAction(event:any,first:any,pageSize:any) {
    switch(event.action) {
      case 'remove':
        this.remove(event.title);
        break;
      case 'refreshData':
        this.loanIdList = []
        this.searchData(first,pageSize);
        break;
    }
  }


  formatSearchData(): any {
    const cloneFromData: any = _.cloneDeep(this.formData);
    if (!this.isEmpty(cloneFromData['releasedDateFrom'])) {
      cloneFromData['releasedDateFrom'] = this.moment(cloneFromData['releasedDateFrom'], 'MM/dd/yyyy');
    }
    if (!this.isEmpty(cloneFromData['releasedDateTo'])) {
      cloneFromData['releasedDateTo'] = this.moment(cloneFromData['releasedDateTo'], 'MM/dd/yyyy');
    }
    if (!this.isEmpty(cloneFromData['receivedDateFrom'])) {
      cloneFromData['receivedDateFrom'] = this.moment(cloneFromData['receivedDateFrom'], 'MM/dd/yyyy');
    }
    if (!this.isEmpty(cloneFromData['receivedDateTo'])) {
      cloneFromData['receivedDateTo'] = this.moment(cloneFromData['receivedDateTo'], 'MM/dd/yyyy');
    }
    cloneFromData['userId'] = JSON.parse(this.appStorageService.getStoredSessionStorageByKey('userId'));
    cloneFromData['LoanId'] = cloneFromData['LoanId'] == '' ? null : cloneFromData['LoanId'];
    if (cloneFromData['loanStatus'] && Array.isArray(cloneFromData['loanStatus'])) {
      cloneFromData['loanStatus'] = cloneFromData['loanStatus'].join('/');
    }
    if (cloneFromData['titleStatus'] && Array.isArray(cloneFromData['titleStatus'])) {
      cloneFromData['titleStatus'] = cloneFromData['titleStatus'].join('/');
    }
    if (cloneFromData['dealerStatus'] && Array.isArray(cloneFromData['dealerStatus'])) {
      cloneFromData['dealerStatus'] = cloneFromData['dealerStatus'].join('/');
    }
    if (cloneFromData['titleReleaseType'] && Array.isArray(cloneFromData['titleReleaseType'])) {
      cloneFromData['titleReleaseType'] = cloneFromData['titleReleaseType'].join('/');
    }

    return cloneFromData;
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchData();
    }
  }

  onInput(event: any): void {
    const input = event.target as HTMLInputElement;

    input.value = input.value.replace(/[^0-9]/g, '');

    if (input.value.length > 10) {
      input.value = input.value.slice(0, 10);
    }

    this.formData['LoanId'] = input.value;
  }

  isEmpty(value: any): boolean {
    return value === '' || value === null || value === undefined;
  }

  getButtonPermission(): void {
    this.permissionService.obtainPermission("app.titleMaintenance").subscribe({
      next: (e: any): void => {
        if (e.data) {
          this.permissionList = e.data;
        }
      }
    });
  }

  isBtnVisible(permission: string): boolean {
    return this.permissionService.hasPermission(this.permissionList, permission);
  }

}
